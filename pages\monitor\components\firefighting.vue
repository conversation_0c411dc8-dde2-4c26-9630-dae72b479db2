<template>
  <view class="data-time">
    <view>{{ $t('上报时间') }}</view>
    <view>{{ stsIo[0]?.sdt }}({{ baseInfo.timeZone }})</view>
  </view>
  <view class="device-info">
    <u-collapse :value="stsIoValue">
      <u-collapse-item v-for="(ioItem, index) in stsIo" :key="ioItem.dc" :name="ioItem.dc">
        <template #title>
          <view class="info-ti">{{ ioItem.name }}</view>
        </template>
        <template #right-icon>
          <view><u-icon name="play-right-fill" size="12px"></u-icon></view>
        </template>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('通讯状态') }}</view>
          <view v-if="ioItem.onLineState">{{ $t(ioItem.onLineState) }}
          </view>
          <view v-else>--</view>
        </view>
        <block v-for="i in 200">
          <view class="info-item flex justify-content align-items"
            v-if="i <= 100 && getPointInfo(ioItem, 'ff_21001_21100', i).alias != $t('未知别名')" :key="i + '21001_21100'">
            <view class="color-grey">{{ getPointInfo(ioItem, 'ff_21001_21100', i).alias }}</view>
            <view v-if="getPointInfo(ioItem, 'ff_21001_21100', i).value">
              {{ getPointInfo(ioItem, 'ff_21001_21100', i).value == 1 ? $t('闭合'): $t('断开') }}
            </view>
            <view v-else>--</view>
          </view>
          <view class="info-item flex justify-content align-items"
            v-if="i > 100 && getPointInfo(ioItem, 'ff_21101_21200', i).alias != $t('未知别名')" :key="i + '21101_21200'">
            <view class="color-grey">{{ getPointInfo(ioItem, 'ff_21101_21200', i).alias }}</view>
            <view v-if="getPointInfo(ioItem, 'ff_21101_21200', i).value">
              {{ getPointInfo(ioItem, 'ff_21101_21200', i).value == 1 ? $t('闭合'): $t('断开') }}
            </view>
            <view v-else>--</view>
          </view>
        </block>
      </u-collapse-item>
    </u-collapse>
  </view>
</template>

<script setup>
  import {
    ref,
    computed,
    getCurrentInstance
  } from 'vue'
  import {
    useMonitorStore
  } from '@/store/monitor.js'
  import {
    useLoginStore
  } from '@/store/login.js'
  import {
    get4058
  } from '@/common/parseBinaryToText.js'
  import otherColor from '../../../common/other.module.scss'
  import {
    isGroupFn
  } from '@/hook/useDeviceType.js'
  import {
    onShow
  } from '@dcloudio/uni-app'

  const {
    proxy
  } = getCurrentInstance()
  const monitorStore = useMonitorStore()
  const userStore = useLoginStore()
  const baseInfo = computed(() => monitorStore.baseInfo)

  const isGroup = computed(() => isGroupFn(monitorStore.routeQuery.type))

  const aliasArr = ref([])
  const getAliasArrFn = () => {
    aliasArr.value = []
    let groupId = monitorStore.routeQuery.groupId
    if (isGroup.value) {
      groupId.forEach(async (item) => {
        const data = await monitorStore.bindAliasQueryFn({
          ac: item,
          enable: 1,
          type: 0,
          mode: 1
        })
        aliasArr.value = [...aliasArr.value, ...data]
      })
    } else {
      monitorStore.bindAliasQueryFn({
        ac: monitorStore.routeQuery.id,
        enable: 1,
        type: 0,
        mode: 1
      }).then(data => {
        aliasArr.value = [...aliasArr.value, ...data]
      })
    }
  }
  getAliasArrFn()

  const stsIoValue = ref([])
  const stsIo = computed(() => {
    let data = monitorStore.pcs_stsIo
    stsIoValue.value = data.map(item => item.dc)
    data.forEach(item => {
      item.name = isGroup.value ? `${item.label}_${uni.$t('消防')}` : uni.$t('消防')
      for (let i = 1; i < 101; i++) {
        let key = `21${String(i).padStart(3, '0')}`
        let value = aliasArr.value.find(item => item.point == key)
        if (value) {
          item.ff_21001_21100[`ff_${key}_alias`] = value.alias
        } else {
          item.ff_21001_21100[`ff_${key}_alias`] = uni.$t('未知别名')
        }
      }
      for (let i = 101; i < 201; i++) {
        let key = `21${String(i).padStart(3, '0')}`
        let value = aliasArr.value.find(item => item.point == key)
        if (value) {
          item.ff_21101_21200[`ff_${key}_alias`] = value.alias
        } else {
          item.ff_21101_21200[`ff_${key}_alias`] = uni.$t('未知别名')
        }
      }
    })
    return data
  })

  const getPointInfo = computed(() => {
    return (item, type, index) => {
      let key = `21${String(index).padStart(3, '0')}`
      return {
        alias: item[type][`ff_${key}_alias`],
        value: item[type][`ff_${key}`]
      }
    }
  })
</script>

<style scoped lang="scss">
  .data-time {
    display: flex;
    justify-content: space-between;
    background-color: #fff;
    padding: 20rpx 30rpx;
    margin-bottom: 20rpx;
    margin: 20rpx 30rpx;
    font-size: 10px;
    border-radius: 6px;
  }

  .device-info {
    /* background-color: $uni-bg-color; */
    background-color: #fff;
    border-radius: $uni-border-radius-lg;
    padding: 0 30rpx;
    margin: 20rpx 30rpx;

    .info-ti {
      font-weight: bold;
    }

    .info-item {
      padding: 30rpx 0;
      border-bottom: 1px solid $uni-bg-color-grey;

      .item-unit {
        font-size: 12px;
        color: $uni-text-color-grey;
        margin-left: 4rpx;
      }

      view:nth-child(2n) {
        width: 50%;
        text-align: right;
      }
    }

    .info-item:last-child {
      border-bottom: none;
    }
  }

  :deep(.u-collapse-item__content__text) {
    padding: 0;
    color: #000;
  }

  :deep(.u-cell__body) {
    padding: 30rpx 0;
  }

  :deep(.u-cell--clickable) {
    background-color: $uni-bg-color;
  }
</style>