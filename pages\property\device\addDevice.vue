<template>
  <view class="add-box">
    <u-navbar leftText="" :title="title" bgColor="#fff" leftIconSize="20px" :autoBack="true" :placeholder="true">
    </u-navbar>
    <u-form labelPosition="left" :model="form" :rules="rules" ref="formRef" labelWidth="auto">
      <view class="add-item u-p-b-10">
        <u-form-item :label="$t('设备名称')" prop="deviceName" required :borderBottom="true">
          <u-input v-model="form.deviceName" :placeholder="$t('请输入设备名称')" border="none"></u-input>
        </u-form-item>
        <u-form-item :label="$t('整机序列号')" prop="deviceSerialNumber" required :borderBottom="true">
          <u-input v-model="form.deviceSerialNumber" :placeholder="$t('请输入序列号')" border="none"></u-input>
        </u-form-item>
        <u-form-item :label="$t('设备型号')" prop="deviceModel" required :borderBottom="true">
          <u-input v-model="form.deviceModel" :placeholder="$t('请输入设备型号')" border="none"></u-input>
        </u-form-item>
        <u-form-item :label="$t('设备类型')" prop="deviceType" required :borderBottom="true"
          @click="handleCellClick('single')">
          <view class="u-line-1" style="width: 60%;text-align: right;">
            {{ type }}
          </view>
        </u-form-item>
        <u-form-item prop="showDiesel" :label="$t('是否显示柴油机')" required :borderBottom="true" v-if="form.deviceType == 9">
          <u-radio-group v-model="form.showDiesel" placement="row" size="16px"
            style="display: flex;justify-content: flex-end;">
            <u-radio :label="$t('是')" :name="1" class="mr-20"></u-radio>
            <u-radio :label="$t('否')" :name="0"></u-radio>
          </u-radio-group>
        </u-form-item>
        <u-form-item :label="$t('光伏装机容量')" prop="photovoltaicInstalledCapacity" required :borderBottom="true"
          v-if="form.deviceType == 1 || form.deviceType == 2 || form.deviceType == 4 || form.deviceType == 5 || form.deviceType == 6 || form.deviceType == 11 || form.deviceType == 12 || isGroup">
          <u-input v-model="form.photovoltaicInstalledCapacity" :placeholder="$t('请输入装机容量')" border="none"></u-input>
        </u-form-item>
        <u-form-item :label="$t('电池容量')" prop="deviceBatteryCapacity" required :borderBottom="true"
          v-if="form.deviceType == 2 || form.deviceType == 5 || form.deviceType == 8 || form.deviceType == 10 || form.deviceType == 11 || form.deviceType == 12 || isGroup">
          <u-input v-model="form.deviceBatteryCapacity" :placeholder="$t('请输入电池容量')" border="none"></u-input>
        </u-form-item>
        <u-form-item :label="$t('额定功率')" prop="deviceRatedPower" required>
          <u-input v-model="form.deviceRatedPower" :placeholder="$t('请输入额定功率')" border="none"></u-input>
        </u-form-item>
      </view>
      <template v-if="isGroup">
        <view class="flex u-flex-between u-flex-items-center u-m-t-20 u-m-b-20 u-m-l-10 u-m-r-10">
          <view style="font-size: 14px;font-weight: bold;">{{ $t('组合设备信息') }}</view>
          <view class="flex">
            <u-button type="primary" :plain="true" :text="$t('添加')" shape="circle" class="u-m-r-20 btn"
              @click="handleAddDeviceClick"></u-button>
            <u-button type="error" :plain="true" :text="$t('删除')" shape="circle" class="btn"
              @click="handleDeleteDeviceClick"></u-button>
          </view>
        </view>
        <view class="add-item mb-20" v-for="(item, index) in form.combinationList" :key="index + 'combination'">
          <u-form-item :label="$t('序列号')" :prop="`combinationList.${index}.ac`" required :borderBottom="true">
            <u-input v-model="item.ac" :placeholder="$t('请输入序列号')" border="none"></u-input>
          </u-form-item>
          <u-form-item :label="$t('设备类型')" :prop="`combinationList.${index}.type`" required :borderBottom="true"
            @click="handleCellClick('group', index)">
            <view class="u-line-1" style="width: 60%;text-align: right;">
              {{ item.typeLabel }}
            </view>
          </u-form-item>
        </view>
      </template>
    </u-form>
    <u-button type="primary" style="border-radius: 100rpx;" class="u-m-t-40"
      @click="handleConfirmClick">{{ $t('确认') }}</u-button>
  </view>
</template>

<script setup>
  import {
    ref,
    computed,
    nextTick,
    getCurrentInstance
  } from 'vue'
  import {
    onLoad,
    onShow
  } from '@dcloudio/uni-app'
  import {
    addDevice,
    editDevice,
    getDeviceInfo
  } from '@/api/device'
  import {
    isGroupFn,
    isPhotovoltaicFn,
    isPowerFn,
    isGirdFn,
    getDeviceType
  } from '@/hook/useDeviceType.js'

  const {
    proxy
  } = getCurrentInstance()
  const title = ref(uni.$t('添加设备'))
  const id = ref()
  const service = uni.cache.getItem('service')
  onLoad((options) => {
    if (options.id) {
      id.value = options.id
      title.value = uni.$t('修改设备')
    } else {
      title.value = uni.$t('添加设备')
    }
    if (title.value == uni.$t('修改设备')) getInfoFn()
    if (service !== 'zh') rules.value.deviceName.push({
      message: uni.$t('不允许有中文字符'),
      pattern: /^[^\u4e00-\u9fa5]+$/,
      trigger: ['blur', 'change'],
    })
  })

  const getInfoFn = async () => {
    const res = await getDeviceInfo({
      deviceId: id.value
    })
    let acs = res.data.combinationDeviceSerialNumber.split(',')
    let types = res.data.combinationDeviceType.split(',')
    let combinationList = []
    acs.forEach((ac, index) => {
      combinationList.push({
        ac,
        type: types[index],
        typeLabel: getDeviceType(types[index], false, false)
      })
    })
    form.value = {
      ...res.data,
      combinationList
    }
  }

  const formRef = ref(null)
  const form = ref({
    deviceSerialNumber: '', //整机序列号
    deviceModel: '', //设备型号
    deviceBatteryCapacity: '', //电池容量
    deviceType: 1,
    deviceName: '', // 设备名称
    deviceRatedPower: '', // 额定功率
    photovoltaicInstalledCapacity: '', // 光伏装机容量
    combinationDeviceSerialNumber: [],
    combinationDeviceType: [],
    combinationList: [{
      ac: '',
      type: 1,
      typeLabel: '光储系统（并离网）'
    }],
    showDiesel: 0
  })
  const rules = ref({
    'deviceSerialNumber': {
      type: 'string',
      required: true,
      message: uni.$t('请输入序列号'),
      trigger: ['blur', 'change'],
    },
    'deviceModel': {
      type: 'string',
      required: true,
      message: uni.$t('请输入设备型号'),
      trigger: ['blur', 'change'],
    },
    'deviceBatteryCapacity': {
      type: 'string',
      required: true,
      message: uni.$t('请输入电池容量'),
      trigger: ['blur', 'change'],
    },
    'deviceType': {
      type: 'number',
      required: true,
      message: uni.$t('请选择'),
      trigger: ['blur', 'change'],
    },
    'deviceName': [{
      type: 'string',
      required: true,
      message: uni.$t('请输入设备名称'),
      trigger: ['blur', 'change'],
    }],
    'deviceRatedPower': {
      type: 'string',
      required: true,
      message: uni.$t('请输入额定功率'),
      trigger: ['blur', 'change'],
    },
    'photovoltaicInstalledCapacity': {
      type: 'string',
      required: true,
      message: uni.$t('请输入光伏装机容量'),
      trigger: ['blur', 'change'],
    },
  })

  const handleConfirmClick = () => {
    if (formRef.value) {
      formRef.value.validate().then(valid => {
        if (valid) {
          let data = {
            ...form.value,
            combinationDeviceSerialNumber: isGroup.value ? form.value.combinationList.map(item => item.ac).join(','): '',
            combinationDeviceType: isGroup.value ? form.value.combinationList.map(item => item.type).join(','): '',
          }
          if (title.value == uni.$t('添加设备')) {
            addDevice(data).then(res => {
              uni.$u.toast(uni.$t('添加成功'))
              uni.navigateBack()
            }).catch(() => {
              uni.$u.toast(uni.$t('添加失败'))
            })
          } else {
            editDevice(data).then(res => {
              uni.$u.toast(uni.$t('修改成功'))
              uni.navigateBack()
            }).catch(() => {
              uni.$u.toast(uni.$t('修改失败'))
            })
          }
        }
      })
    }
  }
  const isGroup = computed(() => {
    return isGroupFn(form.value.deviceType)
  })
  /**
   * 类型
   */
  const type = ref(uni.$t('光储系统（并离网）'))
  const selectType = ref()
  const selectIndex = ref()
  const handleCellClick = (type, index) => {
    selectType.value = type
    selectIndex.value = index
    if (type == 'single') {
      uni.navigateTo({
        url: `/pages/property/device/typeList?value=${form.value.deviceType}&type=${type}`
      })
    } else {
      let deviceType = form.value.combinationList[index].type
      uni.navigateTo({
        url: `/pages/property/device/typeList?value=${deviceType}&type=${type}`
      })
    }
  }
  uni.$on('typeItemClick', (data) => {
    if (!data) return
    if (selectType.value == 'single') {
      form.value.deviceType = data.value
      type.value = data.label
    } else if (selectType.value == 'group') {
      form.value.combinationList[selectIndex.value].type = data.value
      form.value.combinationList[selectIndex.value].typeLabel = data.label
    }
  })
  const handleAddDeviceClick = () => {
    form.value.combinationList.push({
      ac: '',
      type: 1,
      typeLabel: uni.$t('光储系统（并离网）')
    })
  }
  const handleDeleteDeviceClick = () => {
    if (form.value.combinationList.length == 1) return uni.$u.toast(uni.$t('至少要有一条哦'))
    form.value.combinationList.pop()
  }
</script>

<style lang="scss" scoped>
  .add-box {
    width: 100%;
    height: 100vh;
    padding: 20rpx 30rpx;
    overflow: auto;
  }

  .add-item {
    background-color: #fff;
    padding: 0 30rpx;
    border-radius: 6px;
  }

  .btn {
    height: 50rpx;
    background: rgba(0, 0, 0, 0);
  }

  :deep(.u-input__content__field-wrapper__field) {
    text-align: right !important;
  }

  :deep(.u-button__text) {
    font-size: 12px !important;
  }

  :deep(.u-datetime-picker) {
    .u-input {
      padding: 0 !important;
      border: none;
    }
  }

  :deep(.rate) {
    .u-form-item__body__right__content__slot {
      flex-direction: row-reverse !important;
    }
  }

  :deep(.u-form-item__body__right__content__slot) {
    flex-direction: row-reverse;
  }
</style>