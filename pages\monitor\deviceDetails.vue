<template>
  <view class="device-details">
    <z-paging ref="paging" refresher-only @onRefresh="onRefresh" :refresher-enabled="currentName != $t('告警')">
      <template #top>
      <u-navbar :title="routeQuery.name" leftIconSize="30" :autoBack="true" :placeholder="true" class="tabbar">
        <template #center>
          <view class="flex align-items">
            <template v-if="isGroup">
              <view class="device-state-on" v-if="groupControl.length && groupControl[0]['onLineState'] == '在线'"></view>
              <view class="device-state-off" v-if="groupControl.length && groupControl[0]['onLineState'] == '离线'"></view>
            </template>
            <template v-else>
              <view class="device-state-on" v-if="control['onLineState'] == '在线'"></view>
              <view class="device-state-off" v-if="control['onLineState'] == '离线'"></view>
            </template>
            <span class="bold">{{ routeQuery.name }}</span>
          </view>
        </template>
      </u-navbar>
      <u-tabs :list="tabListCom" :scrollable="isScrollable" :current="current" @click="handleTabClick" lineHeight="4px" :itemStyle="{
                lineHeight: '44px'
              }" :activeStyle="{
            color: otherColor.mainColor,
        }"
        :inactiveStyle="{
            color: otherColor.mainColor,
        }" :lineColor="otherColor.primaryColor" style="background-color: #fff;"></u-tabs>
    <!-- </u-sticky> -->
    </template>
    <view>
      <DetailsData v-if="currentName == $t('概括')" :key="detailsKey" ref="detailDataRef" />
      <DetailsInfo v-else-if="currentName == $t('设备信息') && !isGroup" />
      <DetailsGroupInfo v-else-if="currentName == $t('设备信息') && isGroup" />
      <DetailsPcs v-else-if="currentName == 'PCS'" />
      <DetailsAlarm v-else-if="currentName == $t('告警')" :key="alarmKey" />
      <DetailsParam v-else-if="currentName == $t('参数')" :key="paramKey" />
      <DetailsBms type="bms" v-else-if="currentName == 'BMS'" />
      <DetailsBms type="bmsBau" v-else-if="currentName == 'BMS-BAU'" />
      <DetailsCell v-else-if="currentName == 'CELL'" :key="cellKey" />
      <DetailsAmmeter v-else-if="currentName == $t('电表')" />
      <DetailsPeripherals v-else-if="currentName == $t('外设')" :key="ioKey" />
      <DetailsFirefighting v-else-if="currentName == $t('消防')" :key="stsIoKey" />
      <DetailsChargingPile v-else-if="currentName == $t('充电桩')" />
    </view>
    </z-paging>
  </view>
</template>

<script setup>
  import {
    computed,
    ref,
    toRefs,
    defineAsyncComponent,
    getCurrentInstance,
    watch
  } from 'vue'
  import {
    $uGetRect,
    checkRole,
    isShowPerm
  } from '@/common/utils.js'
  import {
    onLoad,
    onReady,
    onResize,
    onUnload,
    onShow
  } from '@dcloudio/uni-app'
  import {
    useMonitorStore
  } from '@/store/monitor.js'
  import {
    useParamStore
  } from '@/store/param.js'
  import {
    storeToRefs
  } from 'pinia'
  import otherColor from '../../common/other.module.scss'
  import { isGroupFn } from '@/hook/useDeviceType.js'
  import { isEmpty } from 'lodash'

  import DetailsData from './components/data.vue'
  import DetailsInfo from './components/info.vue'
  import DetailsGroupInfo from './components/info-group.vue'
  import DetailsPcs from './components/pcs.vue'
  import DetailsAlarm from './components/alarm.vue'
  import DetailsParam from './components/param.vue'
  import DetailsBms from './components/bms.vue'
  import DetailsAmmeter from './components/ammeter.vue'
  import DetailsPeripherals from './components/peripherals.vue'
  import DetailsChargingPile from './components/chargingPile.vue'
  import DetailsCell from './components/cell.vue'
  import DetailsFirefighting from './components/firefighting.vue'
  
  const { proxy } = getCurrentInstance()

  /**
   * 路由参数、初始化
   */
  const tabbarHeight = ref(0)
  const isGroup = ref(false)
  onLoad((options) => {
    isGroup.value = isGroupFn(options.type)
    routeQuery.value = {
      ...options,
      groupId: options.groupId ? decodeURIComponent(options.groupId).split(','): [],
      groupType: options.groupType ? decodeURIComponent(options.groupType).split(','): []
    }
    getData()
  })
  onReady(() => {
    $uGetRect('.tabbar').then(res => {
      tabbarHeight.value = res.height
    })
  })

  const monitorStore = useMonitorStore()
  const {
    routeQuery,
    control,
    groupControl,
    orientation,
    lineGroupQueryInfo
  } = storeToRefs(monitorStore)
  const paramStore = useParamStore()

  const getData = () => {
    getTopFn()
    getControlFn()
    getPcsFn()
    getBmsFn()
    getElectricMeterFn()
    getPeripheralsFn()
    getChargingPileFn()
    getCellFn()
    getBmsBauFn()
    getStsIoFn()
  }

  const isScrollable = ref(false)
  const tabListCom = computed(() => {
    let data = [{
      name: uni.$t('概括')
    }, {
      name: uni.$t('设备信息')
    }]
    let ac = monitorStore.pcs_ac
    let dc = monitorStore.pcs_dc
    let sts = monitorStore.pcs_sts
    let bms = monitorStore.pcs_bms
    let cell = monitorStore.pcs_cell
    let ele = monitorStore.pcs_ele
    let io = monitorStore.pcs_io
    let cp = monitorStore.pcs_cp
    let bmsBau = monitorStore.pcs_bmsBau
    let stsIo = monitorStore.pcs_stsIo
    if (ac.length || dc.length || sts.length) {
      data.push({
        name: 'PCS'
      })
    }
    if (bms.length) data.push({
      name: 'BMS'
    })
    if (bmsBau.length) data.push({
      name: 'BMS-BAU'
    })
    if (cell?.findIndex(item => !isEmpty(item?.bms_7101_7612) || !isEmpty(item?.bms_7613_8124) || !isEmpty(item?.bms_8125_8637) || !isEmpty(item?.bms_8638_9149) || !isEmpty(item?.bms_9150_9661)) != -1) data.push({
      name: 'CELL'
    })
    if (ele.length) data.push({
      name: uni.$t('电表')
    })
    if (io.length) data.push({
      name: uni.$t('外设')
    })
    if (cp.length) data.push({
      name: uni.$t('充电桩')
    })
    if (stsIo.length) data.push({
      name: uni.$t('消防')
    })
    data.push({
      name: uni.$t('告警')
    })
    if (isShowPerm(['system:sendMqtt:argumentsJsonMAC']) || isShowPerm(['system:sendMqtt:argumentsJsonMDC']) || isShowPerm(['system:sendMqtt:argumentsJsonBattery']) || isShowPerm(['system:sendMqtt:argumentsJsonPolicy']) || isShowPerm(['system:sendMqtt:upgradeJson'])) {
      data.push({
        name: uni.$t('参数')
      })
    }
    isScrollable.value = data.length > 5 ? true: false
    return data
  })
  const current = ref(0)
  const currentName = ref(uni.$t('概括'))
  const handleTabClick = (item) => {
    current.value = item.index
    currentName.value = item.name
    uni.cache.setItem('monitorCurrentName', item.name)
  }
  
  const detailsKey = ref(1)
  const ioKey = ref(1)
  const cellKey = ref(1)
  const alarmKey = ref(1)
  const paramKey = ref(1)
  const stsIoKey = ref(1)
  const onRefresh = async () => {
    if (currentName.value == uni.$t('概括')) {
      detailsKey.value++
    } else if (currentName.value == uni.$t('设备信息')) {
      if (isGroup.value) getBmsFn()
      getTopFn()
      await getControlFn()
    } else if (currentName.value == 'PCS') {
      getPcsFn()
    } else if (currentName.value == 'BMS') {
      getBmsFn()
    } else if (currentName.value == uni.$t('电表')) {
      getElectricMeterFn()
    } else if (currentName.value == uni.$t('外设')) {
      ioKey.value++
    } else if (currentName.value == uni.$t('充电桩')) {
      getChargingPileFn()
    } else if (currentName.value == 'BMS-BAU') {
      getBmsBauFn()
    } else if (currentName.value == 'CELL') {
      await getCellFn()
      cellKey.value++
    } else if (currentName.value == uni.$t('告警')) {
      // alarmKey.value++
      proxy.$refs.paging.endRefresh ()
    } else if (currentName.value == uni.$t('参数')) {
      paramKey.value++
    } else if (currentName.value == uni.$t('消防')) {
      stsIoKey.value++
    }
    proxy.$refs.paging.complete()
  }

  const getTopFn = async () => {
    await monitorStore.deviceMonitoringDetailTopFn({
      deviceSerialNumber: routeQuery.value.id,
      deviceType: routeQuery.value.type
    })
  }
  const getControlFn = async () => {
    await monitorStore.deviceMonitoringDetailRightFn({
      deviceSerialNumber: routeQuery.value.id,
      type: 'control',
      deviceType: routeQuery.value.type
    })
  }
  const getPcsFn = async () => {
    await monitorStore.deviceMonitoringDetailRightFn({
      deviceSerialNumber: routeQuery.value.id,
      type: 'pcs',
      deviceType: routeQuery.value.type,
      groupId: routeQuery.value.groupId.length ? routeQuery.value.groupId: undefined
    })
  }
  const getBmsFn = async () => {
    await monitorStore.deviceMonitoringDetailRightFn({
      deviceSerialNumber: routeQuery.value.id,
      type: 'bms',
      deviceType: routeQuery.value.type,
      groupId: routeQuery.value.groupId.length ? routeQuery.value.groupId: undefined
    })
  }
  const getElectricMeterFn = async () => {
    await monitorStore.deviceMonitoringDetailRightFn({ // 电表
      deviceSerialNumber: routeQuery.value.id,
      type: 'electricMeter',
      deviceType: routeQuery.value.type,
      groupId: routeQuery.value.groupId.length ? routeQuery.value.groupId: undefined
    })
  }
  const getPeripheralsFn = async () => {
    await monitorStore.deviceMonitoringDetailRightFn({ // 外设
      deviceSerialNumber: routeQuery.value.id,
      type: 'peripherals',
      deviceType: routeQuery.value.type,
      groupId: routeQuery.value.groupId.length ? routeQuery.value.groupId: undefined
    })
  }
  const getChargingPileFn = async () => {
    await monitorStore.deviceMonitoringDetailRightFn({ // 充电桩
      deviceSerialNumber: routeQuery.value.id,
      type: 'chargingPile',
      deviceType: routeQuery.value.type,
      groupId: routeQuery.value.groupId.length ? routeQuery.value.groupId: undefined
    })
  }
  const getCellFn = async () => {
    await monitorStore.deviceMonitoringDetailRightFn({ // 电芯
      deviceSerialNumber: routeQuery.value.id,
      type: 'BMSCell',
      deviceType: routeQuery.value.type,
      groupId: routeQuery.value.groupId.length ? routeQuery.value.groupId: undefined
    })
  }
  const getBmsBauFn = async () => {
    await monitorStore.deviceMonitoringDetailRightFn({ // BMA-BAU
      deviceSerialNumber: routeQuery.value.id,
      type: 'bms-bau',
      deviceType: routeQuery.value.type,
      groupId: routeQuery.value.groupId.length ? routeQuery.value.groupId: undefined
    })
  }
  const getStsIoFn = async () => {
    await monitorStore.deviceMonitoringDetailRightFn({ // STS-IO
      deviceSerialNumber: routeQuery.value.id,
      type: 'Firefighting',
      deviceType: routeQuery.value.type,
      groupId: routeQuery.value.groupId.length ? routeQuery.value.groupId: undefined
    })
  }
</script>

<style scoped lang="scss">
  .device-details {
    width: 100%;
    height: 100vh;
    overflow: hidden;
  }

  :deep(.u-tabs__wrapper__nav__item) {
    padding: 0 40rpx;
  }
  :deep(.u-tabs__wrapper__nav__item__text) {
    font-size: 14px;
  }
  :deep(.u-tabs__wrapper__nav__item) {
    // padding: 0;
  }

  .device-state-on {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: $device-state-on;
    margin-right: 5px;
  }

  .device-state-off {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: $device-state-off;
    margin-right: 5px;
  }
</style>