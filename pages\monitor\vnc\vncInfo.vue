<template>
  <view class="add-box">
    <u-form labelPosition="left" :model="form" ref="formRef" labelWidth="auto" disabled>
      <view class="add-item">
        <u-form-item :label="$t('开启密码')" prop="pwd" :borderBottom="true">
          <u-input v-model="form.pwd" :placeholder="$t('请输入')" border="none" />
        </u-form-item>
        <u-form-item :label="$t('VNC模式')" prop="vncMode" :borderBottom="true">
          <view class="u-line-1" style="width: 60%;text-align: right;">
            {{ vncModeText }}
          </view>
        </u-form-item>
        <u-form-item :label="$t('VNC端口')" prop="vncPortId" :borderBottom="true"
          v-if="form.vncMode == 5 || form.vncMode == 3">
          <view class="u-line-1" style="width: 60%;text-align: right;">
            {{ vncPortText }}
          </view>
        </u-form-item>
        <u-form-item :label="$t('SSH端口')" prop="sshPortId" :borderBottom="true"
          v-if="form.vncMode == 1 || form.vncMode == 3">
          <view class="u-line-1" style="width: 60%;text-align: right;">
            {{ sshPortText }}
          </view>
        </u-form-item>
      </view>
    </u-form>
  </view>
</template>

<script setup>
  import {
    ref,
    computed
  } from 'vue'
  import {
    onLoad,
    onShow,
    onUnload
  } from '@dcloudio/uni-app'
  import {
    listAll,
    argumentsJsonFrpOpen
  } from '@/api/monitor'
  import {
    checkRole
  } from '@/common/utils.js'

  onLoad((options) => {
    form.value = {
      ...form.value,
      ...JSON.parse(decodeURIComponent(options.item))
    }
  })

  const formRef = ref(null)
  const form = ref({
    ac: '',
    id: '',
    pwd: '',
    sshPortId: '',
    vncMode: 5,
    vncPortId: ''
  })

  const vncModeText = ref(uni.$t('请选择'))
  const vncPortText = ref(uni.$t('请选择'))
  const sshPortText = ref(uni.$t('请选择'))
  /**
   * 选择
   */
  const modeOptions = ref([])
  onShow(() => {
    getPortFn()
    if (checkRole(['admin'])) {
      modeOptions.value = [{
          text: uni.$t('开启VNC'),
          value: 5
        },
        {
          text: uni.$t('开启SSH'),
          value: 1
        },
        {
          text: uni.$t('开启SSH+VNC'),
          value: 3
        },
        {
          text: uni.$t('开启内网VNC'),
          value: 7
        }
      ]
    } else {
      modeOptions.value = [{
          text: uni.$t('开启VNC'),
          value: 5
        },
        {
          text: uni.$t('开启内网VNC'),
          value: 7
        }
      ]
    }
    
    vncModeText.value = modeOptions.value.find(item => item.value == form.value.vncMode).text
  })
  
  // 获取端口
  const portOptions = ref([])
  const getPortFn = () => {
    listAll().then(res => {
      if (res.code !== 200) return uni.$u.toast(res.msg)
      let data = res.data.map(item => {
        return {
          id: item.id,
          port: item.port,
          disabled: item.status == 0,
          type: item.type
        }
      })
      portOptions.value = data
      vncPortText.value = portOptions.value.find((item) => item.id == form.value.vncPortId).port
      sshPortText.value = portOptions.value.find((item) => item.id == form.value.sshPortId).port
    })
  }
</script>

<style lang="scss" scoped>
  .add-box {
    width: 100%;
    height: 100vh;
    padding: 20rpx 30rpx;
    overflow: auto;
  }

  .add-item {
    background-color: #fff;
    padding: 0 30rpx;
    border-radius: 6px;
  }

  :deep(.u-input__content__field-wrapper__field) {
    text-align: right !important;
  }

  :deep(.u-form-item__body__right__content__slot) {
    flex-direction: row-reverse;
  }
</style>