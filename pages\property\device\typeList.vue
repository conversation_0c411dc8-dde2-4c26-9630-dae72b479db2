<template>
  <view class="scheme-list">
    <u-navbar :title="navBarTitle" leftIconSize="30" :autoBack="true" :placeholder="true" class="tabbar" :titleStyle="{
    		        color: '#000'
    		      }">
      <template #right>
        <u-button type="primary" size="mini" style="height: 50rpx;" @click="handleCconfirmClick">{{ $t('完成') }}</u-button>
      </template>
    </u-navbar>

    <view class="scheme-list-wrap u-p-t-10 u-p-b-10 u-m-b-20">
      <u-search v-model="searchValue" :clearabled="true" searchIconSize="30rpx" height="60rpx" :placeholder="$t('请输入关键字')"
        @search="handleSearchClick" @custom="handleSearchClick" :actionText="$t('搜索')"></u-search>
    </view>

    <view class="scheme-list-wrap">
      <view class="scheme-list-wrap-item flex" v-for="item in deviceTypeOptions" :key="item.value"
        @click="handleItemClick(item)">
        <view class="u-line-1 scheme-list-wrap-item-left" :class="{ 'actived': item.value == initData.value }">
          {{ item.label }}
        </view>
        <view v-if="item.value == initData.value">
          <u-icon name="checkbox-mark" color="#3c9cff"></u-icon>
        </view>
      </view>
    </view>
    
    <view v-if="!deviceTypeOptions.length" style="height: calc(100% - 140rpx - 44px);background: #fff;padding-top: 300rpx;border-radius: 6px;">
      <u-empty icon="../../../static/empty.webp" width="300" height="300" textSize="14px" :text="$t('暂无数据')">
      </u-empty>
    </view>
    
  </view>
</template>

<script setup>
  import {
    ref,
    toRefs,
    computed
  } from 'vue'
  import {
    onLoad,
    onShow,
    onUnload
  } from '@dcloudio/uni-app'
  import {
    deviceTypeSingleOptions,
    deviceTypeGroupOptions
  } from '@/hook/useDeviceType.js'

  const deviceTypeOptions = ref()
  const initData = ref()
  const navBarTitle = ref(uni.$t('选择类型'))
  onLoad((options) => {
    initData.value = {
      ...options
    }
    deviceTypeOptions.value = initData.value.type == 'single' ? [...deviceTypeSingleOptions, ...
      deviceTypeGroupOptions
    ] : [...deviceTypeSingleOptions]
  })

  const selectData = ref()
  const handleItemClick = (item) => {
    initData.value.value = item.value
    selectData.value = item
  }
  const handleCconfirmClick = () => {
    uni.$emit('typeItemClick', selectData.value)
    uni.navigateBack({
      delta: 1
    })
  }

  /**
   * 搜索
   */
  const searchValue = ref()
  const handleSearchClick = () => {
    if (!searchValue.value) return deviceTypeOptions.value = initData.value.type == 'single' ? [...
      deviceTypeSingleOptions, ...deviceTypeGroupOptions
    ] : [...deviceTypeSingleOptions]
    deviceTypeOptions.value = deviceTypeOptions.value.filter(item => {
      return item.label.indexOf(searchValue.value) != -1
    })
  }
</script>

<style lang="scss" scoped>
  .scheme-list {
    height: 100vh;
    overflow-y: auto;
    padding: 20rpx 30rpx;

    &-wrap {
      background-color: #fff;
      border-radius: 6px;
      padding: 0 30rpx;

      .actived {
        font-weight: bold;
        color: $u-primary;
      }

      &-item {
        padding: 30rpx 0;
        border-bottom: 1px solid $uni-bg-color-grey;
        justify-content: space-between;

        &-left {
          width: 80%;
        }
      }

      &-item:last-child {
        border-bottom: none;
      }
    }
  }
</style>