<template>
  <view>
    <view class="ques-box">
      <view class="ques-box-view" v-if="value == 1">
        <view>1、{{ $t(`如果手机内其他 APP 产品运行不卡顿，网络状况良好，请在“设置”页面点击清空缓存后，重新进行尝试。`) }}</view>
      </view>
      <view class="ques-box-view" v-if="value == 2">
        <view>1、{{ $t('为了让全球各地用户有更好的使用体验，系统划分了不同地域供用户访问，地域分为：') }}<span class="bold">{{ $t('中国站、国际站') }}</span>{{ $t('（亚洲其他国家/地区、欧洲、非洲、大洋洲、北美洲、南美洲、南极洲等）。') }}</view>
        <view class="u-m-t-20">2、<span class="bold">{{ $t('选择不同服务器的影响') }}</span></view>
        <view class="u-m-t-20">2.1、<span class="bold">{{ $t('中国站、国际站对应不同的账号体系') }}</span>，{{ $t('如果用户想要分别访问中国大陆、国际，需要用户分别开通账号并登录使用。') }}</view>
        <view class="u-m-t-20">2.1、<span class="bold">{{ $t('中国站、国际站的数据是隔离的') }}</span>，{{ $t('即访问中国大陆地域，是无法看到国际站的数据。') }}</view>
        <view class="u-m-t-20">3、<span class="bold">{{ $t('该如何选择服务器') }}</span></view>
        <view class="u-m-t-20">3.1、{{ $t('如果您的业务范围都在中国大陆，您选择中国站') }}</view>
        <view class="u-m-t-20">3.2、{{ $t('如果您的业务范围都在国际，您选择国际站') }}</view>
        <view class="u-m-t-20">3.3、{{ $t('如果您的业务范围在中国大陆、国际都存在，那您需要在中国站、国际站分别开通账号，以管理不同地域的业务') }}</view>
        <view><span class="bold">{{ $t('注') }}</span>：{{ $t('业务范围是指您管理电站的所在地范围') }}</view>
      </view>
      <view class="ques-box-view" v-if="value == 3">
        <view>1、{{ $t(`请退出登录重新登录`) }}</view>
        <view class="u-m-t-20">2、{{ $t("点击`设置`-`清空缓存`") }}</view>
        <view class="u-m-t-20">3、{{ $t("如若还不能访问，请联系管理员点击`设置`-`联系我们`") }}</view>
      </view>
    </view>
  </view>
</template>

<script setup>
  import {
    ref
  } from 'vue'
  import {
    onLoad
  } from '@dcloudio/uni-app'

  const value = ref()
  onLoad((options) => {
    value.value = options.value
  })
</script>

<style lang="scss" scoped>
  .ques-box {
    height: 100vh;
    width: 100%;
    padding: 20rpx 30rpx;

    &-view {
      background-color: #fff;
      padding: 30rpx;
      border-radius: $uni-border-radius-lg;
    }
  }
</style>