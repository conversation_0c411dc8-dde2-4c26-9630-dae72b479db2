<template>
  <view class="add-box">
    <u-navbar leftText="" :title="title" bgColor="#fff" leftIconSize="20px" :autoBack="true" :placeholder="true">
    </u-navbar>
    <u-form labelPosition="left" :model="form" :rules="rules" ref="formRef" labelWidth="auto">
      <view class="add-item">
        <u-form-item :label="$t('国家')" prop="country" :borderBottom="true" required>
          <u-input v-model="form.country" :placeholder="$t('请输入国家')" border="none" />
        </u-form-item>
        <u-form-item :label="$t('国家') + '(EN)'" prop="countryEn" :borderBottom="true" required>
          <u-input v-model="form.countryEn" :placeholder="$t('请输入')" border="none" />
        </u-form-item>
        <u-form-item :label="$t('货币单位')" prop="currency" :borderBottom="true" required>
          <u-input v-model="form.currency" :placeholder="$t('请输入货币单位')" border="none" />
        </u-form-item>
        <u-form-item :label="$t('货币单位') + '(EN)'" prop="currencyEn" :borderBottom="true" required>
          <u-input v-model="form.currencyEn" :placeholder="$t('请输入')" border="none" />
        </u-form-item>
      </view>
    </u-form>
    <u-button type="primary" style="border-radius: 100rpx;" class="u-m-t-40"
      @click="handleConfirmClick">{{ $t('确认') }}</u-button>
  </view>
</template>

<script setup>
  import {
    ref
  } from 'vue'
  import {
    onLoad,
    onShow
  } from '@dcloudio/uni-app'
  import {
    addCurrency,
    editCurrency
  } from '@/api/currency.js'

  const title = ref(uni.$t('添加货币'))
  onLoad((options) => {
    if (options) {
      form.value = {
        ...JSON.parse(decodeURIComponent(options.item))
      }
      title.value = uni.$t('修改货币')
    } else {
      title.value = uni.$t('添加货币')
    }
  })

  const formRef = ref(null)
  const form = ref({
    countryEn: '',
    country: '',
    currencyEn: '',
    currency: '',
  })
  const rules = ref({
    'country': {
      type: 'string',
      required: true,
      message: uni.$t('请输入国家'),
      trigger: ['blur', 'change'],
    },
    'countryEn': {
      type: 'string',
      required: true,
      message: uni.$t('请输入国家'),
      trigger: ['blur', 'change'],
    },
    'currency': {
      type: 'string',
      required: true,
      message: uni.$t('请输入货币单位'),
      trigger: ['blur', 'change'],
    },
    'currencyEn': {
      type: 'string',
      required: true,
      message: uni.$t('请输入货币单位'),
      trigger: ['blur', 'change'],
    }
  })

  const handleConfirmClick = () => {
    if (formRef.value) {
      formRef.value.validate().then(valid => {
        if (valid) {
          if (title.value == uni.$t('添加货币')) {
            addCurrency({
              currencyEn: form.value.currencyEn,
              currency: form.value.currency,
              countryEn: form.value.countryEn,
              country: form.value.country
            }).then(res => {
              uni.$u.toast(uni.$t('添加成功'))
              uni.navigateBack()
            }).catch(() => {
              uni.$u.toast(uni.$t('添加失败'))
            })
          } else {
            editCurrency({
              id: form.value.id,
              currencyEn: form.value.currencyEn,
              currency: form.value.currency,
              countryEn: form.value.countryEn,
              country: form.value.country
            }).then(res => {
              uni.$u.toast(uni.$t('修改成功'))
              uni.navigateBack()
            }).catch(() => {
              uni.$u.toast(uni.$t('修改失败'))
            })
          }
        }
      })
    }
  }
</script>

<style lang="scss" scoped>
  .add-box {
    width: 100%;
    height: 100vh;
    padding: 20rpx 30rpx;
    overflow: auto;
  }

  .add-item {
    background-color: #fff;
    padding: 0 30rpx;
    border-radius: 6px;
  }

  .btn {
    height: 50rpx;
    background: rgba(0, 0, 0, 0);
  }

  :deep(.u-input__content__field-wrapper__field) {
    text-align: right !important;
  }

  :deep(.u-button__text) {
    font-size: 12px !important;
  }

  :deep(.u-datetime-picker) {
    .u-input {
      padding: 0 !important;
      border: none;
    }
  }

  :deep(.rate) {
    .u-form-item__body__right__content__slot {
      flex-direction: row-reverse !important;
    }
  }
</style>