<template>
  <view style="height: 100vh;padding: 20rpx 30rpx;overflow-y: auto;">
    <view class="param-form">
      <u-form labelPosition="left" labelWidth="auto" :model="form" :rules="rules" ref="formRef" :labelStyle="paramLabelStyle" errorType="toast">
        <u-form-item prop="status" :label="$t('下发状态')">
          <view class="send-status color-grey" v-if="form.status == 0">{{ $t('未下发') }}</view>
          <view class="send-status" v-if="form.status == 1">
            <image src="../../../static/green.png" mode="" style="width: 30rpx;height: 30rpx;"></image>
            <view>{{ $t('下发成功') }}</view>
          </view>
          <view class="send-status" v-if="form.status == 2">
            <image src="../../../static/yellow.png" mode="" style="width: 35rpx;height: 35rpx;"></image>
            <view>{{ $t('下发中') }}</view>
          </view>
          <view class="send-status" v-if="form.status == 3">
            <image src="../../../static/red.png" mode="" style="width: 35rpx;height: 35rpx;"></image>
            <view>{{ $t('下发失败') }}</view>
          </view>
        </u-form-item>
        <u-form-item prop="setting1920" :label="$t('直流源电压设置')" required>
          <u-number-box v-model="form.setting1920" :min="50" :max="1000" decimal-length="1" v-bind="inputNumberStyle"></u-number-box>
        </u-form-item>
        <u-form-item prop="setting1921" :label="$t('电池恒流设置')" required>
          <u-number-box v-model="form.setting1921" :min="-2000" :max="2000" decimal-length="1" v-bind="inputNumberStyle"></u-number-box>
        </u-form-item>
        <u-form-item prop="setting1922" :label="$t('电池恒功率设置')" required>
          <u-number-box v-model="form.setting1922" :step="0.1" :min="-100" :max="100" decimal-length="1" v-bind="inputNumberStyle"></u-number-box>
        </u-form-item>
        <u-form-item prop="setting1923" :label="$t('光伏限功率设置')" required>
          <u-number-box v-model="form.setting1923" :step="0.1" :min="0" :max="100" decimal-length="1" v-bind="inputNumberStyle"></u-number-box>
        </u-form-item>
      </u-form>
    </view>
    <view
      style="margin-top: 20rpx;font-size: 12px;padding: 20rpx 30rpx;background-color: #fff;border-radius: 6px;color: #b7b7b7;">
      <view style="margin-bottom: 10rpx;">
        <view style="color: #333;margin-bottom: 10rpx;">
          {{ $t('下发状态') }}：
        </view>
        <view>{{ $t('未下发') }}：{{ $t('该类参数从未下发') }}</view>
        <view>{{ $t('下发中') }}：{{ $t('参数已成功下发至设备，执行未知，请等待') }}</view>
        <view>{{ $t('下发成功') }}：{{ $t('参数已成功下发至设备并已执行成功') }}</view>
        <view>{{ $t('下发失败') }}：{{ $t('参数已成功下发至设备，设备并未执行成功') }}</view>
      </view>
      <view>
        <view style="color: #333;margin-bottom: 10rpx;">
          {{ $t('直流源电压设置') }}：
        </view>
        <view>{{ $t('设置DC模块恒压模式下输出电压。') }}（50~1000）V</view>
      </view>
      <view>
        <view style="color: #333;margin-bottom: 10rpx;">
          {{ $t('电池恒流设置') }}：
        </view>
        <view>{{ $t('设置恒流模式下的输出电流。') }}（-2000~2000）A</view>
      </view>
      <view>
        <view style="color: #333;margin-bottom: 10rpx;">
          {{ $t('电池恒功率设置') }}：
        </view>
        <view>{{ $t('设置恒功率模式下的输出功率。') }}（-100~100）%</view>
      </view>
      <view>
        <view style="color: #333;margin-bottom: 10rpx;">
          {{ $t('光伏限功率设置') }}：
        </view>
        <view>{{ $t('设置光伏功率最大值。') }}（0~100）%</view>
      </view>
    </view>
    <u-button type="primary" style="margin-top: 40rpx;border-radius: 6px;" @click="handleSendClick"
      :disabled="isSend">{{ $t('下发') }}</u-button>
    <view class="ft12 u-m-t-20 color-grey"
      style="width: 100%;display: flex; align-items: center;justify-content: center;" v-if="isSend">
      <u-icon name="error-circle" size="14px" style="margin-right: 6rpx;margin-top: 1rpx;"></u-icon>
      <view>
        {{ $t('设备已离线，不可下发') }}
      </view>
    </view>
  </view>
</template>

<script setup>
  import {
    ref,
    reactive,
    getCurrentInstance,
    computed,
    toRefs
  } from 'vue';
  import {
    useParamStore
  } from '@/store/param.js'
  import {
    useMonitorStore
  } from '@/store/monitor.js'
  import {
    onShow,
    onLoad
  } from '@dcloudio/uni-app'
  import otherColor from '../../../common/other.module.scss'
  import {
    isGroupFn
  } from '@/hook/useDeviceType.js'
  import { paramLabelStyle, inputNumberStyle } from '@/constant'

  const paramStore = useParamStore()
  const {
    routeQuery,
    control,
    groupControl
  } = toRefs(useMonitorStore())
  const {
    proxy
  } = getCurrentInstance()
  const isGroup = computed(() => isGroupFn(routeQuery.value.type))
  const current = ref()
  onLoad((options) => {
    current.value = options.index
  })

  // 使用 reactive 创建响应式状态  
  const form = ref({
    setting1920: undefined,
    setting1921: undefined,
    setting1922: undefined,
    setting1923: undefined,
    status: 0
  })
  const rules = ref({
  })
  const formRef = ref(null);
  const handleSendClick = async () => {
    let ac = null
    if (isGroup.value) {
      ac = routeQuery.value.groupId[current.value]
    } else {
      ac = routeQuery.value.id
    }
    // if (formRef.value) {
    //   formRef.value.validate().then(async valid => {
    //     if (valid) {
          try {
            await paramStore.sendParamMDCFn({
              setting1920: form.value.setting1920,
              setting1921: form.value.setting1921,
              setting1922: form.value.setting1922,
              setting1923: form.value.setting1923,
              ac: ac,
              id: form.value.id
            })
            setTimeout(() => {
              uni.$u.toast(uni.$t('下发成功'))
            }, 20)
            getInfo()
          } catch (e) {
            setTimeout(() => {
              uni.$u.toast(uni.$t('下发失败'))
            }, 20)
            getInfo()
          }
      //   }
      // })
    // }
  }

  const isSend = computed(() => {
    if (isGroup.value) {
      let ac = routeQuery.value.groupId[current.value]
      let value = groupControl.value.find(item => item.ac == ac)
      if (!Object.keys(value).length) return true
      return value['onLineState'] == '离线'
    } else {
      if (!Object.keys(control.value).length) return true
      return control.value['onLineState'] == '离线'
    }
  })

  const getInfo = async () => {
    let ac = null
    if (isGroup.value) {
      ac = routeQuery.value.groupId[current.value]
    } else {
      ac = routeQuery.value.id
    }
    const res = await paramStore.mdcInfoFn({
      ac
    })
    if (!res) return
    for (let key in res) {
     if (res[key] == null) res[key] = undefined
    }
    let data = JSON.parse(JSON.stringify(form.value))
    // if (res.status == 1 && data.status == 2) {
    //   uni.$u.toast(uni.$t('下发成功'))
    // } else if (res.status == 3 && data.status == 2) {
    //   uni.$u.toast(uni.$t('下发失败'))
    // }
    form.value = {
      ...res,
    }
  }
  onShow(() => {
    getInfo()
  })
</script>

<style scoped lang="scss">
  .send-status {
    display: flex;
    align-items: center;

    image {
      margin-right: 6rpx;
    }
  }

  .param-form {
    width: 100%;
    background-color: #fff;
    border-radius: $uni-border-radius-lg;
    padding: 0 30rpx;
  }

  :deep(.u-form-item__body__right__content__slot) {
    flex-direction: row-reverse;
  }
</style>