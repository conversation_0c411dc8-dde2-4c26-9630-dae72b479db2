<template>
  <view class="profile" :style="[style]">
    <u-navbar leftText="返回" :bgColor="otherColor.bgColor" :title="$t('tabbar.tabbar4')" :placeholder="true">
    </u-navbar>
    <view class="home-top">
      <view class="top-user">
        <u-avatar :src="avatar" size="140"></u-avatar>
        <view class="bold u-m-b-20 u-m-t-18">
          {{ userInfo.userName }}
        </view>
        <!-- <view class="u-m-b-20">
          {{ roleGroup }}
        </view> -->
      </view>
      <view class="top flex align-items">
        <view class="top-item" @click="handleChangePassword" hover-class="hover-bg">
          <image src="../../static/password.png" class="item-img"></image>
          <view class="item-text">{{ $t('changePassword') }}</view>
        </view>
        <view class="top-item" @click="handleLanguageClick('lang')">
          <image src="../../static/lanauge.png" class="item-img"></image>
          <view class="item-text">{{ $t('语言') }}</view>
        </view>
        <view class="top-item" @click="handleLanguageClick('service')" v-if="serviceArr.length > 1">
          <image src="../../static/server.png" class="item-img"></image>
          <view class="item-text">{{ $t('服务器') }}</view>
        </view>
        <view class="top-item" @click="handleAbout">
          <image src="../../static/about.png" class="item-img"></image>
          <view class="item-text">{{ $t('关于APP') }}</view>
        </view>
        <view class="top-item" @click="handleClearClick">
          <image src="../../static/clear.png" class="item-img"></image>
          <view class="item-text">{{ $t('清空缓存') }}</view>
        </view>
        <view class="top-item" @click="handleLogoutClick">
          <image src="../../static/about.png" class="item-img"></image>
          <view class="item-text">{{ $t('退出登录') }}</view>
        </view>
      </view>
    </view>


    <u-picker :show="isLanguageShow" :columns="selectOptions" :defaultIndex="currentIndex" keyName="label"
      :closeOnClickOverlay="true" @close="languageCancel" @cancel="languageCancel" @confirm="languageConfirm"
      itemHeight="88" :cancelText="$t('取消')" :confirmText="$t('确认')"></u-picker>

    <view class="profile-bottom" @click="handlePhone" :style="{ bottom: `calc(30rpx + ${style.paddingBottom})` }">
      {{ $t('联系我们') }}：+86 0755-23051586
    </view>
  </view>
</template>

<script setup>
  import {
    ref,
    getCurrentInstance,
    computed
  } from 'vue'
  import {
    useLoginStore
  } from '@/store/login.js'
  import {
    languageArr
  } from '@/locale/index.js'
  import {
    init as initLang
  } from '@/locale/index'
  import {
    getUserProfile
  } from '@/api/login.js'
  import {
    onShow
  } from '@dcloudio/uni-app'
  import {
    config,
    initService
  } from '@/common/request.js'
  import otherColor from '../../common/other.module.scss'

  const loginStore = useLoginStore()
  const {
    proxy
  } = getCurrentInstance()

  /**
   * 退出登录
   */
  const handleLogoutClick = () => {
    uni.showModal({
      title: uni.$t('系统提示'),
      content: uni.$t('确定退出系统吗？'),
      confirmColor: otherColor.primaryColor,
      success: async (res) => {
        if (res.confirm) {
          await loginStore.LogOut()
        } else if (res.cancel) {
          console.log('用户点击取消');
        }
      }
    })
  }

  /**
   * 切换语言
   */
  const actionName = ref('')
  const serviceArr = ref([])
  const initLanguage = () => {
    serviceArr.value = JSON.parse(import.meta.env.VITE_SERVICE_ARR).map(item => {
      if (item.value == 'zh' || item.value == 'en' || item.value == 'test') {
        item.label = uni.$t(item.label)
      }
      return item
    })
  }
  initLanguage()
  const selectOptions = computed(() => {
    if (actionName.value == 'lang') {
      return [languageArr]
    } else if (actionName.value == 'service') {
      return [serviceArr.value]
    }
  })
  const handleLanguageClick = (type) => {
    actionName.value = type
    initSelectCurrentIndex()
    isLanguageShow.value = true
  }
  const isLanguageShow = ref(false)
  const languageColumns = ref([
    languageArr
  ])
  const languageCancel = () => {
    isLanguageShow.value = false
  }
  const languageConfirm = ({
    indexs,
    value,
    values
  }) => {
    isLanguageShow.value = false
    if (actionName.value == 'lang') {
      uni.cache.setItem('language', value[0].value)
      currentLanguage.value = value[0].label
      uni.setLocale(value[0].value)
      proxy.$i18n.locale = value[0].value
      initLanguage()
    } else if (actionName.value == 'service') {
      uni.cache.setItem('service', value[0].value)
      initService()
      uni.reLaunch({
        url: '/pages/common/login'
      })
    }
  }
  const currentLanguage = ref()
  const currentIndex = ref([])
  currentLanguage.value = languageArr.find(item => item.value == uni.cache.getItem('language')).label

  const initSelectCurrentIndex = () => {
    if (actionName.value == 'lang') {
      currentIndex.value = [languageArr.findIndex(item => item.value == uni.cache.getItem('language'))]
    } else if (actionName.value == 'service') {
      currentIndex.value = [serviceArr.value.findIndex(item => item.value == uni.cache.getItem('service'))]
    }
  }

  /**
   * 清缓存
   */
  const handleClearClick = () => {
    uni.showModal({
      title: uni.$t('系统提示'),
      content: uni.$t('确定清空所有缓存，包括登录状态，记住密码等？'),
      confirmColor: otherColor.primaryColor,
      success: (res) => {
        if (res.confirm) {
          uni.clearStorageSync()
          initLang()
          uni.reLaunch({
            // url: '/pages/profile/index-copy'
            url: '/pages/common/login'
          })
          uni.$u.toast(uni.$t('清空成功'))
        } else if (res.cancel) {
          console.log('用户点击取消');
        }
      }
    })
  }

  /**
   * 获取用户信息
   */
  const userInfo = ref({})
  const roleGroup = ref()
  const avatar = ref()
  const getUserProfileFn = async () => {
    const res = await getUserProfile()
    userInfo.value = res.data
    roleGroup.value = res.roleGroup
    avatar.value = `${config.baseURL}profile${res.data.avatar.split('profile')[1]}`
  }
  onShow(() => {
    getUserProfileFn()
  })

  /**
   * 修改密码
   */
  const handleChangePassword = () => {
    uni.$u.toast(uni.$t('功能开发中~'))
    // uni.navigateTo({
    //   url: '/pages/common/chnage-password'
    // })
  }

  /**
   * 关于App
   */
  const handleAbout = () => {
    uni.navigateTo({
      url: '/pages/profile/about-app'
    })
  }

  /**
   * 联系我们
   */
  const handlePhone = () => {
    uni.makePhoneCall({
      phoneNumber: '+86 0755-23051586'
    })
  }

  /**
   * h5、app
   */
  const style = computed(() => {
    const obj = {}
    // #ifdef APP-PLUS
    obj.paddingBottom = '0'
    // #endif
    // #ifdef H5
    obj.paddingBottom = '50px'
    // #endif

    return obj
  })
</script>

<style scoped lang="scss">
  .profile {
    width: 100%;
    height: 100vh;

    .home-top {
      background: $transparent;
      height: 340rpx;
      width: 100%;
      position: relative;
      padding-top: 10rpx;
    }

    .top-user {
      text-align: center;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 30rpx;
    }

    .top {
      flex-wrap: wrap;
      background-color: $uni-bg-color;
      border-radius: $uni-border-radius-lg;
      margin-bottom: 20rpx;
      text-align: center;
      margin: 0 30rpx;
      position: absolute;
      top: 270rpx;
      width: calc(100% - 60rpx);
      padding-top: 40rpx;
      padding-bottom: 20rpx;

      .hover-bg {
        // background-color: #f3f4f6;
      }

      .top-item {
        font-size: 12px;
        margin-top: 8rpx;
        // line-height: 40rpx;
        width: 33.3%;
        margin-bottom: 30rpx;

        .item-img {
          width: 38rpx;
          height: 38rpx;
        }

        .item-text {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-top: 10rpx;
        }
      }
    }

    .profile-bottom {
      position: fixed;
      bottom: 30rpx;
      width: 100%;
      text-align: center;
      font-size: 14px;
      color: $uni-text-color-grey;
    }

    .item-img {
      margin-right: 10rpx;
      width: 34rpx;
      height: 34rpx;
    }
  }

  :deep(.u-navbar__content__left) {
    display: none;
  }
</style>