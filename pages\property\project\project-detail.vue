<template>
  <view class="device-info">
    <view class="base-info">
      <view class="info-item flex justify-content align-items">
        <view class="bold flex align-items">
          <image src="../../../static/detail.png" class="item-img"></image>
          <span>{{ $t('项目信息') }}</span>
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('项目名称') }}</view>
        <view>{{ info.projectName }}</view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('国家') }}</view>
        <view>{{ info.country }}
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('区域') }}</view>
        <view>{{ info.projectArea }}
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('项目地址') }}</view>
        <view>{{ info.projectAddress }}
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('经纬度') }}</view>
        <view>{{ `${info.projectLatitudex},${info.projectLatitudey}` }}
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('时区地址') }}</view>
        <view>{{ info[getPropFn()] }}
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('创建人员') }}</view>
        <view>{{ info.nickName }}
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('添加时间') }}</view>
        <view>{{ info.createTime }}
        </view>
      </view>
    </view>
    <view class="base-info" v-if="info.ylkDevices">
      <view class="info-item flex justify-content align-items">
        <view class="bold flex align-items">
          <image src="../../../static/device-list.png" class="item-img"></image>
          <span>{{ $t('设备列表') }}</span>
        </view>
      </view>
      <view class="device-list">
        <view class="device-item" v-for="item in info.ylkDevices" :key="item.deviceId" @click="handleDeviceItemClick(item)">
          <view class="item-ti u-line-1">{{ item.deviceName }}</view>
          <view class=" u-line-1">{{ $t('额定功率') }}：<span class="ri-num">{{ item.deviceRatedPower }}</span>kW</view>
          <view class=" u-line-1">{{ $t('电池容量') }}：<span class="ri-num">{{ item.deviceBatteryCapacity || '--' }}</span>kWh</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
  import { ref } from 'vue'
  import { onLoad, onShow } from '@dcloudio/uni-app'
  import { getProjectInfo } from '@/api/project.js'
  
  const id = ref()
  onLoad((options) => {
    id.value = options.id
  })
  onShow(() => {
    getInfoFn()
  })
  
  const info = ref({})
  const getInfoFn = async () => {
    const res = await getProjectInfo({ projectId: id.value })
    info.value = res.data
  }
  
  const handleDeviceItemClick = (item) => {
    uni.navigateTo({
      url: `/pages/property/device/device-detail?id=${item.deviceId}&isShow=true`
    })
  }
  const getPropFn = () => {
    const lang = uni.cache.getItem('language')
    switch (lang) {
      case 'zh':
        return 'timeZoneAddress'
      case 'en':
        return 'timeZoneAddressUs'
      case 'it':
        return 'timeZoneAddressIt'
      default:
        return 'timeZoneAddress'
    }
  }
</script>

<style lang="scss" scoped>
  .device-info {
    height: 100vh;
    overflow: auto;
    padding-bottom: 10rpx;
    .base-info, .group-info {
      background-color: $uni-bg-color;
      border-radius: $uni-border-radius-lg;
      padding: 0 30rpx;
      margin: 20rpx 30rpx;
    }
  
    .info-item {
      padding: 30rpx 0;
      border-bottom: 1px solid $detail-border-color;
      
      .item-img {
        width: 30rpx;
        height: 30rpx;
        margin-right: 10rpx;
      }
  
      .item-unit {
        font-size: 12px;
        // color: $uni-text-color-grey;
        margin-left: 4rpx;
      }
  
      view:nth-child(2n) {
        width: 60%;
        text-align: right;
      }
    }
  
    .info-item:last-child {
      border-bottom: none;
    }
    
    .device-list {
      padding-top: 20rpx;
      padding-bottom: 10rpx;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      .device-item {
        background-color: $uni-bg-color-grey;
        padding: 16rpx;
        border-radius: 20rpx;
        font-size: 12px;
        margin-bottom: 20rpx;
        line-height: 40rpx;
        width: 48%;
        .item-ti {
          font-size: 14px;
          font-weight: bold;
        }
      }
    }
  }
</style>
