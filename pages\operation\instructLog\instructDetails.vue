<template>
  <view class="device-info">
    <view class="base-info">
      <view class="info-item flex justify-content align-items">
        <view class="bold flex align-items">
          <image src="../../../static/detail.png" class="item-img"></image>
          <span>{{ obj.type == 5 ? $t('升级记录'): $t('远程控制记录') }}</span>
        </view>
      </view>
    </view>
    <template v-if="obj.type == 8">
      <view class="base-info" v-for="item in info" :key="item.id">
        <view class="info-item flex justify-content align-items">
          <view>{{ $t('序列号') }}</view>
          <view :class="['flex', 'align-items', 'u-flex-end']" style="text-align: center;">
            <view class="u-m-r-5">{{ item.ac }}</view>
            <u-copy :content="item.ac" :notice="$t('复制成功')" class="flex align-items" style="width: auto;">
              <image src="../../../static/copy.png" class="copy-img-width"></image>
            </u-copy>
          </view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view>{{ $t('回复类型') }}</view>
          <view><u-tag :text="item.askdt == 'IT25' ? $t('下发'): $t('关闭')" size="mini" plain plainFill></u-tag>
          </view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view>{{ $t('回复结果') }}</view>
          <view><u-tag :text="getBitType(item.code).text" size="mini" :type="getBitType(item.code).type" plain
              plainFill></u-tag>
          </view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view>{{ $t('回复时间') }}</view>
          <view>{{ item.sdt }}
          </view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view>{{ $t('描述') }}</view>
          <view>{{ item.vncDesc ?? '--' }}
          </view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view>{{ $t('添加时间') }}</view>
          <view>{{ item.createTime }}(UTC+08:00)
          </view>
        </view>
      </view>
    </template>
    <template v-else>
      <view class="base-info" v-for="item in info" :key="item.id">
        <view class="info-item flex justify-content align-items">
          <view>{{ $t('序列号') }}</view>
          <view :class="['flex', 'align-items', 'u-flex-end']" style="text-align: center;">
            <view class="u-m-r-5">{{ item.ac }}</view>
            <u-copy :content="item.ac" :notice="$t('复制成功')" class="flex align-items" style="width: auto;">
              <image src="../../../static/copy.png" class="copy-img-width"></image>
            </u-copy>
          </view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view>{{ $t('升级文件') }}</view>
          <view class="u-line-2">{{ item.softUpdFileName }}
          </view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view>{{ $t('升级版本') }}</view>
          <view class="u-line-2">{{ item.softUpdFileName }}
          </view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view>{{ $t('当前升级个数') }}</view>
          <view>{{ item.current ? item.current: '--' }}
          </view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view>{{ $t('总升级个数') }}</view>
          <view>{{ item.total ? item.total: '--' }}
          </view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view>{{ $t('升级结果') }}</view>
          <view>{{ item.softUpdResult }}
          </view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view>{{ $t('升级描述') }}</view>
          <view>{{ item.updDesc ? item.updDesc: '--' }}
          </view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view>{{ $t('升级时间') }}</view>
          <view>{{ item.softUpdTime }}(UTC+08:00)
          </view>
        </view>
      </view>
    </template>
  </view>
</template>

<script setup>
  import {
    ref,
    computed
  } from 'vue'
  import {
    onLoad,
    onShow
  } from '@dcloudio/uni-app'
  import {
    getSelectVncInfo,
    getUpgradeProgress
  } from '@/api/instruct.js'
  import {
    decimalToBinaryReverseArray
  } from '@/common/utils'

  const obj = ref()
  onLoad((options) => {
    obj.value = options
  })
  onShow(() => {
    if (obj.value.type == 5) {
      getUpgradeProgressFn()
    } else {
      getSelectVncInfoFn()
    }
  })

  const info = ref({})
  const getSelectVncInfoFn = async () => {
    const res = await getSelectVncInfo({
      ac: obj.value.ac,
      uuid: obj.value.uuid
    })
    info.value = res.data
  }
  const getUpgradeProgressFn = async () => {
    const res = await getUpgradeProgress({
      ac: obj.value.ac,
      uuid: obj.value.uuid
    })
    info.value = res.data
  }
  const getBitType = computed(() => {
    return (num) => {
      let n = parseInt(num)
      if (n == 0) return {
        text: uni.$t('成功'),
        type: 'success'
      }
      let res = decimalToBinaryReverseArray(n)
      let result = []
      let msg = [uni.$t('成功'), uni.$t('目录创建失败'), uni.$t('文件下载失败'), uni.$t('库文件缺失')]
      res.forEach((item, index) => {
        if (index != 0) {
          if (item == 1) result.push(msg[index])
        }
      })
      return {
        text: result.join('，'),
        type: 'error'
      }
    }
  })
</script>

<style lang="scss" scoped>
  .device-info {
    height: 100vh;
    overflow: auto;

    .base-info,
    .group-info {
      background-color: $uni-bg-color;
      border-radius: $uni-border-radius-lg;
      padding: 0 30rpx;
      margin: 20rpx 30rpx;
    }

    .base-info1 {
      margin: 0 30rpx;
    }

    .info-item {
      padding: 30rpx 0;
      border-bottom: 1px solid $detail-border-color;

      .item-img {
        width: 30rpx;
        height: 30rpx;
        margin-right: 10rpx;
      }

      .item-unit {
        font-size: 12px;
        // color: $uni-text-color-grey;
        margin-left: 4rpx;
      }

      view:nth-child(2n) {
        width: 50%;
        text-align: right;
        overflow-wrap: break-word;
      }
    }

    .info-item:last-child {
      border-bottom: none;
    }
  }

  :deep(.u-tag--mini) {
    height: auto;
    padding: 0 10rpx;
  }
</style>