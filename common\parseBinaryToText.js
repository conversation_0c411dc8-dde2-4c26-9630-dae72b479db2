/**
 * 将十进制转二进制的反向数组数据根据点位展示具体信息
 */
import { decimalToBinaryReverseArray } from './utils.js'
// import i18n from '@/lang'

/**
 * num为十进制
 */
/**
 * 监控显示屏
 */
// 系统工作模式 1001 bit3、bit4、bit5
export const getWorkState = (num) => {
  let n = parseInt(num)
  if (n == 0) return '--'
  let res = decimalToBinaryReverseArray(n)
  if (res.length < 4) return uni.$i18n().t('正常')
  if (res.length == 4) return uni.$i18n().t('电池不可充电')
  if (res.length == 5) return uni.$i18n().t('电池不可放电')
  if (res.length >= 6) {
    let bit3 = res[3]
    let bit4 = res[4]
    let bit5 = res[5]
    if (bit3 == 1 && bit4 == 0 && bit5 == 0) return uni.$i18n().t('电池不可充电')
    if (bit3 == 0 && bit4 == 1 && bit5 == 0) return uni.$i18n().t('电池不可放电')
    if (bit3 == 0 && bit4 == 0 && bit5 == 1) return uni.$i18n().t('电池不可启动')
    if (bit3 == 1 && bit4 == 0 && bit5 == 1) return uni.$i18n().t('电池不可启动')
    if (bit3 == 1 && bit4 == 1 && bit5 == 1) return uni.$i18n().t('正常')
    return '--'
  }
}

// 监控列表，系统工作模式 1001 bit3\bit4，bit1\bit2，优先显示bit3\bit4
export const getListWorkState = (num) => {
  let n = parseInt(num)
  if (n == 0) return '--'
  let res = decimalToBinaryReverseArray(n)
  if (res.length < 4) return getOnAndOff(num)
  if (res.length == 4) return uni.$i18n().t('电池不可充电')
  if (res.length == 5) return uni.$i18n().t('电池不可放电')
  if (res.length >= 6) {
    let bit3 = res[3]
    let bit4 = res[4]
    let bit5 = res[5]
    if (bit3 == 1 && bit4 == 0) return uni.$i18n().t('电池不可充电')
    if (bit3 == 0 && bit4 == 1) return uni.$i18n().t('电池不可放电')
    if (bit3 == 0 && bit4 == 0) return getOnAndOff(num)
    return '--'
  }
}

// 系统工作模式 1001 bit1、bit2
export const getOnAndOff = (num) => {
  let n = parseInt(num)
  if (n == 0) return '--'
  let res = decimalToBinaryReverseArray(n)
  if (res.length < 2) return '--'
  if (res.length == 2) return res[1] == 1 ? uni.$i18n().t('开启') : '--'
  if (res.length > 2) {
    if (res[1] == 1) {
      return uni.$i18n().t('开启')
    } else if (res[2] == 1) {
      return uni.$i18n().t('关闭')
    } else {
      return '--'
    }
  }
}

// 并离网状态 1001 bit14
export const get1001Bit14 = (num) => {
  let n = parseInt(num)
  if (n == 0) return '--'
  let res = decimalToBinaryReverseArray(n)
  if (res.length < 15) return uni.$i18n().t('并网')
  return res[14] == 1 ? uni.$i18n().t('离网') : uni.$i18n().t('并网')
}

// IO状态位 1003
export const get1003 = (num, bit) => {
  let n = parseInt(num)
  // if (n == 0) return '--'
  let res = decimalToBinaryReverseArray(n)
  if (bit == 2 && res[2] == 0) return uni.$i18n().t('正常')
  if (bit == 2 && res[2] == 1) return uni.$i18n().t('故障')
  if (bit == 3 && res[3] == 0) return uni.$i18n().t('正常')
  if (bit == 3 && res[3] == 1) return uni.$i18n().t('故障')
  if (bit == 4 && res[4] == 0) return uni.$i18n().t('正常')
  if (bit == 4 && res[4] == 1) return uni.$i18n().t('故障')
  if (bit == 5 && res[5] == 0) return uni.$i18n().t('正常')
  if (bit == 5 && res[5] == 1) return uni.$i18n().t('故障')
  // if (res.length < 3) return '--'
  // if (res.length == 3) {
  //   if (bit == 2 && res[2] == 0) return uni.$i18n().t('正常')
  //   if (bit == 2 && res[2] == 1) return uni.$i18n().t('故障')
  //   if (bit == 3) return '--'
  //   if (bit == 4) return '--'
  //   if (bit == 5) return '--'
  // }
  // if (res.length == 4) {
  //   if (bit == 2 && res[2] == 0) return uni.$i18n().t('正常')
  //   if (bit == 2 && res[2] == 1) return uni.$i18n().t('故障')
  //   if (bit == 3 && res[3] == 0) return uni.$i18n().t('正常')
  //   if (bit == 3 && res[3] == 1) return uni.$i18n().t('故障')
  //   if (bit == 4) return '--'
  //   if (bit == 5) return '--'
  // }
  // if (res.length == 5) {
  //   if (bit == 2 && res[2] == 0) return uni.$i18n().t('正常')
  //   if (bit == 2 && res[2] == 1) return uni.$i18n().t('故障')
  //   if (bit == 3 && res[3] == 0) return uni.$i18n().t('正常')
  //   if (bit == 3 && res[3] == 1) return uni.$i18n().t('故障')
  //   if (bit == 4 && res[4] == 0) return uni.$i18n().t('正常')
  //   if (bit == 4 && res[4] == 1) return uni.$i18n().t('故障')
  //   if (bit == 5) return '--'
  // }
  // if (res.length == 6) {
  //   if (bit == 2 && res[2] == 0) return uni.$i18n().t('正常')
  //   if (bit == 2 && res[2] == 1) return uni.$i18n().t('故障')
  //   if (bit == 3 && res[3] == 0) return uni.$i18n().t('正常')
  //   if (bit == 3 && res[3] == 1) return uni.$i18n().t('故障')
  //   if (bit == 4 && res[4] == 0) return uni.$i18n().t('正常')
  //   if (bit == 4 && res[4] == 1) return uni.$i18n().t('故障')
  //   if (bit == 5 && res[5] == 0) return uni.$i18n().t('正常')
  //   if (bit == 5 && res[5] == 1) return uni.$i18n().t('故障')
  // }
}

// 自动模式状态 1001 bit15 策略状态
export const get1001Bit15 = (num) => {
  let n = parseInt(num)
  if (n == 0) return '--'
  let res = decimalToBinaryReverseArray(n)
  if (res.length < 16) return uni.$i18n().t('停止')
  return res[15] == 1 ? uni.$i18n().t('运行') : uni.$i18n().t('停止')
}

/**
 * AC
 */
// 并离网状态 2015 bit4
export const get2015Bit4 = (num) => {
  let n = parseInt(num)
  if (n == 0) return '--'
  let res = decimalToBinaryReverseArray(n)
  if (res.length < 5) return uni.$i18n().t('并网')
  return res[4] == 1 ? uni.$i18n().t('离网') : uni.$i18n().t('并网')
}

// 充放电 2015 bit5
export const get2015Bit5 = (num) => {
  let n = parseInt(num)
  if (n == 0) return '--'
  let res = decimalToBinaryReverseArray(n)
  if (res.length < 6) return uni.$i18n().t('放电')
  return res[5] == 1 ? uni.$i18n().t('充电') : uni.$i18n().t('放电')
}

// 运行状态 2015 bit6
export const get2015Bit6 = (num) => {
  let n = parseInt(num)
  if (n == 0) return '--'
  let res = decimalToBinaryReverseArray(n)
  if (res.length < 7) return uni.$i18n().t('停机')
  return res[6] == 1 ? uni.$i18n().t('运行') : uni.$i18n().t('停机')
}

// 故障状态 2015 bit7
export const get2015Bit7 = (num) => {
  let n = parseInt(num)
  if (n == 0) return '--'
  let res = decimalToBinaryReverseArray(n)
  if (res.length < 8) return uni.$i18n().t('正常')
  if (res.length >= 8) {
    return res[7] == 1 ? uni.$i18n().t('故障') : uni.$i18n().t('正常')
  }
}

/**
 * DC
 */
// 运行状态 3004 bit6
export const get3004Bit6 = (num) => {
  let n = parseInt(num)
  if (n == 0) return '--'
  let res = decimalToBinaryReverseArray(n)
  if (res.length < 7) return uni.$i18n().t('停机')
  return res[6] == 1 ? uni.$i18n().t('运行') : uni.$i18n().t('停机')
}

// 故障状态 3004 bit7
export const get3004Bit7 = (num) => {
  let n = parseInt(num)
  if (n == 0) return '--'
  let res = decimalToBinaryReverseArray(n)
  if (res.length < 8) return uni.$i18n().t('正常')
  return res[7] == 1 ? uni.$i18n().t('故障') : uni.$i18n().t('正常')
}

/**
 * STS
 */
// 设备状态 3502 bit0
export const get3002Bit0 = (num) => {
  let n = parseInt(num)
  if (n == 0) return '--'
  let res = decimalToBinaryReverseArray(n)
  if (res.length == 1 && res[0] == 1) return uni.$i18n().t('闭合')
  if (res.length == 1 && res[0] == 0) return uni.$i18n().t('断开')
  return res[0] == 1 ? uni.$i18n().t('闭合') : uni.$i18n().t('断开')
}

// 发电机状态 3502 bit10
export const isShow3502Bit10 = (num) => {
  let n = parseInt(num)
  if (n == 0) return '--'
  let res = decimalToBinaryReverseArray(n)
  if (res.length < 10) return false
  if (res.length == 10) {
    return res[10] == 1 ? true : false
  }
  if (res.length > 10) {
    return res[10] == 1 ? true : false
  }
}

/**
 * BMS
 */
// 电池空调状态 4058
export const get4058 = (num, bit) => {
  let n = parseInt(num)
  // if (n == 0) return '--'
  let res = decimalToBinaryReverseArray(n)
  if (bit == 0 && res[0] == 0) return uni.$i18n().t('停止')
  if (bit == 0 && res[0] == 1) return uni.$i18n().t('运行')
  if (bit == 1 && res[1] == 0) return uni.$i18n().t('停止')
  if (bit == 1 && res[1] == 1) return uni.$i18n().t('运行')
  if (bit == 2 && res[2] == 0) return uni.$i18n().t('停止')
  if (bit == 2 && res[2] == 1) return uni.$i18n().t('运行')
  if (bit == 3 && res[3] == 0) return uni.$i18n().t('停止')
  if (bit == 3 && res[3] == 1) return uni.$i18n().t('运行')
  if (bit == 4 && res[4] == 0) return uni.$i18n().t('停止')
  if (bit == 4 && res[4] == 1) return uni.$i18n().t('运行')
  if (bit == 5 && res[5] == 0) return uni.$i18n().t('停止')
  if (bit == 5 && res[5] == 1) return uni.$i18n().t('运行')
  if (bit == 6 && res[6] == 0) return uni.$i18n().t('停止')
  if (bit == 6 && res[6] == 1) return uni.$i18n().t('运行')
}
