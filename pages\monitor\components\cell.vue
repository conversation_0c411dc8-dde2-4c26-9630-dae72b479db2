<template>
  <view class="data-time">
    <view>{{ $t('上报时间') }}</view>
    <view>{{ cell[0]?.sdt }}({{ baseInfo.timeZone }})</view>
  </view>
  <template v-if="moreCell.length">
    <view class="device-info u-p-t-20 u-p-b-10">
      <view v-for="(item, index) in moreCell" :key="item.text" class="u-m-b-20">{{ index + 1 }}、{{ item.text }}</view>
    </view>
  </template>
  <view class="device-info">
    <u-collapse :value="cellValue" v-if="isShowCell == 1 || moreCell.length">
      <template v-for="(item, index) in cell">
        <u-collapse-item v-for="(clusterItem, clusterIndex) in item.clusterData" :key="clusterItem.name"
          :name="clusterItem.name">
          <template #title>
            <view class="info-ti flex u-flex-between" style="width: 100%">
              <view>{{ clusterItem.name }}</view>
              <view @click.stop="handleClusterClick(index, clusterIndex, clusterItem.name)"><u-button size="mini"
                  type="primary" style="width: 20%">{{ $t('详情') }}</u-button></view>
            </view>
          </template>
          <template #right-icon>
            <view><u-icon name="play-right-fill" size="12px"></u-icon></view>
          </template>
          <view class="info-item flex justify-content align-items" v-if="clusterItem.positive">
            <view class="color-grey">{{ $t('簇级传感器1') }}</view>
            <view v-if="clusterItem.positive">{{ clusterItem.positive }}<span class="item-unit">℃</span></view>
            <view v-else>--</view>
          </view>
          <view class="info-item flex justify-content align-items" v-if="clusterItem.negative">
            <view class="color-grey">{{ $t('簇级传感器2') }}</view>
            <view v-if="clusterItem.negative">{{ clusterItem.negative }}<span class="item-unit">℃</span></view>
            <view v-else>--</view>
          </view>
          <view class="info-item flex justify-content align-items">
            <view class="color-grey">{{ $t('最高单体电压') }}</view>
            <view>{{ clusterItem.maxVoltage }}<span class="item-unit">V</span></view>
          </view>
          <view class="info-item flex justify-content align-items">
            <view class="color-grey">{{ $t('最低单体电压') }}</view>
            <view>{{ clusterItem.minVoltage }}<span class="item-unit">V</span></view>
          </view>
          <view class="info-item flex justify-content align-items">
            <view class="color-grey">{{ $t('最高单体温度') }}</view>
            <view>{{ clusterItem.maxTemperature }}<span class="item-unit">℃</span></view>
          </view>
          <view class="info-item flex justify-content align-items">
            <view class="color-grey">{{ $t('最低单体温度') }}</view>
            <view>{{ clusterItem.minTemperature }}<span class="item-unit">℃</span></view>
          </view>
        </u-collapse-item>
      </template>
    </u-collapse>
    <template v-else-if="isShowCell == 2 || moreCell.length">
      <view style="background-color: #fff;border-radius: 6px;">
        <view class="flex u-flex-between u-p-t-20 u-p-b-20 u-m-b-20" style="border-bottom: 1px solid #eaebec;">
          <view class="bold">{{ currentName }}</view>
          <u-button size="mini" type="primary" style="width: 16%" @click="handleBackClick">{{ $t('返回') }}</u-button>
        </view>
        <view style="display: grid;
      grid-template-columns: repeat(4, 22%);
      grid-gap: 20rpx;
      justify-content: center;">
          <view v-for="(item, index) in cellData" :key="item.TemperatureRiseName" style="width: 100%;"
            @click="handleCellClick(index)">
            <view class="cell-item">
              <view class="cell-item-top">
              </view>
              <view class="cell-item-cont">
                <view class="cell-item-cont-row" v-if="!isEmpty(cell[currentBmsIndex]['bms_7101_7612'])">
                  <image src="../../../static/cellV.svg" mode="" style="width: 28rpx;height: 26rpx"></image>
                  <text class="cell-item-cont-num">{{ item.cellVoltage }}</text>
                  <text class="cell-item-cont-unit">V</text>
                </view>
                <view class="cell-item-cont-row" style="line-height: 28rpx;" v-if="!isEmpty(cell[currentBmsIndex]['bms_7613_8124'])">
                  <image src="../../../static/cellT.svg" mode="" style="width: 26rpx;height: 28rpx"></image>
                  <text class="cell-item-cont-num">{{
                            item.cellTemperature
                          }}</text>
                  <text class="cell-item-cont-unit">℃</text>
                </view>
                <view class="cell-item-cont-row" style="line-height: 28rpx;" v-if="!isEmpty(cell[currentBmsIndex]['bms_9150_9661'])">
                  <image src="../../../static/cellTR.svg" mode="" style="width: 26rpx;height: 28rpx"></image>
                  <text class="cell-item-cont-num">{{
                            item.cellTemperatureRise
                          }}</text>
                  <text class="cell-item-cont-unit">℃</text>
                </view>
                <view class="cell-item-cont-row" style="line-height: 28rpx;" v-if="!isEmpty(cell[currentBmsIndex]['bms_8125_8637'])">
                  <image src="../../../static/cellTR.svg" mode="" style="width: 26rpx;height: 28rpx"></image>
                  <text class="cell-item-cont-num">{{
                            item.cellCurrent
                          }}</text>
                  <text class="cell-item-cont-unit">A</text>
                </view>
                <view class="cell-item-cont-row" style="line-height: 28rpx;" v-if="!isEmpty(cell[currentBmsIndex]['bms_8638_9149'])">
                  <image src="../../../static/cellTR.svg" mode="" style="width: 26rpx;height: 28rpx"></image>
                  <text class="cell-item-cont-num">{{
                            item.cellImpedance
                          }}</text>
                  <text class="cell-item-cont-unit">Ω</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </template>
    <template v-else-if="isShowCell == 0 && !moreCell.length">
      <view>{{ $t('暂未配置电池电芯规格信息，请联系管理员或售后人员。') }}</view>
    </template>
  </view>
</template>

<script setup>
  import {
    ref,
    computed,
    getCurrentInstance
  } from 'vue'
  import {
    useMonitorStore
  } from '@/store/monitor.js'
  import {
    useLoginStore
  } from '@/store/login.js'
  import otherColor from '../../../common/other.module.scss'
  import {
    isGroupFn
  } from '@/hook/useDeviceType.js'
  import {
    onShow
  } from '@dcloudio/uni-app'
  import {
    getCellConfig
  } from '@/api/param'
  import {
    maxBy,
    minBy,
    isEmpty
  } from 'lodash'

  const {
    proxy
  } = getCurrentInstance()
  const monitorStore = useMonitorStore()
  const userStore = useLoginStore()
  const baseInfo = computed(() => monitorStore.baseInfo)

  const cellValue = ref([])
  const cell = computed(() => {
    cellValue.value = []
    let data = monitorStore.pcs_cell
    let type = monitorStore.routeQuery.type
    let isGroup = isGroupFn(type)
    data?.forEach(item => {
      item.clusterData?.forEach((item1, index1) => {
        if (isGroup) {
          item1.name = `${item.label}_${parseInt(item.dc) - 161000 + 1}#BMS-${index1 + 1}#${uni.$t('包')}`
        } else {
          item1.name = `${parseInt(item.dc) - 161000 + 1}#BMS-${index1 + 1}#${uni.$t('簇')}`
        }
        cellValue.value.push(item1.name)
      })
    })
    return data
  })

  const currentBmsIndex = ref(0)
  const currentClusterIndex = ref(0)
  const currentName = ref()
  const cellData = computed(() => {
    if (cell.value.length) {
      return cell.value[currentBmsIndex.value].clusterData.length ? cell.value[currentBmsIndex.value].clusterData[
        currentClusterIndex.value].data : []
    }
    return []
  })
  const isShowCell = ref(0)
  const handleClusterClick = (index, clusterIndex, name) => {
    currentBmsIndex.value = index
    currentClusterIndex.value = clusterIndex
    currentName.value = name
    isShowCell.value = 2
  }
  const handleBackClick = () => {
    isShowCell.value = 1
  }
  const handleCellClick = (index) => {
    uni.$u.toast(uni.$t('cell', [index + 1]))
  }

  // 获取电池电芯配置信息
  const moreCell = ref([])
  const getCellConfigFn = async () => {
    moreCell.value = []

    function setData(data, config) {
      let {
        cellNumber,
        voltageNumber,
        temperatureNumber,
        electricityNumber,
        impedanceNumber,
        temperatureRiseNumber
      } = config
      let cell = JSON.parse(JSON.stringify(data))
      cell?.forEach(item => {
        let clusterLength = item?.bms_7101_7612 ? Object.keys(item?.bms_7101_7612).length / voltageNumber : 0
        item.clusterData = []
        for (let i = 1; i <= clusterLength; i++) {
          let cluster = {
            positive: item.bms_9701_9800[`bms_${9700 + ((i * 2) - 1)}`], // 正极
            negative: item.bms_9701_9800[`bms_${9700 + (i * 2)}`], // 正极
            data: []
          }
          for (let j = 1; j <= cellNumber; j++) {
            let point = {}
            point.cellVoltage = j <= voltageNumber ? item.bms_7101_7612[
              `bms_${7100 + ((voltageNumber * i) - (voltageNumber - j))}`] : '--' // 电压
            point.cellTemperature = j <= temperatureNumber ? item.bms_7613_8124[
                `bms_${7612 + ((temperatureNumber * i) - (temperatureNumber - j))}`] :
              '--' // 温度
            point.cellTemperatureRise = j <= temperatureRiseNumber ? item.bms_9150_9661[
              `bms_${9149 + ((temperatureRiseNumber * i) - (temperatureRiseNumber - j))}`] : '--' // 温升
            point.cellCurrent = j <= electricityNumber ? item.bms_8125_8637[
                `bms_${8124 + ((electricityNumber * i) - (electricityNumber - j))}`] :
              '--' // 电流
            point.cellImpedance = j <= impedanceNumber ? item.bms_8638_9149[
                `bms_${8637 + ((impedanceNumber * i) - (impedanceNumber - j))}`] :
              '--' // 阻抗
            point.voltageName = j <= voltageNumber ?
              `bms_${7100 + ((voltageNumber * i) - (voltageNumber - j))}` : '--'
            point.temperatureName = j <= temperatureNumber ?
              `bms_${7612 + ((temperatureNumber * i) - (temperatureNumber - j))}` : '--'
            point.TemperatureRiseName = j <= temperatureRiseNumber ?
              `bms_${9149 + ((temperatureRiseNumber * i) - (temperatureRiseNumber - j))}` : '--'
            point.currentName = j <= electricityNumber ?
              `bms_${8124 + ((electricityNumber * i) - (electricityNumber - j))}` : '--'
            point.impedanceName = j <= impedanceNumber ?
              `bms_${8637 + ((impedanceNumber * i) - (impedanceNumber - j))}` : '--'
            cluster.data.push(point)
          }
          item.clusterData.push({
            ...cluster,
            maxVoltage: maxBy(cluster.data, (item1) => {
              if (item1.cellVoltage != '--') return item1.cellVoltage
            })?.cellVoltage,
            minVoltage: minBy(cluster.data, (item1) => {
              if (item1.cellVoltage != '--') return item1.cellVoltage
            })?.cellVoltage,
            maxTemperature: maxBy(cluster.data, (item1) => {
              if (item1.cellTemperature != '--') return item1.cellTemperature
            })?.cellTemperature,
            minTemperature: minBy(cluster.data, (item1) => {
              if (item1.cellTemperature != '--') return item1.cellTemperature
            })?.cellTemperature,
            maxTemperatureRise: maxBy(cluster.data, (item1) => {
              if (item1.cellTemperatureRise != '--') return item1.cellTemperatureRise
            })?.cellTemperatureRise,
            minTemperatureRise: minBy(cluster.data, (item1) => {
              if (item1.cellTemperatureRise != '--') return item1.cellTemperatureRise
            })?.cellTemperatureRise,
          })
        }
      })
      return cell
    }
    let type = monitorStore.routeQuery.type
    let isGroup = isGroupFn(type)
    let groupId = monitorStore.routeQuery.groupId
    if (isGroup) {
      Promise.all(groupId.map(item => getCellConfig({
        ac: item
      }))).then(res => {
        res.forEach((item, index) => {
          if (!item.data) {
            isShowCell.value = 0
            let label = null
            if (index == 0) {
              label = `${uni.$t('主机')} - ${index + 1 < 10 ? '0' + (index + 1) : index + 1}`
            } else {
              label = `${uni.$t('从机')} - ${index < 10 ? '0' + index : index}`
            }
            moreCell.value.push({
              text: `${label},${uni.$t('暂未配置电池电芯规格信息，请联系管理员或售后人员。')}`
            })
          } else {
            monitorStore.pcs_cell = setData([monitorStore.pcs_cell[index]], item.data)
            isShowCell.value = 1
          }
        })
      })
    } else {
      const res = await getCellConfig({
        ac: monitorStore.routeQuery.id
      })
      if (!res.data) {
        isShowCell.value = 0
      } else {
        monitorStore.pcs_cell = setData(monitorStore.pcs_cell, res.data)
        isShowCell.value = 1
      }
    }
  }
  getCellConfigFn()
</script>

<style scoped lang="scss">
  .data-time {
    display: flex;
    justify-content: space-between;
    background-color: #fff;
    padding: 20rpx 30rpx;
    margin-bottom: 20rpx;
    margin: 20rpx 30rpx;
    font-size: 10px;
    border-radius: 6px;
  }

  .device-info {
    /* background-color: $uni-bg-color; */
    background-color: #fff;
    border-radius: $uni-border-radius-lg;
    margin: 20rpx 30rpx;
    padding: 0 30rpx;

    .info-ti {
      font-weight: bold;
    }

    .info-item {
      padding: 30rpx 0;
      border-bottom: 1px solid $uni-bg-color-grey;

      .item-unit {
        font-size: 12px;
        color: $uni-text-color-grey;
        margin-left: 4rpx;
      }

      view:nth-child(2n) {
        width: 50%;
        text-align: right;
      }
    }

    .info-item:last-child {
      border-bottom: none;
    }
  }

  :deep(.u-collapse-item__content__text) {
    padding: 0;
    color: #000;
  }

  :deep(.u-cell__body) {
    padding: 30rpx 0;
  }

  :deep(.u-cell--clickable) {
    background-color: $uni-bg-color;
  }

  uni-button {
    margin: 0;
  }

  .cell-item {
    position: relative;
    width: 100%;
    height: 160rpx;
    display: flex;
    align-items: center;
    flex-direction: column;
    font-size: 12px;

    &-top {
      width: 40%;
      height: 15.5%;
      background-color: #eee;
      border-radius: 10px;
      position: absolute;
      top: 0;
      left: 30%;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    &-cont {
      width: 100%;
      height: 90%;
      background-color: #eee;
      border-radius: 10px;
      margin-top: 10%;
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: space-evenly;

      &-row {
        display: flex;
        align-items: center;
      }

      &-color {
        color: #787878
      }
      
      &-num {
        font-weight: 600;
        margin: 0 6rpx;
      }

      &-unit {
        font-size: 10px;
        color: $uni-text-color-grey;
        margin-left: 4rpx;
      }
    }
  }
</style>