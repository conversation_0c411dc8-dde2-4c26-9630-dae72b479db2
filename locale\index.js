import en from './en.json'
import zhHans from './zh-<PERSON>.json'
import it from './it.json'


export const languageArr = [{
    label: '简体中文',
    value: 'zh-Hans',
    api: 'zh_CN'
  },
  {
    label: 'English',
    value: 'en',
    api: 'en_US'
  },
  {
    label: 'ltaliano',
    value: 'it',
    api: 'en_IT'
  },
]

export const init = () => {
  let cacheLanguage = uni.cache.getItem('language')
  if (cacheLanguage) return
  let systemLanguage = uni.$u.sys().language // 系统默认语言
  let platform = uni.$u.os() // ios、android
  // if (platform == 'ios') {
  //   if (systemLanguage == 'zh-Hans-CN') {
  //     systemLanguage = 'zh-Hans'
  //   } else if (systemLanguage == 'zh-Hans-CN') {
  //     systemLanguage = 'en'
  //   }
  // } else if (platform == 'android') {
  //   if (systemLanguage == 'zh-CN') {
  //     systemLanguage = 'zh-Hans'
  //   } else if (systemLanguage == 'en-US') {
  //     systemLanguage = 'en'
  //   }
  // }
  if (systemLanguage.indexOf('zh') !== -1) {
    systemLanguage = 'zh-Hans'
  } else if (systemLanguage.indexOf('en') !== -1) {
    systemLanguage = 'en'
  } else if (systemLanguage.indexOf('it') !== -1) {
    systemLanguage = 'it'
  }
  let index = languageArr.findIndex(item => item.value == systemLanguage)
  let sysLangeage = index == -1 ? 'en' : languageArr[index].value
  uni.cache.setItem('language', sysLangeage)
  uni.setLocale(sysLangeage)
}

export default {
  en: {
    ...en,
    "cell": ({list}) => `${list(0)} cell`
  },
  'zh-Hans': {
    ...zhHans,
    "cell": ({list}) => `第${list(0)}个电芯`
  },
  it: {
    ...it,
    "cell": ({list}) => `第${list(0)}个电芯`
  },
}