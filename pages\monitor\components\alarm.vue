<template>
  <view class="device-alarm">
    <view class="alarm-top flex justify-content align-items"
      :style="{ margin: isShowPopup ? '20rpx 0': '20rpx 30rpx', padding: isShowPopup ? '20rpx 60rpx': '20rpx 30rpx' }">
      <view>{{ showValue }}</view>
      <view>
        <uv-drop-down ref="dropDown" sign="dropDown_1" :defaultValue="[0,'0','all']" text-size="14px"
          text-active-size="14px" :text-color="otherColor.mainColor" :text-active-color="otherColor.primaryColor"
          :extra-active-icon="{name: 'arrow-up', size: '30rpx', color: otherColor.primaryColor}"
          :extra-icon="{name: 'arrow-down', size: '30rpx', color: otherColor.mainColor}">
          <uv-drop-down-item name="order" type="2" :label="$t('筛选')" value="all" style="padding: 0;">
          </uv-drop-down-item>
        </uv-drop-down>
        <uv-drop-down-popup ref="dropDownPopup" sign="dropDown_1" @popupChange="handlePopup">
          <view class="select-popup" :style="{ height: isGroup ? '339px': '294px' }">
            <u-cell-group>
              <u-cell :title="$t('设备')" name="device" @click="handleCellClick" v-if="isGroup">
                <template #value>
                  <text>{{ deviceValue }}</text>
                </template>
              </u-cell>
              <u-cell :title="$t('类型')">
                <template #value>
                  <u-subsection :list="dayList" mode="subsection" :current="current" fontSize="14px" style="width: 50%;"
                    @change="sectionChange" :activeColor="otherColor.primaryColor"
                    :inactiveColor="otherColor.mainColor"></u-subsection>
                </template>C
              </u-cell>
              <u-cell :title="$t('发生时间')" name="date" @click="handleCellClick">
                <template #value>
                  <text class="u-slot-value">{{ dateValue }}</text>
                </template>
              </u-cell>
              <u-cell :title="$t('状态')" name="state" @click="handleCellClick">
                <template #value>
                  <text>{{ stateValue }}</text>
                </template>
              </u-cell>
              <u-cell :title="$t('等级')" name="level" @click="handleCellClick">
                <template #value>
                  <text>{{ levelValue }}</text>
                </template>
              </u-cell>
              <u-cell :title="$t('告警代码')" name="alarmPoint">
                <template #value>
                  <u--input v-model="queryInfo.alarmPoint" :customStyle="{
                  borderRadius: '100px',
                  height: '50rpx',
                  width: '50%'
                }" :placeholderStyle="`{
                  fontSize: '12px'
                }`" shape="circle" :placeholder="$t('请输入')" :adjustPosition="false"></u--input>
                </template>
              </u-cell>
            </u-cell-group>
            <view class="popup-oper flex justify-content align-items">
              <view class="oper-item" @click="handleOperClick('cancel')">
                {{ $t('取消') }}
              </view>
              <view class="oper-item" @click="handleOperClick('confirm')">
                {{ $t('确认') }}
              </view>
            </view>
          </view>
        </uv-drop-down-popup>
        <uv-calendars v-if="current == 0" ref="calendars" :mode="calendarMode" :date="calendarValue"
          @confirm="handleCalendarConfirm" />
        <uv-calendars v-else ref="calendars" :mode="calendarMode" :date="calendarValue"
          @confirm="handleCalendarConfirm" />
        <u-picker :show="isShowSelect" :defaultIndex="defaultSelectIndex" keyName="text" :columns="selectCom"
          itemHeight="88" @confirm="handleSelectConfirm" :closeOnClickOverlay="true" @close="handleSelectCancel"
          @cancel="handleSelectCancel" :cancelText="$t('取消')" :confirmText="$t('确认')"></u-picker>
      </view>
    </view>
    <z-paging ref="paging" v-model="alarmData" show-refresher-update-time refresher-update-time-key="alarm"
      @query="queryList" hide-empty-view :fixed="false" height="100%" use-page-scroll>
      <view class="alarm-item" v-for="item in alarmData" :key="item.alarmNameId" @click="handleItemClick(item)">
        <view class="item-top flex justify-content align-items">
          <view class="item-ti">{{ item.alarmName }}_{{ item.alarmPoint }}</view>
          <view>
            <view class="item-solve flex align-items" v-if="item.state == 1">
              <image src="/static/solve.svg" mode="" class="img-status"></image>
              <span>{{ $t('已处理') }}</span>
            </view>
            <view class="item-solve flex align-items" v-if="item.state == 2">
              <image src="/static/solve-off.svg" mode="" class="img-status"></image>
              <span>{{ $t('未处理') }}</span>
            </view>
          </view>
        </view>
        <view>{{ $t('设备名称') }}：{{ item.deviceName }}</view>
        <view>{{ $t('告警对象') }}：{{ item.name }}</view>
        <view>
          {{ $t('告警等级') }}：
          <u-tag v-if="item.alarmLevel == 1" size="mini" :text="$t('等级一')" type="error" plain></u-tag>
          <u-tag v-if="item.alarmLevel == 2" size="mini" :text="$t('等级二')" type="warning" plain></u-tag>
        </view>
        <view>{{ $t('所属项目') }}：{{ item.projectName }}</view>
        <!-- <view>{{ $t('所属时区') }}：{{ item.timeZone }}</view> -->
        <view>{{ $t('发生时间') }}：{{ item.sdt }}</view>
        <view>{{ $t('上报时间') }}：{{ item.createTime }}(UTC+08:00)</view>
        <view class="item-bo flex justify-content align-items">
          <view>{{ item.ac }}</view>
          <view v-if="isShowPerm(['system:alarm:updateState'])" @click.stop.prevent="handleUpdateClick(item)"><u-button
              :text="$t('处理')" size="small" type="primary" :disabled="item.state == 1"></u-button></view>
        </view>
      </view>
      <view style="background-color: #fff;margin: 0 30rpx;border-radius: 6px;padding: 30rpx 0;height: 70vh;padding-top: 200rpx;"
        v-if="!alarmData.length">
        <u-empty icon="../../static/empty.webp" width="300" height="300" textSize="14px" :text="$t('暂无数据')">
        </u-empty>
      </view>
    </z-paging>
  </view>
</template>

<script setup>
  import {
    ref,
    onMounted,
    getCurrentInstance,
    computed,
  } from 'vue'
  import {
    alarmList,
    updateStateByRecordNameIds
  } from '@/api/alarm.js'
  import {
    isShowPerm,
    dayjsFn,
    getAllDatesInMonth,
    getAllDatesInYear
  } from '@/common/utils.js'
  import {
    useMonitorStore
  } from '@/store/monitor.js'
  import otherColor from '../../../common/other.module.scss'
  import {
    isGroupFn
  } from '@/hook/useDeviceType.js'
  import useZPaging from "@/uni_modules/z-paging/components/z-paging/js/hooks/useZPaging.js";

  const monitorStore = useMonitorStore()
  const {
    proxy
  } = getCurrentInstance()
  const alarmData = ref([])
  const paging = ref({
    pageNum: 1,
    pageSize: 10,
    total: 0
  })
  const queryInfo = ref({
    startTime: '',
    endTime: '',
    state: undefined,
    alarmLevel: undefined,
    ac: undefined,
  })
  const alarmListFn1 = async ({
    pageNum,
    pageSize
  }) => {
    const res = await alarmList({
      pageNum,
      pageSize,
      ...queryInfo.value
    })
    paging.value.total = res.total
    let data = res.rows
    data.forEach(item => {
      if (item.det == "13") {
        item.name = `${parseInt(item.dc) - 131000 + 1}#Monet-AC`
      } else if (item.det == '14') {
        item.name = `${parseInt(item.dc) - 141000 + 1}#Monet-DC`
      } else if (item.det == '15') {
        item.name = `${parseInt(item.dc) - 151000 + 1}#Monet-STS`
      } else if (item.det == '16') {
        // item.name = `${parseInt(item.dc) - 161000 + 1}#BMS`
        item.name = getBmsCluster.value(item)
      } else if (item.det == '12') {
        item.name = uni.$t('本地控制器')
      } else if (item.det == '19') {
        if (item.alarmPoint == '19029' || item.alarmPoint == '19030' || item.alarmPoint == '19031' || item
          .alarmPoint == '19022') {
          item.name = `${parseInt(item.dc) - 191000 + 1}#${uni.$t('充电桩')}_2#${uni.$t('枪')}`
        } else if (item.alarmPoint == '19026' || item.alarmPoint == '19027' || item.alarmPoint == '19028' ||
          item
          .alarmPoint == '19011') {
          item.name = `${parseInt(item.dc) - 191000 + 1}#${uni.$t('充电桩')}_1#${uni.$t('枪')}`
        } else {
          item.name = `${parseInt(item.dc) - 191000 + 1}#${uni.$t('充电桩')}`
        }
      } else if (item.det == '24') {
        item.name = getBmsCluster.value(item)
      }
    })
    return data
  }

  useZPaging(proxy.$refs.paging)
  const queryList = (pageNo, pageSize) => {
    // 组件加载时会自动触发此方法，因此默认页面加载时会自动触发，无需手动调用
    // 这里的pageNo和pageSize会自动计算好，直接传给服务器即可
    // 模拟请求服务器获取分页数据，请替换成自己的网络请求
    alarmListFn1({
      pageNum: pageNo,
      pageSize
    }).then(res => {
      // 将请求的结果数组传递给z-paging
      proxy.$refs.paging.completeByTotal(res, paging.value.total)
    }).catch(res => {
      // 如果请求失败写this.$refs.paging.complete(false);
      // 注意，每次都需要在catch中写这句话很麻烦，z-paging提供了方案可以全局统一处理
      // 在底层的网络请求抛出异常时，写uni.$emit('z-paging-error-emit');即可
      proxy.$refs.paging.complete(false);
    })
  }
  const getBmsCluster = computed(() => {
    return ({
      alarmPoint,
      dc,
      det,
      protocolType
    }) => {
      const ALARM_POINT_BASE = 9900
      const DC_OFFSET = det == '16' ? 161000 : 241000
      const BMS_COUNT = 9
      const isBMSBAUCUSTER = det == '24' && protocolType == 20

      // 输入验证
      if (isNaN(Number(alarmPoint)) || isNaN(Number(dc))) {
        throw new Error('Invalid input: alarmPoint and dc must be numbers')
      }

      let alarmPointNum = Number(alarmPoint)
      let clusterIndex = Math.floor((alarmPointNum - ALARM_POINT_BASE) / 10)

      // 边界条件处理
      if (clusterIndex < 0 || clusterIndex >= BMS_COUNT) {
        clusterIndex = BMS_COUNT // 超出范围则默认为 'BMS'
      }

      const dcValue = parseInt(dc) - DC_OFFSET + 1
      return `${dcValue}#${det == '16' ? 'BMS': 'BMS-BAU'}${clusterIndex === BMS_COUNT ? '' : '_' + (isBMSBAUCUSTER ? uni.$t('簇') : '') + (clusterIndex + 1)}`
    }
  })

  /**
   * 处理告警
   */
  const handleUpdateClick = (item) => {
    if (item.state == 1) return
    uni.showModal({
      title: uni.$t('系统提示'),
      content: uni.$t('确定要手动处理这条异常吗？'),
      confirmColor: otherColor.primaryColor,
      success: function(res) {
        if (res.confirm) {
          updateStateByRecordNameIds([item.alarmNameId]).then(res => {
            if (res.code !== 200) return uni.$u.toast(res.msg)
            uni.$u.toast(uni.$t('处理成功'))
            proxy.$refs.paging.reload()
          })
        } else if (res.cancel) {
          console.log('用户点击取消');
        }
      }
    })
  }

  /**
   * 筛选
   */
  const dayList = ref([{
      name: uni.$i18n().t('日')
    }, {
      name: uni.$i18n().t('月')
    },
    {
      name: uni.$i18n().t('年')
    }
  ])
  const current = ref(1)
  const showValue = ref()
  const groupId = computed(() => {
    let data = monitorStore.routeQuery.groupId
    let res = []
    data.forEach((item, index) => {
      let label = ''
      if (index == 0) {
        label = `${uni.$t('主机')}-${index + 1 < 10 ? '0' + (index + 1) : index + 1}`
      } else {
        label = `${uni.$t('从机')}-${index < 10 ? '0' + index : index}`
      }
      res.push({
        text: label,
        value: item
      })
    })
    return res
  })
  const isGroup = ref(false)
  const init = () => {
    let type = monitorStore.routeQuery.type
    isGroup.value = isGroupFn(type)
    if (isGroup.value) {
      showValue.value = groupId.value[0].text
      queryInfo.value.ac = groupId.value[0].value
    } else {
      if (current.value == 0) {
        showValue.value = dayjsFn().format('YYYY-MM-DD')
      } else if (current.value == 1) {
        showValue.value = dayjsFn().format('YYYY-MM')
      } else if (current.value == 2) {
        showValue.value = dayjsFn().format('YYYY')
      }
      queryInfo.value.ac = monitorStore.routeQuery.id
    }
  }
  init()
  const sectionChange = (index) => {
    current.value = index
    if (index == 0) {
      dateValue.value = dayjsFn().format('YYYY-MM-DD')
      calendarValue.value = dateValue.value
      calendarMode.value = ''
    } else if (index == 1) {
      let start = dayjsFn().startOf('month').format('YYYY-MM-DD')
      let end = dayjsFn().endOf('month').format('YYYY-MM-DD')
      dateValue.value = `${start} ~ ${end}`
      calendarMode.value = 'range'
      calendarValue.value = getAllDatesInMonth(start.split('-')[0], start.split('-')[1])
    } else if (index == 2) {
      let start = dayjsFn().startOf('year').format('YYYY-MM-DD')
      let end = dayjsFn().endOf('year').format('YYYY-MM-DD')
      dateValue.value = `${start} ~ ${end}`
      calendarMode.value = 'range'
      calendarValue.value = getAllDatesInYear(start.split('-')[0])
    }
    changeDate()
  }
  const isShowPopup = ref(false)
  const handlePopup = ({
    show
  }) => {
    isShowPopup.value = show
  }

  const dateValue = ref()
  const calendarValue = ref()
  const calendarMode = ref()
  const initDate = () => {
    if (current.value == 0) {
      dateValue.value = dayjsFn().format('YYYY-MM-DD')
      calendarValue.value = dateValue.value
      calendarMode.value = ''
    } else if (current.value == 1) {
      let start = dayjsFn().startOf('month').format('YYYY-MM-DD')
      let end = dayjsFn().endOf('month').format('YYYY-MM-DD')
      dateValue.value = `${start} ~ ${end}`
      calendarMode.value = 'range'
      calendarValue.value = getAllDatesInMonth(start.split('-')[0], start.split('-')[1])
    } else if (current.value == 2) {
      let start = dayjsFn().startOf('year').format('YYYY-MM-DD')
      let end = dayjsFn().endOf('year').format('YYYY-MM-DD')
      dateValue.value = `${start} ~ ${end}`
      calendarMode.value = 'range'
      calendarValue.value = getAllDatesInYear(start.split('-')[0])
    }
  }
  initDate()
  const handleCellClick = ({
    name
  }) => {
    if (name == 'date') {
      proxy.$refs.calendars.open()
    } else if (name == 'state') {
      selectCom.value = [stateOptions.value]
      selectText.value = name
      defaultSelectIndex.value = [stateOptions.value.findIndex(item => item.text == stateValue.value)]
      isShowSelect.value = true
    } else if (name == 'level') {
      selectCom.value = [levelOptions.value]
      selectText.value = name
      defaultSelectIndex.value = [levelOptions.value.findIndex(item => item.text == levelValue.value)]
      isShowSelect.value = true
    } else if (name == 'device') {
      selectCom.value = [groupId.value]
      selectText.value = name
      defaultSelectIndex.value = [groupId.value.findIndex(item => item.text == levelValue.value)]
      isShowSelect.value = true
    }
  }
  const handleCalendarConfirm = (e) => {
    if (current.value == 0) {
      dateValue.value = e.fulldate
    } else {
      dateValue.value = `${e.range.before} ~ ${e.range.after}`
    }
    changeDate()
  }

  const changeDate = () => {
    if (current.value == 0) {
      queryInfo.value.startTime = dateValue.value
      queryInfo.value.endTime = dateValue.value + ' 23:59:59'
    } else {
      let date = dateValue.value.split(' ~ ')
      queryInfo.value.startTime = date[0]
      queryInfo.value.endTime = date[1] + ' 23:59:59'
    }
  }
  changeDate()

  const defaultSelectIndex = ref([0])
  const isShowSelect = ref(false)
  const deviceValue = ref()
  if (isGroup.value) deviceValue.value = groupId.value[0].text
  const selectText = ref('state')
  const stateValue = ref(uni.$t('全部'))
  const levelValue = ref(uni.$t('全部'))
  const selectCom = ref([])
  const stateOptions = ref([{
      text: uni.$t('全部'),
      value: undefined
    },
    {
      text: uni.$t('已处理'),
      value: 1
    },
    {
      text: uni.$t('未处理'),
      value: 2
    },
  ])
  const levelOptions = ref([{
      text: uni.$t('全部'),
      value: undefined
    },
    {
      text: uni.$t('等级一'),
      value: 1
    },
    {
      text: uni.$t('等级二'),
      value: 2
    },
  ])
  const handleSelectConfirm = ({
    value
  }) => {
    if (selectText.value == 'state') {
      stateValue.value = value[0].text
      queryInfo.value.state = value[0].value
    } else if (selectText.value == 'level') {
      levelValue.value = value[0].text
      queryInfo.value.alarmLevel = value[0].value
    } else if (selectText.value == 'device') {
      deviceValue.value = value[0].text
      queryInfo.value.ac = value[0].value
    }
    isShowSelect.value = false
  }
  const handleSelectCancel = () => {
    isShowSelect.value = false
  }
  const handleOperClick = (type) => {
    if (isGroup.value) {
      let data = groupId.value.find(item => item.value == queryInfo.value.ac)
      showValue.value = data ? data.text : ''
    } else {
      if (current.value == 0) {
        showValue.value = dateValue.value
      } else if (current.value == 1) {
        showValue.value = dayjsFn(dateValue.value.split(' ~ ')[0]).format('YYYY-MM')
      } else if (current.value == 2) {
        showValue.value = dayjsFn(dateValue.value.split(' ~ ')[0]).format('YYYY')
      }
    }
    if (type == 'confirm') proxy.$refs.paging.reload()
    proxy.$refs.dropDownPopup.close()
  }

  const handleItemClick = (item) => {
    uni.navigateTo({
      url: `/pages/common/alarm-detail?id=${item.alarmNameId}`
    })
  }
</script>

<style scoped lang="scss">
  .device-alarm {
    height: 100%;

    .alarm-top {
      border-bottom: 1px solid $uni-bg-color-grey;
      padding-bottom: 20rpx;
      padding-top: 10rpx;
      background-color: $uni-bg-color;
      padding: 20rpx 30rpx;
      margin: 20rpx 30rpx;
      border-radius: $uni-border-radius-lg;
    }

    .alarm-item {
      margin: 20rpx 0;
      border-radius: $uni-border-radius-lg;
      background-color: $uni-bg-color;
      padding: 20rpx 30rpx;
      margin: 20rpx 30rpx;
      line-height: 40rpx;

      .item-top {
        border-bottom: 1px solid $uni-bg-color-grey;
        padding-bottom: 20rpx;
        margin-bottom: 20rpx;
      }

      .item-solve {
        .img-status {
          width: 38rpx;
          height: 38rpx;
          margin-right: 6rpx;
        }
      }

      .item-ti {
        font-weight: bold;
      }

      .item-bo {
        border-top: 1px solid $uni-bg-color-grey;
        padding-top: 20rpx;
        margin-top: 20rpx;
      }

      .item-time {
        font-size: 12px;
        color: $uni-text-color-grey;
      }
    }

    .alarm-item:first-child {
      margin-top: 0;
    }

    .alarm-item:last-child {
      margin-bottom: 0;
    }
  }

  .select-popup {
    width: 750rpx;
    height: 249px;
    background-color: #fff;
    padding-top: 20rpx;
    position: relative;
    border-bottom-left-radius: 40rpx;
    border-bottom-right-radius: 40rpx;

    .popup-oper {
      width: 100%;
      height: 88rpx;
      text-align: center;
      position: absolute;
      bottom: 0;

      .oper-item {
        width: 50%;
        height: 100%;
        line-height: 88rpx;
      }

      .oper-item:first-child {
        color: $uni-text-color-grey;
      }

      .oper-item:last-child {
        border-bottom-right-radius: 40rpx;
        color: #fff;
        background-color: $u-primary;
      }
    }
  }

  :deep(.u-slide-right-enter-active) {
    width: 50%;
  }

  :deep(.uv-drop-down) {
    border: none;
  }

  :deep(.uv-icon__icon) {
    font-size: 14px !important;
  }

  :deep(.uv-calendar) {
    .uv-popup {
      z-index: 999 !important;
    }
  }

  :deep(.u-cell__body) {
    padding: 20rpx 60rpx;
  }

  :deep(.uv-dp__container) {
    background-color: rgba(0, 0, 0, 0);
  }
</style>