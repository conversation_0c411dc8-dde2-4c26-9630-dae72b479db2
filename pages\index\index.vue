<template>
  <view class="home">
    <z-paging ref="paging" v-model="tableData" show-refresher-update-time refresher-update-time-key="home"
      @query="queryList" hide-empty-view>
      <u-navbar :title="$t('home.title')" :bgColor="tabbarBack" :titleStyle="{
      color: '#000'
    }">
      </u-navbar>
      <view class="home-top">
        <view class="mb-20">
          <u-count-to fontSize="22px" bold :start-val="0"
            :end-val="formatThan10WFn(sumData.dischargeCapacityCalculateSum).num" separator="," :decimals="2"
            :duration="2000" :useEasing="false"></u-count-to>
          <span class="top-unit">{{ formatThan10WFn(sumData.dischargeCapacityCalculateSum).unit }}</span>
        </view>
        <view class="top-text">{{ $t('累计放电量') }}</view>
      </view>
      <view class="home-data">
        <view class="data-items">
          <view class="data-item">
            <view>
              <span class="item-num">{{ formatThan10WFn(sumData.chargeCapacityCalculateSum).num }}</span>
              <span class="color-grey ml-5"
                style="font-size: 9px;">{{ formatThan10WFn(sumData.chargeCapacityCalculateSum).unit }}</span>
            </view>
            <view class="item-text u-line-2">{{ $t('累计充电量') }}</view>
          </view>
          <view class="line"></view>
          <view class="data-item">
            <view>
              <span class="item-num">{{ formatThan10WFn(sumData.photovoltaicPowerCapacityCalculateSum).num }}</span>
              <span class="color-grey ml-5"
                style="font-size: 9px;">{{ formatThan10WFn(sumData.photovoltaicPowerCapacityCalculateSum).unit }}</span>
            </view>
            <view class="item-text u-line-2">{{ $t('光伏发电量') }}</view>
          </view>
          <view class="line"></view>
          <view class="data-item">
            <swiper style="height: 40rpx;width: 100%;text-align: center;" :autoplay="true" :interval="3000">
              <swiper-item v-for="item in sumData.totalIncome" :key="item.currency">
                <span class="item-num">{{ item.income }}</span>
                <span class="color-grey ml-5" style="font-size: 9px;">{{ item.currency }}</span>
              </swiper-item>
            </swiper>
            <view class="item-text u-line-2">{{ $t('总收益') }}</view>
          </view>
          <view class="data-item">
            <view>
              <span class="item-num">{{ sumData.deviceBatteryCapacitySum }}</span>
              <span class="color-grey ml-5" style="font-size: 9px;">kWh</span>
            </view>
            <view class="item-text u-line-2">{{ $t('总装机容量') }}</view>
          </view>
          <view class="line"></view>
          <view class="data-item">
            <view>
              <span class="item-num">{{ sumData.deviceRatedPowerSum }}</span>
              <span class="color-grey ml-5" style="font-size: 9px;">kW</span>
            </view>
            <view class="item-text u-line-2">{{ $t('总装机功率') }}</view>
          </view>
          <view class="line"></view>
          <view class="data-item">
            <view>
              <span class="item-num">{{ sumData.powerPlantSum }}</span>
              <!-- <span class="color-grey ml-5" style="font-size: 9px;">{{ $t('台') }}</span> -->
            </view>
            <view class="item-text u-line-2">{{ $t('项目总数') }}</view>
          </view>
        </view>
      </view>
      <view class="home-alarm flex justify-content align-items" @click="handleAlarm">
        <view class="alarm-le flex align-items">
          <image src="../../static/alarm.svg" class="alarm-img"></image>
          <swiper class="swiper" :vertical="true" :autoplay="true" :interval="3000">
            <swiper-item v-for="item in alarmData" :key="item.id">
              {{ item.alarmName }}
            </swiper-item>
          </swiper>
        </view>
        <image src="../../static/list.png" class="alarm-more"></image>
      </view>
      <view class="home-project">
        <view class="project-top flex justify-content align-items">
          <view style="font-weight: bold;" class="flex align-items">
            <image src="../../static/project-all.png" class="top-img"></image>
            <view>{{ $t('项目汇总') }}</view>
          </view>

          <image src="../../static/select.png" style="width: 34rpx;height: 34rpx;" @click="isShowPopup = true"></image>
        </view>
        <view class="project-items" v-if="tableData.length">
          <view class="project-item flex align-items" v-for="item in tableData" :key="item.projectId"
            @click="handleProjectClick(item)">
            <view class="item-le mr-20">
              <image src="../../static/project-item.png" mode="" class="le-img"></image>
            </view>
            <view class="item-ri">
              <view class="ri-title" :title="item.projectName">
                <u-text :lines="1" :text="item.projectName" bold size="12px"></u-text>
              </view>
              <view>{{ $t('设备') }}：<span class="ri-num">{{ item.countDevice }}</span>{{ $t('台') }}</view>
              <view>
                <u-text :lines="1" :text="`${$t('国家')}：${item.country}`" size="12px"></u-text>
              </view>
            </view>
          </view>
        </view>
        <view class="flex u-flex-center" style="width: 100%;padding: 100rpx 0 150rpx 0;" v-else>
          <u-empty icon="../../static/empty.webp" width="300" height="300" textSize="14px" :text="$t('暂无数据')">
          </u-empty>
        </view>
      </view>
    </z-paging>
  </view>
  <!-- 版本升级 -->
  <update theme="blue" :h5preview="true" :appstoreflag="true" :oldversion="nowVersion" ref="upgradeRef"
    :autocheckupdate="true" :noticeflag="false"></update>


  <u-popup :show="isShowPopup" mode="right" @close="closePopup" @open="openPopup">
    <view class="popup-wrapper">
      <view class="popup-wrapper-item" v-for="item in fieldOptions" :key="item.value">
        <view class="popup-wrapper-item-title">
          <view class="flex u-flex-items-center">
            <view style="font-size: 12px;margin-right: 6rpx;">{{ item.text }}</view>
          </view>
        </view>
        <view class="popup-wrapper-item-input"
          :style="{'border-bottom': item.value == 'nickName'? 'none': '1px solid #f8f8f8'}">
          <u--input v-model="queryInfo[item.value]" :customStyle="{
                        borderRadius: '100px',
                        height: '50rpx',
                      }" :placeholderStyle="`{
                        fontSize: '12px'
                      }`" shape="circle" :placeholder="$t('请输入')" :adjustPosition="false"></u--input>
        </view>
      </view>
      <view class="popup-wrapper-oper flex u-flex-center">
        <u-button size="small" class="mr-20" @click="handlePopupResetClick">{{ $t('重置') }}</u-button>
        <u-button type="primary" size="small" @click="handlePopupConfirmClick">{{ $t('确定') }}</u-button>
      </view>
    </view>
  </u-popup>
</template>

<script setup>
  import {
    computed,
    ref,
    getCurrentInstance
  } from 'vue'
  import {
    onPageScroll,
    onReachBottom,
    onShow
  } from '@dcloudio/uni-app'
  import {
    dayjsFn
  } from '@/common/utils.js'
  import {
    getSum
  } from '@/api/home.js'
  import {
    projectList
  } from '@/api/project.js'
  import {
    alarmList
  } from '@/api/alarm.js'
  import otherColor from '../../common/other.module.scss'
  import Update from '@/components/upgrade/upgrade.vue'

  /**
   * 获取统计
   */
  const sumData = ref({
    chargeCapacityCalculateSum: 0,
    deviceBatteryCapacitySum: 0,
    deviceRatedPowerSum: 0,
    deviceSum: 0,
    dischargeCapacityCalculateSum: 0,
    photovoltaicPowerCapacityCalculateSum: 0,
    powerPlantSum: 0,
    totalIncome: 0,
  })
  const getSumFn = async () => {
    const res = await getSum()
    let data = res.data
    if (!data.totalIncome?.length) data.totalIncome.push({
      currency: uni.$t('元'),
      income: 0
    })
    data.totalIncome.forEach(item => {
      item.income = item.income.toFixed(2)
    })
    for (const key in data) {
      if (key != 'deviceSum' && key !== 'totalIncome' && key !== 'powerPlantSum') data[key] = data[key] !== null ?
        data[key].toFixed(2) : data[
          key]
    }
    sumData.value = data
  }
  // getSumFn()
  const formatThan10WFn = (num) => {
    let isThan10W = num > 100000
    if (!isThan10W) return {
      num,
      unit: 'kWh'
    }
    num = (num / 1000).toFixed(2)
    return {
      num,
      unit: 'MWh'
    }
  }

  /**
   * 获取项目
   */
  const handleProjectClick = (item) => {
    uni.navigateTo({
      url: `/pages/property/project/project-detail?id=${item.projectId}`
    })
  }
  const tableData = ref([])
  const {
    proxy
  } = getCurrentInstance()
  const paging = ref({
    pageNum: 1,
    pageSize: 10,
    total: 0
  })
  const queryInfo = ref({
    projectName: undefined
  })
  const projectListFn1 = async ({
    pageNum,
    pageSize
  }) => {
    const res = await projectList({
      pageNum,
      pageSize,
      ...queryInfo.value
    })
    paging.value.total = res.total
    return res.rows
  }


  const queryList = (pageNo, pageSize) => {
    // 组件加载时会自动触发此方法，因此默认页面加载时会自动触发，无需手动调用
    // 这里的pageNo和pageSize会自动计算好，直接传给服务器即可
    // 模拟请求服务器获取分页数据，请替换成自己的网络请求
    projectListFn1({
      pageNum: pageNo,
      pageSize
    }).then(res => {
      // 将请求的结果数组传递给z-paging
      proxy.$refs.paging.completeByTotal(res, paging.value.total)
    }).catch(res => {
      // 如果请求失败写this.$refs.paging.complete(false);
      // 注意，每次都需要在catch中写这句话很麻烦，z-paging提供了方案可以全局统一处理
      // 在底层的网络请求抛出异常时，写uni.$emit('z-paging-error-emit');即可
      proxy.$refs.paging.complete(false);
    })
  }
  /**
   * 滚动tabbar
   */
  const tabbarBack = ref('rgba(0,0,0,0)')
  onPageScroll((e) => {
    tabbarBack.value = e.scrollTop == 0 ? 'rgba(0,0,0,0)' : otherColor.bgColor
  })
  /**
   * 获取告警
   */
  const alarmData = ref([])
  const alarmListFn = async () => {
    const res = await alarmList({
      pageNum: 1,
      pageSize: 10,
      startTime: dayjsFn().startOf('month').format('YYYY-MM-DD'),
      endTime: dayjsFn().endOf('month').format('YYYY-MM-DD') + ' 23:59:59'
    })
    let data = res.rows
    if (data.length) {
      alarmData.value = res.rows
    } else {
      alarmData.value = [{
        id: 1,
        alarmName: uni.$t('暂无告警')
      }]
    }
  }

  const windowInfo = ref()
  const safeTop = ref()
  const safeBottom = ref()
  onShow(() => {
    getSumFn()
    alarmListFn()
    windowInfo.value = uni.getWindowInfo()
    safeTop.value = windowInfo.value.statusBarHeight + 30 + 'rpx'
    // #ifdef APP-PLUS
    safeBottom.value = (windowInfo.value.windowBottom * 2) + 30 + 'rpx'
    // #endif
    // #ifdef H5
    safeBottom.value = 30 + 'rpx'
    // #endif
  })

  /**
   * 告警列表
   */
  const handleAlarm = () => {
    uni.navigateTo({
      url: '/pages/index/alarm-list'
    })
  }

  /**
   * 版本升级
   */
  const nowVersion = ref()
  // #ifdef APP-PLUS
  nowVersion.value = uni.$u.sys().appWgtVersion
  // #endif
  // #ifdef H5
  nowVersion.value = uni.$u.sys().appVersion
  // #endif
  const showupdatetips = (flag) => {
    if (flag == 0) {
      uni.showToast({
        title: uni.$t('已经是最新版本了'),
        icon: 'none'
      });
    }
  }
  const setMenu = () => {
    let routes = uni.cache.getItem('routes')
    if (!routes) return
    if (routes?.findIndex(item => item.name == 'Operation') == -1) {
      uni.setTabBarItem({
        index: 3,
        text: uni.$i18n().t('运维'),
        visible: false,
        iconPath: '/static/icon4.png',
        selectedIconPath: '/static/icon4-a.png',
        success: () => {
          console.log('成功隐藏')
        },
        fail: (err) => {
          console.log(err)
        }
      })
    } else {
      uni.setTabBarItem({
        index: 3,
        text: uni.$i18n().t('运维'),
        visible: true,
        iconPath: '/static/icon4.png',
        selectedIconPath: '/static/icon4-a.png',
        success: () => {
          console.log('成功展示')
        },
        fail: (err) => {
          console.log(err)
        }
      })
    }
  }
  setMenu()

  /**
   * 弹出
   */
  const isShowPopup = ref(false)
  const closePopup = () => {
    uni.showTabBar()
    isShowPopup.value = false
  }
  const openPopup = () => {
    isShowPopup.value = true
    uni.hideTabBar()
  }
  const fieldOptions = ref([{
      text: uni.$t('项目名称'),
      value: 'projectName',
    },
    {
      text: uni.$t('国家'),
      value: 'country',
    },
    {
      text: uni.$t('创建人员'),
      value: 'nickName',
    }
  ])
  const handlePopupConfirmClick = () => {
    proxy.$refs.paging.reload()
    isShowPopup.value = false
    uni.showTabBar()
  }
  const handlePopupResetClick = () => {
    queryInfo.value = {}
    proxy.$refs.paging.reload()
    isShowPopup.value = false
    uni.showTabBar()
  }
</script>

<style scoped lang="scss">
  .home {
    position: relative;
    overflow: auto;

    .home-top {
      background: rgba(0, 0, 0, 0);
      height: 550rpx;
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      font-size: 24px;
      text-align: center;

      view {
        width: 100%;
      }

      .top-text {
        font-size: 14px;
        margin-top: -8rpx;
      }

      .top-unit {
        font-size: 12px;
        margin-left: 4rpx;
      }
    }

    .home-data {
      position: absolute;
      top: 360rpx;
      height: 400rpx;
      width: 100%;
      padding: 30rpx;

      .data-items {
        background-color: $uni-bg-color;
        height: 100%;
        border-radius: 30rpx;
        display: flex;
        align-items: center;
        justify-content: space-evenly;
        align-content: center;
        flex-wrap: wrap;

        .data-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          width: 30%;
          height: 40%;
          font-size: 12px;
        }

        .item-num {
          font-size: 14px;
          font-weight: bold;
        }

        .item-text {
          color: $uni-text-color-grey;
          margin-top: 10rpx;
          height: 64rpx;
        }

        .line {
          width: 1px;
          height: 60rpx;
          background-color: #dcdfe6;
        }
      }
    }

    .home-alarm {
      margin: 0 30rpx;
      margin-top: 200rpx;
      background-color: $uni-bg-color;
      border-radius: 30rpx;
      padding: 18rpx 30rpx;

      .alarm-le {
        flex: 1;
      }

      .alarm-img {
        width: 38rpx;
        height: 38rpx;
        margin-right: 8rpx;
      }

      .alarm-more {
        width: 36rpx;
        height: 38rpx;
      }

      .swiper {
        height: 40rpx;
        line-height: 48rpx !important;
        flex: 1;
      }
    }

    .home-project {
      display: flex;
      justify-content: space-between;
      background-color: $uni-bg-color;
      flex-wrap: wrap;
      margin: 20rpx 30rpx;
      padding: 20rpx 30rpx;
      border-radius: 30rpx;
      padding-bottom: 0;
      margin-bottom: 0;

      .project-top {
        width: 100%;

        .top-img {
          width: 32rpx;
          height: 32rpx;
          margin-right: 14rpx;
        }
      }

      .project-items {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        width: 100%;
        margin-top: 20rpx;
      }

      .project-item {
        background-color: $uni-bg-color-grey;
        padding: 16rpx;
        border-radius: 20rpx;
        font-size: 12px;
        margin-bottom: 20rpx;
        line-height: 40rpx;
        width: 48%;

        .item-le {
          flex: 1;
          position: relative;

          .le-img {
            width: 80rpx;
            height: 70rpx;
            margin-top: 10rpx;
          }
        }

        .item-ri {
          flex: 4;

          .ri-title {
            font-size: 14px;
            font-weight: bold;
          }

          .ri-num {
            font-weight: bold;
            margin-right: 4rpx;
          }
        }
      }
    }
  }

  .popup-wrapper {
    width: 50vw;
    padding: 30rpx;
    position: relative;
    padding-top: v-bind(safeTop);
    height: 100%;

    &-title {
      font-weight: 600;
    }

    &-item {
      margin: 20rpx 0;

      &-title {
        font-size: 12px;
        margin-left: 6rpx;
      }

      &-input {
        margin-top: 20rpx;
        border-bottom: 1px solid #f8f8f8;
        padding-bottom: 10px;
      }
    }

    &-oper {
      width: calc(100% - 60rpx);
      position: absolute;
      bottom: v-bind(safeBottom);
    }
  }

  :deep(.u-navbar__content) {
    .u-navbar__content__left {
      display: none;
    }
  }

  :deep(.u-count-num) {
    color: $main-color !important;
  }

  :deep(.u-input) {
    flex: none
  }
</style>