// 获取列表
export const jfpgList = (queryInfo) => uni.$u.http.get('/system/cutTop/list', {
  params: queryInfo
})

// 新增
export const addJfpg = (data) => uni.$u.http.post('/system/cutTop', data)

// 修改
export const editJfpg = (data) => uni.$u.http.put('/system/cutTop', data)

// 获取全部数据
export const allJfpg = (queryInfo) => uni.$u.http.get('/system/cutTop/getCutTops', {
  params: queryInfo
})

// 删除
export const deleteJfpg = (queryInfo) => uni.$u.http.delete(`/system/cutTop/${queryInfo.cutTopIds}`)

// 查看
export const lookJfpg = (queryInfo) => uni.$u.http.get(`/system/cutTop/${queryInfo.id}`)