<template>
  <view class="add-box">
    <u-navbar leftText="" :title="title" bgColor="#fff" leftIconSize="20px" :autoBack="true" :placeholder="true">
    </u-navbar>
    <u-form labelPosition="left" :model="form" :rules="rules" ref="formRef" labelWidth="auto">
      <view class="add-item">
        <u-form-item :label="$t('方案名称')" prop="name" :borderBottom="true">
          <u-input v-model="form.name" :placeholder="$t('请输入方案名称')" border="none"></u-input>
        </u-form-item>
        <u-form-item :label="$t('买卖电是否同价')" prop="buySell">
          <u-radio-group v-model="form.buySell" placement="row" size="16px"
            style="display: flex;justify-content: flex-end;">
            <u-radio :label="$t('是')" :name="0" class="mr-20"></u-radio>
            <u-radio :label="$t('否')" :name="1"></u-radio>
          </u-radio-group>
        </u-form-item>
      </view>
      <view class="flex u-flex-between u-flex-items-center u-m-t-20 u-m-b-20">
        <view style="font-size: 14px;font-weight: bold;">{{ $t('费率') }}</view>
      </view>
      <view class="add-item mb-20">
        <template v-for="(item, index) in form.rateList" :key="index + 'rate'">
          <u-form-item :label="$t('费率') + (index + 1)" :prop="`rateList.${index}.value`"
            :borderBottom="index == (form.rateList.length - 1) ? false: true" :rules="[{
            type: 'string',  
            required: true,  
            message: $t('请输入'),  
            trigger: ['blur', 'change'],  
          }]">
            <u-input v-model="item.value" :placeholder="$t('请输入')" border="none"
              @update:modelValue="handleRateChange"></u-input>
          </u-form-item>
        </template>
      </view>
      <template v-if="form.buySell == 0">
        <view class="flex u-flex-between u-flex-items-center u-m-t-20 u-m-b-20">
          <view style="font-size: 14px;font-weight: bold;">{{ $t('时段信息') }}</view>
          <view class="flex">
            <u-button type="primary" :plain="true" :text="$t('添加')" shape="circle" class="u-m-r-20 btn"
              @click="handleAddTimeClick('pointList1')"></u-button>
            <u-button type="error" :plain="true" :text="$t('删除')" shape="circle" class="btn"
              @click="handleDeleteTimeClick('pointList1')"></u-button>
          </view>
        </view>
        <view class="add-item mb-20" v-for="(item, index) in form.pointList1" :key="index + 'time'">
          <u-form-item :label="$t('开始时间')" :prop="`pointList1.${index}.startTime`" :borderBottom="true" :rules="[{  
          type: 'string',  
          required: true,  
          message: $t('请选择开始时间'),  
          trigger: ['blur', 'change'],  
        }]">
            <u-datetime-picker hasInput :show="showTime" v-model="item.startTime" mode="time"
              itemHeight="88"></u-datetime-picker>
          </u-form-item>
          <u-form-item :label="$t('结束时间')" :prop="`pointList1.${index}.endTime`" :borderBottom="true" :rules="[{
            		  type: 'string',  
            		  required: true,  
            		  message: $t('请选择结束时间'),  
            		  trigger: ['blur', 'change'],  
            		}]">
            <u-datetime-picker hasInput :show="showTime" v-model="item.endTime" mode="time"
              itemHeight="88"></u-datetime-picker>
          </u-form-item>
          <u-form-item :label="$t('费率')" :prop="`pointList1.${index}.type`" :borderBottom="true" :rules="[{
            		  type: 'number', 
            		  message: $t('请选择费率'),  
            		  trigger: ['blur', 'change'],  
            		}]" class="rate">
            <view v-if="item.type !== undefined" @click="handleCellClick(item.type, index, 'pointList1')">
              {{ form.rateList.find(item1 => item1.num == item.type).label }}
            </view>
            <u-input v-model="item.type" :placeholder="$t('请选择费率')" border="none"
              @focus="handleCellClick(item.type, index, 'pointList1')" v-else></u-input>
          </u-form-item>
          <u-form-item :label="$t('电价')" :prop="`pointList1.${index}.price`" borderBottom :rules="[{
                  type: 'string',
            		  required: true,  
            		  message: $t('电价'),  
            		  trigger: ['blur', 'change'],  
            		}]">
            <u-input v-model="item.price" :placeholder="$t('电价')" border="none" readonly></u-input>
          </u-form-item>
        </view>
      </template>
      <template v-else>
        <view class="flex u-flex-between u-flex-items-center u-m-t-20 u-m-b-20">
          <view style="font-size: 14px;font-weight: bold;">{{ $t('买电') }}</view>
          <view class="flex">
            <u-button type="primary" :plain="true" :text="$t('添加')" shape="circle" class="u-m-r-20 btn"
              @click="handleAddTimeClick('pointList')"></u-button>
            <u-button type="error" :plain="true" :text="$t('删除')" shape="circle" class="btn"
              @click="handleDeleteTimeClick('pointList')"></u-button>
          </view>
        </view>
        <view class="add-item mb-20" v-for="(item, index) in form.pointList" :key="index + 'time'">
          <u-form-item :label="$t('开始时间')" :prop="`pointList.${index}.startTime`" :borderBottom="true" :rules="[{  
          type: 'string',  
          required: true,  
          message: $t('请选择开始时间'),  
          trigger: ['blur', 'change'],  
        }]">
            <u-datetime-picker hasInput :show="showTime" v-model="item.startTime" mode="time"
              itemHeight="88"></u-datetime-picker>
          </u-form-item>
          <u-form-item :label="$t('结束时间')" :prop="`pointList.${index}.endTime`" :borderBottom="true" :rules="[{
            		  type: 'string',  
            		  required: true,  
            		  message: $t('请选择结束时间'),  
            		  trigger: ['blur', 'change'],  
            		}]">
            <u-datetime-picker hasInput :show="showTime" v-model="item.endTime" mode="time"
              itemHeight="88"></u-datetime-picker>
          </u-form-item>
          <u-form-item :label="$t('费率')" :prop="`pointList.${index}.type`" :borderBottom="true" :rules="[{
            		  type: 'number', 
            		  message: $t('请选择费率'),  
            		  trigger: ['blur', 'change'],  
            		}]" class="rate">
            <view v-if="item.type !== undefined" @click="handleCellClick(item.type, index, 'pointList')">
              {{ form.rateList.find(item1 => item1.num == item.type).label }}
            </view>
            <u-input v-model="item.type" :placeholder="$t('请选择费率')" border="none"
              @focus="handleCellClick(item.type, index, 'pointList')" v-else></u-input>
          </u-form-item>
          <u-form-item :label="$t('电价')" :prop="`pointList.${index}.price`" borderBottom :rules="[{
                  type: 'string',
            		  required: true,  
            		  message: $t('电价'),  
            		  trigger: ['blur', 'change'],  
            		}]">
            <u-input v-model="item.price" :placeholder="$t('电价')" border="none" readonly></u-input>
          </u-form-item>
        </view>
        <view class="flex u-flex-between u-flex-items-center u-m-t-20 u-m-b-20">
          <view style="font-size: 14px;font-weight: bold;">{{ $t('卖电') }}</view>
          <view class="flex">
            <u-button type="primary" :plain="true" :text="$t('添加')" shape="circle" class="u-m-r-20 btn"
              @click="handleAddTimeClick('sellPointList')"></u-button>
            <u-button type="error" :plain="true" :text="$t('删除')" shape="circle" class="btn"
              @click="handleDeleteTimeClick('sellPointList')"></u-button>
          </view>
        </view>
        <view class="add-item mb-20" v-for="(item, index) in form.sellPointList" :key="index + 'time'">
          <u-form-item :label="$t('开始时间')" :prop="`sellPointList.${index}.startTime`" :borderBottom="true" :rules="[{  
          type: 'string',  
          required: true,  
          message: $t('请选择开始时间'),  
          trigger: ['blur', 'change'],  
        }]">
            <u-datetime-picker hasInput :show="showTime" v-model="item.startTime" mode="time"
              itemHeight="88"></u-datetime-picker>
          </u-form-item>
          <u-form-item :label="$t('结束时间')" :prop="`sellPointList.${index}.endTime`" :borderBottom="true" :rules="[{
            		  type: 'string',  
            		  required: true,  
            		  message: $t('请选择结束时间'),  
            		  trigger: ['blur', 'change'],  
            		}]">
            <u-datetime-picker hasInput :show="showTime" v-model="item.endTime" mode="time"
              itemHeight="88"></u-datetime-picker>
          </u-form-item>
          <u-form-item :label="$t('费率')" :prop="`sellPointList.${index}.type`" :borderBottom="true" :rules="[{
            		  type: 'number', 
            		  message: $t('请选择费率'),  
            		  trigger: ['blur', 'change'],  
            		}]" class="rate">
            <view v-if="item.type !== undefined" @click="handleCellClick(item.type, index, 'sellPointList')">
              {{ form.rateList.find(item1 => item1.num == item.type).label }}
            </view>
            <u-input v-model="item.type" :placeholder="$t('请选择费率')" border="none"
              @focus="handleCellClick(item.type, index, 'sellPointList')" v-else></u-input>
          </u-form-item>
          <u-form-item :label="$t('电价')" :prop="`sellPointList.${index}.price`" borderBottom :rules="[{
                  type: 'string',
            		  required: true,  
            		  message: $t('电价'),  
            		  trigger: ['blur', 'change'],  
            		}]">
            <u-input v-model="item.price" :placeholder="$t('电价')" border="none" readonly></u-input>
          </u-form-item>
        </view>
      </template>
    </u-form>
    <u-button type="primary" style="border-radius: 100rpx;" class="u-m-t-40"
      @click="handleConfirmClick">{{ $t('确认') }}</u-button>
    <view class="ft12 u-m-t-20 color-grey" style="width: 100%;text-align: center;">{{ $t('注：电价：电价货币为项目绑定的货币为准，如项目绑定货币为美元，那么电价为0.2美元') }}</view>

    <u-picker :show="isShowSelect" :defaultIndex="defaultSelectIndex" keyName="label" :columns="selectCom"
      itemHeight="88" @confirm="handleSelectConfirm" :closeOnClickOverlay="true" @close="handleSelectCancel"
      @cancel="handleSelectCancel" :cancelText="$t('取消')" :confirmText="$t('确认')"></u-picker>
  </view>
</template>

<script setup>
  import {
    ref,
    computed
  } from 'vue'
  import {
    onLoad,
    onShow
  } from '@dcloudio/uni-app'
  import {
    addPrice,
    lookPrice,
    editPrice
  } from '@/api/price'

  const title = ref(uni.$t('添加分时电价'))
  const id = ref()
  onLoad((options) => {
    if (options.id) {
      id.value = options.id
      title.value = uni.$t('修改分时电价')
    } else {
      title.value = uni.$t('添加分时电价')
    }
  })
  onShow(() => {
    if (title.value == uni.$t('修改分时电价')) getInfoFn()
  })

  const rateList = ref([{
      label: uni.$t('费率一'),
      num: 0,
      value: undefined
    },
    {
      label: uni.$t('费率二'),
      num: 1,
      value: undefined
    },
    {
      label: uni.$t('费率三'),
      num: 2,
      value: undefined
    },
    {
      label: uni.$t('费率四'),
      num: 3,
      value: undefined
    },
    {
      label: uni.$t('费率五'),
      num: 4,
      value: undefined
    },
  ])
  const rateTextList = ref([uni.$t('费率一'), uni.$t('费率二'), uni.$t('费率三'), uni.$t('费率四'), uni.$t('费率五')])
  const getInfoFn = async () => {
    const res = await lookPrice({
      id: id.value
    })
    let data = res.data[0]
    if (data.buySell == 0) {
      data.pointList1 = data.pointList
      data.sellPointList = [{
        enable: 0,
        endTime: '',
        price: undefined,
        startTime: '',
        type: undefined
      }]
    } else {
      data.pointList1 = [{
        enable: 0,
        endTime: '',
        price: undefined,
        startTime: '',
        type: undefined
      }]
      data.sellPointList = data.sellPointList ? data.sellPointList : [{
        enable: 0,
        endTime: '',
        price: undefined,
        startTime: '',
        type: undefined
      }]
    }
    form.value = {
      ...data,
      rateList: data.rateList?.length ? data.rateList.map(item => {
        item.label = rateTextList.value.find((text, index) => index == item.num)
        return item
      }) : JSON.parse(JSON.stringify(rateList.value))
    }
  }

  const formRef = ref(null)
  const form = ref({
    name: '',
    buySell: 0,
    pointList1: [{
      enable: 0,
      endTime: '',
      price: undefined,
      startTime: '',
      type: undefined
    }],
    pointList: [{
      enable: 0,
      endTime: '',
      price: undefined,
      startTime: '',
      type: undefined
    }],
    sellPointList: [{
      enable: 0,
      endTime: '',
      price: undefined,
      startTime: '',
      type: undefined
    }],
    rateList: [{
        label: uni.$t('费率一'),
        num: 0,
        value: undefined
      },
      {
        label: uni.$t('费率二'),
        num: 1,
        value: undefined
      },
      {
        label: uni.$t('费率三'),
        num: 2,
        value: undefined
      },
      {
        label: uni.$t('费率四'),
        num: 3,
        value: undefined
      },
      {
        label: uni.$t('费率五'),
        num: 4,
        value: undefined
      },
    ]
  })
  const rules = ref({
    'title': {
      type: 'string',
      required: true,
      message: uni.$t('请输入方案名称'),
      trigger: ['blur', 'change'],
    },
  })

  /**
   * 选择费率
   */
  /**
   * 选择
   */
  const isShowSelect = ref(false)
  const defaultSelectIndex = ref([0])
  const selectTypeObj = ref({
    text: 'pointList1',
    type: 0,
    index: 0
  })
  const selectCom = computed(() => [form.value.rateList.filter(item => item.value)])
  const handleSelectConfirm = ({
    value
  }) => {
    form.value[selectTypeObj.value.text][selectTypeObj.value.index].num = value[0].num
    form.value[selectTypeObj.value.text][selectTypeObj.value.index].type = value[0].num
    form.value[selectTypeObj.value.text][selectTypeObj.value.index].price = value[0].value
    isShowSelect.value = false
  }
  const handleSelectCancel = () => {
    isShowSelect.value = false
  }
  const getPriceText = computed(() => {
    return (num) => {
      return form.value.rateList.find(item => item.num == num)?.value
    }
  })
  const handleCellClick = (type, index, formText) => {
    selectTypeObj.value = {
      text: formText,
      type,
      index
    }
    let defaultIndex = form.value.rateList.findIndex(item => item.num == type)
    defaultSelectIndex.value = defaultIndex == -1 ? [0] : [defaultIndex]
    if (form.value.buySell == 0) {
      form.value.pointList1[index].price = getPriceText.value(type)
    } else {
      form.value[formText][index].price = getPriceText.value(type)
    }
    isShowSelect.value = true
  }
  const handleRateChange = (value) => {
    if (form.value.buySell == 0) {
      form.value.pointList1.forEach(item => {
        item.price = getPriceText.value(item.type)
      })
    } else {
      form.value.pointList.forEach(item => {
        item.price = getPriceText.value(item.type)
      })
      form.value.sellPointList.forEach(item => {
        item.price = getPriceText.value(item.type)
      })
    }
  }
  /**
   * 时间
   */
  const showTime = ref(false)
  const handleAddTimeClick = (type) => {
    if (form.value[type].length == 12) return uni.$u.toast(uni.$t('最多只能添加12条哦'))
    form.value[type].push({
      enable: 0,
      endTime: '',
      price: undefined,
      startTime: '',
      type: undefined
    })
  }
  const handleDeleteTimeClick = (type) => {
    if (form.value[type].length == 1) return uni.$u.toast(uni.$t('至少要有一条哦'))
    form.value[type].pop()
  }

  const handleConfirmClick = () => {
    if (formRef.value) {
      formRef.value.validate().then(valid => {
        if (valid) {
          if (title.value == uni.$t('添加分时电价')) {
            let pointList = form.value.buySell == 0 ? form.value.pointList1 : form.value.pointList
            addPrice({
              name: form.value.name,
              countryCurrencyId: form.value.countryCurrencyId,
              buySell: form.value.buySell,
              sellPointList: form.value.sellPointList,
              pointList,
              rateList: form.value.rateList
            }).then(res => {
              uni.$u.toast(uni.$t('添加成功'))
              uni.navigateBack()
            }).catch(() => {
              uni.$u.toast(uni.$t('添加失败'))
            })
          } else {
            editPrice({
              title: form.value.title,
              pointList: form.value.pointList,
              id: form.value.id
            }).then(res => {
              uni.$u.toast(uni.$t('修改成功'))
              uni.navigateBack()
            }).catch(() => {
              uni.$u.toast(uni.$t('修改失败'))
            })
            let pointList = form.value.buySell == 0 ? form.value.pointList1 : form.value.pointList
            let sellPointList = form.value.buySell == 0 ? [] : form.value.sellPointList
            editPrice({
              name: form.value.name,
              countryCurrencyId: form.value.countryCurrencyId,
              buySell: form.value.buySell,
              sellPointList,
              pointList,
              rateList: form.value.rateList,
              id: form.value.id
            }).then(res => {
              uni.$u.toast(uni.$t('修改成功'))
              uni.navigateBack()
            }).catch(() => {
              uni.$u.toast(uni.$t('修改失败'))
            })
          }
        }
      })
    }
  }
</script>

<style lang="scss" scoped>
  .add-box {
    width: 100%;
    height: 100vh;
    padding: 20rpx 30rpx;
    overflow: auto;
  }

  .add-item {
    background-color: #fff;
    padding: 0 30rpx;
    border-radius: 6px;
  }

  .btn {
    height: 50rpx;
    background: rgba(0, 0, 0, 0);
  }

  :deep(.u-input__content__field-wrapper__field) {
    text-align: right !important;
  }

  :deep(.u-button__text) {
    font-size: 12px !important;
  }

  :deep(.u-datetime-picker) {
    .u-input {
      padding: 0 !important;
      border: none;
    }
  }

  :deep(.rate) {
    .u-form-item__body__right__content__slot {
      flex-direction: row-reverse !important;
    }
  }
</style>