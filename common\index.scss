page {
	width: 100%;
	font-size: 14px;
	background: $bg-color;
  color: $main-color;
  font-family: 'Arial', sans-serif, 'Source Han Sans';
}

view {
	box-sizing: border-box;
}

.ft12 {
  font-size: 12px
}
.ft10 {
  font-size: 10px
}
.ft8 {
  font-size: 8px
}

.color-grey {
	color: $uni-text-color-grey;
}

.primary {
	color: $uni-color-primary;
}

.error {
	color: $uni-color-error;
}

.ml-5 {
	margin-left: 5rpx;
}

.mr-20 {
	margin-right: 20rpx;
}

.mb-20 {
	margin-bottom: 20rpx;
}

.flex {
	display: flex;
}
.justify-content {
	justify-content: space-between;
}
.align-items {
	align-items: center !important;
}

.bold {
  font-weight: bold;
}

.copy-img-width {
	width: 30rpx;
	height: 30rpx;
}