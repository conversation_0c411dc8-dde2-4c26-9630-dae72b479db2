<template>
  <view class="flow-item u-flex u-flex-center align-items bold">
    <view class="item-le">
      <view class="le-gird flex align-items">
        <image src="../../../static/flow_ac.png" mode=""></image>
        <view class="ml-5">{{ flowData.power }} kW</view>
      </view>
      <view class="le-gird-line mb-20" :style="[lineStyle('power')]">
        <template v-if="showCircle('power')">
          <u-icon name="arrow-down-fill" size="20rpx" color="#fb560a" class="rightToGird"
            v-if="flowData.power < -1"></u-icon>
          <u-icon name="play-left-fill" size="20rpx" color="#fb560a" class="leftToGird"
            v-else-if="flowData.power > 1"></u-icon>
        </template>
      </view>
      <view class="le-load-line" :style="[lineStyle('load')]">
        <template v-if="showCircle('load')">
          <u-icon name="arrow-up-fill" size="20rpx" color="#fb560a" class="rightToLoad"
            v-if="flowData.load < -1"></u-icon>
          <u-icon name="play-left-fill" size="20rpx" color="#fb560a" class="leftToLoad"
            v-else-if="flowData.load > 1"></u-icon>
        </template>
      </view>
      <view class="le-load flex align-items">
        <image src="../../../static/flow_load.png" mode=""></image>
        <view class="ml-5">{{ flowData.load }} kW</view>
      </view>
    </view>
    <view class="item-ce">
      <image src="../../../static/flow_hmi.png"></image>
    </view>
    <view class="item-ri">
      <view class="ri-bus flex u-flex-column align-items">
        <image src="../../../static/flow_dc.png" mode=""></image>
        <view class="u-m-r-10">{{ flowData.bus }} kW</view>
      </view>
      <view class="ri-bus-line" :style="[lineStyle('bus')]">
        <template v-if="showCircle('bus')">
         <u-icon name="play-right-fill" size="20rpx" color="#fb560a" class="rightToBus"
            v-if="flowData.bus < -1"></u-icon>
          <u-icon name="play-left-fill" size="20rpx" color="#fb560a" class="leftToBus"
            v-else-if="flowData.bus > 1"></u-icon>
        </template>
      </view>
    </view>
  </view>
</template>

<script setup>
  import {
    ref,
    computed
  } from 'vue'
  import {
    useMonitorStore
  } from '@/store/monitor.js'

  const monitorStore = useMonitorStore()

  const deviceType = computed(() => monitorStore.routeQuery.type)
  const flowData = computed(() => {
    return monitorStore.flowData
  })
  const lineStyle = computed(() => (type) => {
    let color = showCircle.value(type) ? '#fb560a' : '#eeeeef'
    let borderColor = ''
    switch (type) {
      case 'power':
        borderColor = `transparent transparent ${color} ${color}`
        break;
      case 'load':
        borderColor = `${color} transparent transparent ${color}`
        break;
    }
    return {
      borderColor,
      backgroundColor: type == 'bus' ? color : ''
    }
  })
  // 流动拓补图展示圆
  const showCircle = computed(() => {
    let control = monitorStore.control
    let flowData = monitorStore.flowData
    return (name) => {
      if (!control) return false
      if (control.onLineState == '离线') return false
      if (-1 < flowData[name] && flowData[name] < 1) {
        return false
      } else if (flowData[name] == 0) {
        return false
      } else {
        return true
      }
    }
  })

  monitorStore.selectDynamicGraphFn({
    deviceSerialNumber: monitorStore.routeQuery.id,
    deviceType: monitorStore.routeQuery.type
  })
</script>

<style scoped lang="scss">
  image {
    width: 100%;
    height: 100%;
  }

  .flow-item {
    position: relative;
    height: 400rpx;
    font-size: 10px;

    .item-ce {
      width: 70rpx;
      height: 50rpx;
      z-index: 100;
      margin-top: 12rpx;
    }

    .item-le {
      position: relative;
      right: -18rpx;

      .le-gird {
        position: relative;
        left: -30rpx;
        top: 14rpx;
        z-index: 100;

        image {
          width: 80rpx;
          height: 100rpx;
        }
      }

      .le-gird-line {
        height: 80rpx;
        width: 250rpx;
        border: 4rpx solid;
        border-color: transparent transparent #fb560a #fb560a;
        border-radius: 0 20rpx;
        position: relative;

        .rightToGird {
          position: absolute;
          animation: rightToGird 3s linear infinite;
        }

        .leftToGird {
          position: absolute;
          animation: leftToGird 3s linear infinite;
        }

        @keyframes rightToGird {
          0% {
            top: -14rpx;
            left: -12rpx;
            transform: rotate(0);
          }

          47% {
            top: calc(100% - 40rpx);
            left: -12rpx;
            transform: rotate(0);
          }

          50% {
            top: calc(100% - 12rpx);
            left: -12rpx;
            transform: rotate(-45deg);
          }

          53% {
            top: calc(100% - 8rpx);
            left: 8rpx;
            transform: rotate(-90deg);
          }

          100% {
            top: calc(100% - 8rpx);
            left: calc(100% - 12rpx);
            transform: rotate(-90deg);
          }
        }

        @keyframes leftToGird {
          100% {
            top: -14rpx;
            left: -13rpx;
            transform: rotate(90deg);
          }

          53% {
            top: calc(100% - 48rpx);
            left: -13rpx;
            transform: rotate(90deg);
          }

          50% {
            top: calc(100% - 14rpx);
            left: -10rpx;
            transform: rotate(45deg);
          }

          47% {
            top: calc(100% - 7rpx);
            left: -10rpx;
            transform: rotate(0);
          }

          0% {
            top: calc(100% - 7rpx);
            left: calc(100% - 8rpx);
            transform: rotate(0);
          }
        }
      }

      .le-load {
        position: relative;
        left: -44rpx;
        bottom: 14rpx;
      
        image {
          width: 90rpx;
          height: 80rpx;
        }
      }
      
      .le-load-line {
        position: relative;
        height: 90rpx;
        width: 250rpx;
        border: 2px solid;
        border-color: #fb560a transparent transparent #fb560a;
        border-radius: 20rpx 0;
      
        .rightToLoad {
          position: absolute;
          animation: rightToLoad 3s linear infinite;
        }
      
        .leftToLoad {
          position: absolute;
          animation: leftToLoad 3s linear infinite;
        }
      
        @keyframes rightToLoad {
          100% {
            top: -12rpx;
            left: calc(100% - 10rpx);
            transform: rotate(90deg);
          }
      
          53% {
            top: -12rpx;
            left: 0;
            transform: rotate(90deg);
          }
      
          50% {
            top: 0;
            left: -10rpx;
            transform: rotate(-45deg);
          }
      
          47% {
            top: 14rpx;
            left: -10rpx;
            transform: rotate(0);
          }
      
          0% {
            top: calc(100% - 14rpx);
            left: -10rpx;
            transform: rotate(0);
          }
        }
      
        @keyframes leftToLoad {
          100% {
            top: calc(100% - 8rpx);
            left: -10rpx;
            transform: rotate(-90deg);
          }
      
          53% {
            top: 8rpx;
            left: -10rpx;
            transform: rotate(-90deg);
          }
      
          50% {
            top: 0;
            left: -8rpx;
            transform: rotate(-45deg);
          }
      
          47% {
            top: -11rpx;
            left: 8rpx;
            transform: rotate(0);
          }
      
          0% {
            top: -11rpx;
            left: calc(100% - 8rpx);
            transform: rotate(0);
          }
        }
      }
    }

    .item-ri {
      position: relative;
      left: -18rpx;
      top: -58rpx;
      display: flex;
      flex-direction: column;
      align-items: flex-end;

      .ri-bus {
        z-index: 0;
        position: relative;
        right: -70rpx;
        top: 68rpx;
        z-index: 100;
      
        image {
          width: 80rpx;
          height: 100rpx;
        }
      }
      
      .ri-bus-line {
        position: relative;
        height: 4rpx;
        width: 230rpx;
        background-color: #fb560a;
      
        .rightToBus {
          position: absolute;
          animation: rightToBus 3s linear infinite;
        }
      
        .leftToBus {
          position: absolute;
          animation: leftToBus 3s linear infinite;
        }
      
        @keyframes rightToBus {
          0% {
            top: -7rpx;
            left: -12rpx;
          }
      
          100% {
            top: -7rpx;
            left: calc(100% - 14rpx);
          }
        }
      
        @keyframes leftToBus {
          0% {
            top: -7rpx;
            left: calc(100% - 14rpx);
          }
          
          100% {
            top: -7rpx;
            left: -12rpx;
          }
        }
      }
    }
  }
</style>