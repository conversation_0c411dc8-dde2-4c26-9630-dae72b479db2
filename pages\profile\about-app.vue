<template>
  <view class="about">
    <u-navbar leftText="" :autoBack="true" leftIconSize="14px" bgColor="rgba(156, 189, 244, 0)" :title="$t('aboutApp')"
      :placeholder="true"></u-navbar>
    <view class="about-cont">
      <u-avatar :src="appIcon" size="140" shape="square"></u-avatar>
      <view class="u-m-t-30 bold">{{ $t('aboutApp.name') }}</view>
      <up-cell-group class="about-cont-cell">
        <up-cell :title="$t('当前版本')" :value="nowVersion"></up-cell>
        <up-cell :title="$t('版本更新')" :isLink="!isShowNew" @click="handleUpdateClick">
          <template #value v-if="isShowNew">
            <text class="u-slot-value">{{ $t('新') }}</text>
          </template>
        </up-cell>
        <update theme="blue" :h5preview="true" :oldversion="nowVersion" :appstoreflag="true" ref="upgradeRef"
          @showupdateTips="showupdatetips" :noticeflag="true"></update>
      </up-cell-group>
    </view>
    <view class="about-footer">
      <view class="about-footer-agree">
        <u-text type="primary" size="12px" :text="$t('PrivacyPokicy')" decoration="underline"
          @click="handlePrivacyClick" style="flex: none;width: auto;"></u-text>
      </view>
      <view class="about-footer-cont">{{ $t('aboutApp.filing') }}</view>
      <u-safe-bottom></u-safe-bottom>
    </view>
  </view>
</template>

<script setup>
  import {
    ref,
    getCurrentInstance
  } from 'vue'
  import { onShow } from '@dcloudio/uni-app'
  import {
    getUpdradeInfo
  } from '@/api/upgrade.js'
  import appIcon from '@/static/logo.png'
  import Update from '@/components/upgrade/upgrade.vue'

  const {
    proxy
  } = getCurrentInstance()

  const nowVersion = ref()
  // #ifdef APP-PLUS
  nowVersion.value = uni.$u.sys().appWgtVersion
  // #endif
  // #ifdef H5
  nowVersion.value = uni.$u.sys().appVersion
  // #endif

  /**
   * 隐私协议
   */
  const handlePrivacyClick = () => {
    let lang = uni.cache.getItem('language')
    if (lang == 'zh-Hans') {
      uni.navigateTo({
        url: '/pages/common/privacy-policy'
      })
    } else if (lang == 'en') {
      uni.navigateTo({
        url: '/pages/common/privacy-policy-en'
      })
    } else if (lang == 'it') {
      uni.navigateTo({
        url: '/pages/common/privacy-policy-it'
      })
    }
  }

  /**
   * 升级
   */
  const handleUpdateClick = async () => {
    proxy.$refs.upgradeRef.check_update()
  }
  const showupdatetips = (flag) => {
    if (flag == 0) {
      uni.showToast({
        title: uni.$t('已经是最新版本了'),
        icon: 'none'
      });
    }
  }
  const isShowNew = ref(false)
  const getUpdradeInfoFn = async () => {
    const res = await getUpdradeInfo({
      version: nowVersion.value,
      name: uni.$u.sys().appName,
      platform: uni.$u.sys().platform
    })
    if (res.msg == '应用程序需要更新') {
      isShowNew.value = true
    } else {
      isShowNew.value = false
    }
  }
  onShow(() => {
    getUpdradeInfoFn()
  })
</script>

<style lang="scss" scoped>
  .about {
    width: 100%;
    height: 100vh;

    &-cont {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 160rpx;

      &-cell {
        width: 80%;
        margin-top: 80rpx;
      }
    }

    &-footer {
      position: fixed;
      bottom: 0;
      text-align: center;
      width: 100%;
      font-size: 12px;
      color: $about-app-bottom-color;

      &-cont {
        padding-bottom: 60rpx;
      }

      &-agree {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
      }
    }
  }

  .u-slot-value {
    line-height: 17px;
    text-align: center;
    font-size: 10px;
    padding: 0 5px;
    height: 17px;
    color: #fff;
    border-radius: 100px;
    background-color: #f56c6c;
    margin-left: auto;
  }

  :deep(.u-avatar__image--square) {
    border-radius: 10px !important;
  }

  :deep(.u-cell--clickable) {
    background: transparent !important;
  }
</style>