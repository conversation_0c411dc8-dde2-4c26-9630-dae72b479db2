<template>
  <qiun-data-charts type="line" :opts="opts" :chartData="chartData" :ontouch="true" :optsWatch="false" :onmovetip="true"
    :animation="false" :inScrollView="true" :tooltipCustom="{x:50,y:0}" tooltipFormat="tooltipLineDemo2"
    :reshow="reshow" />
</template>

<script setup>
  import {
    computed,
    ref
  } from 'vue'
  import {
    useMonitorStore
  } from '@/store/monitor.js'
  import {
    isPhotovoltaicFn,
    isPowerFn,
    isGirdFn,
    isCellFn,
    isBusFn2
  } from '@/hook/useDeviceType.js'
  import {
    emTypeOptions
  } from '@/constant/index.js'

  // const props = defineProps({
  //   reshow: Boolean
  // })
  const reshow = ref(false)

  const monitorStore = useMonitorStore()
  const isShowV54154 = computed(() => {
    let versionStart = monitorStore.groupControl[0]?.jk_1000?.split('V')[1].split('.')[0]
    let versionTwo = monitorStore.groupControl[0]?.jk_1000?.split('V')[1].split('.')[1]
    let versionThere = monitorStore.groupControl[0]?.jk_1000?.split('V')[1].split('.')[2]
    if (versionStart == 5)
      if (versionTwo == 4154) return true
    return false
  })

  const opts = ref({
    color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4", "#ea7ccc"],
    padding: [15, 20, 0, 15],
    enableScroll: false,
    dataLabel: false,
    dataPointShape: false,
    legend: {
      show: false
    },
    xAxis: {
      type: 'grid',
      gridType: 'dash',
      // scrollShow: true,//新增是否显示滚动条，默认false
      labelCount: 5,
    },
    yAxis: {
      gridType: "dash",
      dashLength: 2,
      showTitle: true,
      data: [{
        tofix: 2,
        title: 'kW',
        titleOffsetY: -8
      }]
    },
    extra: {
      line: {
        type: "curve",
        width: 2,
        activeType: "hollow"
      },
      tooltip: {
        showArrow: false,
        borderWidth: 0,
        borderRadius: 8,
        borderColor: "#fff",
        bgColor: "#FFFFFF",
        fontColor: "#000000",
        splitLine: false,
        legendShape: 'circle'
      }
    },
    update: true,
  })
  // 组合设备类型，主机为RT07储能系统(纯并网)、PCC电表，电池功率-电表有功功率,取绝对值
  const isDeviceMasterTypeRT07 = computed(() => {
    let groupType = monitorStore.routeQuery.groupType
    let data = monitorStore.pcs_ele
    let isEmTypePCC = data.some(item => {
      return emTypeOptions.findIndex(range => item.dc >= range.min && item.dc <= range.max) !== -1
    })
    if ((groupType[0] == 7 || groupType[0] == 6) && isEmTypePCC) return true
    return false
  })
  const chartData = computed(() => {
    let {
      type,
      id
    } = monitorStore.routeQuery
    let {
      times,
      powers,
      cells,
      loads,
      photovoltaics,
      groupData,
      ac,
      em_10012s: em_10012ss
    } = monitorStore.powerGroupLineData
    let options = {
      categories: JSON.parse(JSON.stringify(times ? times : [])),
      series: []
    };
    // 电网功率
    if (type == 10000 || type == 10002) {
      options.series.push({
        name: uni.$t('电网功率'),
        data: JSON.parse(JSON.stringify(powers ? powers : [])),
        groupData: JSON.parse(JSON.stringify(groupData ? groupData : [])),
        isShowV54154: isShowV54154.value,
        isDeviceMasterTypeRT07: isDeviceMasterTypeRT07.value
      })
    }
    // 电池功率
    if (type == 10000 || type == 10002) {
      options.series.push({
        name: '电池功率',
        data: JSON.parse(JSON.stringify(cells ? cells : [])),
        groupData: JSON.parse(JSON.stringify(groupData ? groupData : [])),
        isShowV54154: isShowV54154.value,
        isDeviceMasterTypeRT07: isDeviceMasterTypeRT07.value
      })
    }
    // 负载功率
    if (type == 10000 || isShowV54154.value) {
      options.series.push({
        name: uni.$t('负载功率'),
        data: JSON.parse(JSON.stringify(loads ? loads : [])),
        groupData: JSON.parse(JSON.stringify(groupData ? groupData : [])),
        isShowV54154: isShowV54154.value,
        isDeviceMasterTypeRT07: isDeviceMasterTypeRT07.value
      })
    }
    if (isDeviceMasterTypeRT07.value) {
      let data = null
      if (cells) data = cells.map((item, index) => {
        let em_10012s = em_10012ss[index]
        return (item - em_10012s)?.toFixed(2)
      })
      options.series.push({
        name: uni.$t('负载功率'),
        data: JSON.parse(JSON.stringify(data ? data : [])),
        groupData: JSON.parse(JSON.stringify(groupData ? groupData : [])),
        isShowV54154: isShowV54154.value,
        isDeviceMasterTypeRT07: isDeviceMasterTypeRT07.value
      })
    }
    // 光伏功率
    if (type == 10000 || type == 10001) {
      options.series.push({
        name: uni.$t('光伏功率'),
        data: JSON.parse(JSON.stringify(photovoltaics ? photovoltaics : [])),
        groupData: JSON.parse(JSON.stringify(groupData ? groupData : [])),
        isShowV54154: isShowV54154.value,
        isDeviceMasterTypeRT07: isDeviceMasterTypeRT07.value
      })
    }
    // soc
    for (const key in groupData) {
      let socData = groupData[key].socs ?? []
      let socDet = new Set()
      socData.forEach(item1 => {
        if (item1) Object.keys(item1).forEach(item2 => socDet.add(item2))
      })
      if (Array.from(socDet).length) {
        Array.from(socDet).forEach(item3 => {
          let data = socData.map(item4 => {
            if (item4) {
              if (item4[item3] !== null) {
                return item4[item3]?.toFixed(2)
              } else {
                return null
              }
            }
            return null
          })
          options.series.push({
            name: item3.substring(0, 2) == '16' ?
              `${groupData[key].label}_${parseInt(item3) - 161000 + 1}#_SOC` :
              `${groupData[key].label}_SOC`,
            data: JSON.parse(JSON.stringify(data.length ? data : [])),
            groupData: JSON.parse(JSON.stringify(groupData ? groupData : []))
          })
        })
      }
    }
    // 直流母线功率
    // if (type == 2 || type == 3 || type == 4 || type == 5 || type == 8 || type == 10) {
    // options.series.push({
    //   name: uni.$t('直流母线功率'),
    //   data: JSON.parse(JSON.stringify(bus ? bus: []))
    // })
    // }
    if (ac == id) {
      reshow.value = true
      return JSON.parse(JSON.stringify(options))
    }
    // opts.value.xAxis.itemCount = times ? times.length : 0
    // if (times) return JSON.parse(JSON.stringify(options));
  })

  defineExpose({
    opts
  })
</script>

<style scoped lang="scss">

</style>