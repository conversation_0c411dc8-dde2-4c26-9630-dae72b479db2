{"common": {"uni.app.quit": "Premi di nuovo e l'app si chiuderà", "uni.async.error": "La connessione al server è scaduta. Fare clic sullo schermo per riprovare", "uni.showActionSheet.cancel": "<PERSON><PERSON><PERSON>", "uni.showToast.unpaired": "Si prega di notare che showToast e hideToast devono essere associati per poter essere utilizzati", "uni.showLoading.unpaired": "Si prega di notare che showLoading e hideLoading devono essere associati per poter essere utilizzati", "uni.showModal.cancel": "<PERSON><PERSON><PERSON>", "uni.showModal.confirm": "OK", "uni.chooseImage.cancel": "<PERSON><PERSON><PERSON>", "uni.chooseImage.sourceType.album": "Seleziona dall'album", "uni.chooseImage.sourceType.camera": "Telecamera", "uni.chooseVideo.cancel": "<PERSON><PERSON><PERSON>", "uni.chooseVideo.sourceType.album": "Seleziona dall'album", "uni.chooseVideo.sourceType.camera": "Telecamera", "uni.previewImage.cancel": "<PERSON><PERSON><PERSON>", "uni.previewImage.button.save": "<PERSON><PERSON>", "uni.previewImage.save.success": "Salva correttamente l'immagine nell'album", "uni.previewImage.save.fail": "Impossibile salvare l'immagine nell'album", "uni.setClipboardData.success": "Contenuto copiato", "uni.scanCode.title": "Codice di scansione", "uni.scanCode.album": "Album", "uni.scanCode.fail": "Riconoscimento fallito", "uni.scanCode.flash.on": "Tocca per illuminare", "uni.scanCode.flash.off": "Tocca per chiudere", "uni.startSoterAuthentication.authContent": "Riconoscimento delle impronte digitali...", "uni.picker.done": "Completamento", "uni.picker.cancel": "<PERSON><PERSON><PERSON>", "uni.video.danmu": "「<PERSON><PERSON><PERSON><PERSON><PERSON>」", "uni.video.volume": "Volume", "uni.button.feedback.title": "<PERSON><PERSON>", "uni.button.feedback.send": "Inviare"}, "ios": {}, "android": {}}