<template>
  <view style="height: 100vh;padding: 20rpx 30rpx;overflow-y: auto;">
    <view class="param-form">
      <u-form labelPosition="left" labelWidth="auto" :model="form" :rules="rules" ref="formRef" :labelStyle="paramLabelStyle" errorType="toast">
        <u-form-item prop="status" :label="$t('下发状态')">
          <view class="send-status color-grey" v-if="form.status == 0">{{ $t('未下发') }}</view>
          <view class="send-status" v-if="form.status == 1">
            <image src="../../../static/green.png" mode="" style="width: 30rpx;height: 30rpx;"></image>
            <view>{{ $t('下发成功') }}</view>
          </view>
          <view class="send-status" v-if="form.status == 2">
            <image src="../../../static/yellow.png" mode="" style="width: 35rpx;height: 35rpx;"></image>
            <view>{{ $t('下发中') }}</view>
          </view>
          <view class="send-status" v-if="form.status == 3">
            <image src="../../../static/red.png" mode="" style="width: 35rpx;height: 35rpx;"></image>
            <view>{{ $t('下发失败') }}</view>
          </view>
        </u-form-item>
        <u-form-item prop="setting1899" :label="$t('开关机')" required>
          <u-radio-group v-model="form.setting1899" placement="row" size="16px"
            style="display: flex;justify-content: flex-end;">
            <u-radio :label="$t('开机')" name="1" class="mr-20"></u-radio>
            <u-radio :label="$t('关机')" name="0"></u-radio>
          </u-radio-group>
        </u-form-item>
      </u-form>
    </view>
    <view
      style="margin-top: 20rpx;font-size: 12px;padding: 20rpx 30rpx;background-color: #fff;border-radius: 6px;color: #b7b7b7;">
      <view style="margin-bottom: 10rpx;">
        <view style="color: #333;margin-bottom: 10rpx;">
          {{ $t('下发状态') }}：
        </view>
        <view>{{ $t('未下发') }}：{{ $t('该类参数从未下发') }}</view>
        <view>{{ $t('下发中') }}：{{ $t('参数已成功下发至设备，执行未知，请等待') }}</view>
        <view>{{ $t('下发成功') }}：{{ $t('参数已成功下发至设备并已执行成功') }}</view>
        <view>{{ $t('下发失败') }}：{{ $t('参数已成功下发至设备，设备并未执行成功') }}</view>
      </view>
      <view>
        <view style="color: #333;margin-bottom: 10rpx;">
          {{ $t('开关机') }}：
        </view>
        <view>{{ $t('控制系统按其工作模式启停。') }}</view>
      </view>
    </view>
    <u-button type="primary" style="margin-top: 40rpx;border-radius: 6px;" @click="handleSendClick" :disabled="isSend">{{ $t('下发') }}</u-button>
    <view class="ft12 u-m-t-20 color-grey" style="width: 100%;display: flex; align-items: center;justify-content: center;" v-if="isSend">
      <u-icon name="error-circle" size="14px" style="margin-right: 6rpx;margin-top: 1rpx;"></u-icon>
      <view>
        {{ $t('设备已离线，不可下发') }}
      </view>
    </view>
  </view>
</template>

<script setup>
  import {
    ref,
    reactive,
    getCurrentInstance,
    computed,
    toRefs
  } from 'vue';
  import {
    useParamStore
  } from '@/store/param.js'
  import {
    useMonitorStore
  } from '@/store/monitor.js'
  import {
    onShow,
    onLoad
  } from '@dcloudio/uni-app'
  import otherColor from '../../../common/other.module.scss'
  import {
    isGroupFn
  } from '@/hook/useDeviceType.js'
  import { paramLabelStyle } from '@/constant'

  const paramStore = useParamStore()
  const {
    routeQuery,
    control,
    groupControl
  } = toRefs(useMonitorStore())
  const {
    proxy
  } = getCurrentInstance()
  const isGroup = computed(() => isGroupFn(routeQuery.value.type))
  const current = ref()
  onLoad((options) => {
    current.value = options.index
  })

  // 使用 reactive 创建响应式状态  
  const form = ref({
    setting1899: '1',
    status: 0
  })
  const rules = ref({
    setting1899: {
      type: 'string',
      required: true,
      message: uni.$t('请选择'),
      trigger: ['blur', 'change'],
    },
  })
  const formRef = ref(null);
  const handleSendClick = () => {
    let ac = null
    if (isGroup.value) {
      ac = routeQuery.value.groupId[current.value]
    } else {
      ac = routeQuery.value.id
    }
    if (formRef.value) {
      formRef.value.validate().then(async valid => {
        if (valid) {
          try{
            await paramStore.sendParamOnOffFn({
              setting1899: form.value.setting1899,
              ac: ac,
              id: form.value.id
            })
            setTimeout(() => {
              uni.$u.toast(uni.$t('下发成功'))
            }, 20)
            getInfo()
          }catch(e){
            setTimeout(() => {
              uni.$u.toast(uni.$t('下发失败'))
            }, 20)
            getInfo()
          }
        }
      })
    }
  }

  const isSend = computed(() => {
    if (isGroup.value) {
      let ac = routeQuery.value.groupId[current.value]
      let value = groupControl.value.find(item => item.ac == ac)
      if (!Object.keys(value).length) return true
      return value['onLineState'] == '离线'
    } else {
      if (!Object.keys(control.value).length) return true
      return control.value['onLineState'] == '离线'
    }
  })

  const getInfo = async () => {
    let ac = null
    if (isGroup.value) {
      ac = routeQuery.value.groupId[current.value]
    } else {
      ac = routeQuery.value.id
    }
    const res = await paramStore.onOffInfoFn({ ac })
    if (!res) return
     let data = JSON.parse(JSON.stringify(form.value))
      if (res.status == 1 && data.status == 2) {
        uni.$u.toast(uni.$t('下发成功'))
      } else if (res.status == 3 && data.status == 2) {
        uni.$u.toast(uni.$t('下发失败'))
      }
    form.value = {
      ...res,
    }
  }
  onShow(() => {
    getInfo()
  })
</script>

<style scoped lang="scss">
  .send-status {
    display: flex;
    align-items: center;

    image {
      margin-right: 6rpx;
    }
  }

  .param-form {
    width: 100%;
    background-color: #fff;
    border-radius: $uni-border-radius-lg;
    padding: 0 30rpx;
  }

  :deep(.u-form-item__body__right__content__slot) {
    flex-direction: row-reverse;
  }
</style>