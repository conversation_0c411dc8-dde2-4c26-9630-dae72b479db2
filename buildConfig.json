{"project": "elecloud_mobile_copy", "platform": "android", "iscustom": true, "safemode": false, "android": {"packagename": "uni.UNI655B477", "androidpacktype": "0", "certalias": "elecloud_app", "certfile": "C:/Users/<USER>/Desktop/定制app/亿兰科云平台/secretKey/android/elecloud_app.keystore", "certpassword": "elecloud8888", "channels": ""}, "ios": {"bundle": "uni.UNI655B477", "supporteddevice": "iPhone,iPad", "profile": "C:/Users/<USER>/Desktop/上架/app_证书密钥/apple/elecloud.mobileprovision", "certfile": "C:/Users/<USER>/Desktop/上架/app_证书密钥/apple/证书.p12", "certpassword": "elecloud8888"}, "isconfusion": false, "splashads": false, "rpads": false, "pushads": false, "exchange": false}