<template>
  <qiun-data-charts type="column" :opts="opts" :chartData="chartData" :ontouch="true" :optsWatch="false"
    :onmovetip="true" :animation="false" :inScrollView="true" :tooltipCustom="{x:50,y:0}"
    tooltipFormat="tooltipBarDemo1" :reshow="props.reshow" />
</template>

<script setup>
  import {
    computed,
    ref
  } from 'vue'
  import {
    useMonitorStore
  } from '@/store/monitor.js'
  import { isGroupFn, isPhotovoltaicFn, isEnergyFn } from '@/hook/useDeviceType.js'
  
  const props = defineProps({
    reshow: Boolean
  })

  const monitorStore = useMonitorStore()

  const opts = ref({
    color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4", "#ea7ccc"],
    padding: [15, 20, 0, 15],
    enableScroll: false,
    dataLabel: false,
    dataPointShape: false,
    rotate: false,
    legend: {
      show: false
    },
    xAxis: {
      type: 'grid',
      gridType: 'dash',
      scrollShow: true, //新增是否显示滚动条，默认false
      labelCount: 5,
    },
    yAxis: {
      gridType: "dash",
      dashLength: 2,
      showTitle: true,
      data: [
        {
          title: 'kWh',
          titleOffsetY: -8
        }
      ]
    },
    extra: {
      column: {
        type: "stack",
        width: 6,
        activeBgColor: "#FFFFFF",
        activeBgOpacity: 0.08,
        labelPosition: "center",
        seriesGap: 5,
        barBorderRadius: [10, 10, 0, 0]
      },
      tooltip: {
        showArrow: false,
        borderWidth: 0,
        borderRadius: 8,
        borderColor: "#FFFFFF",
        bgColor: "#FFFFFF",
        bgOpacity: 1,
        fontColor: "#000000",
        splitLine: false,
        legendShape: 'circle'
      }
    }
  })
  const chartData = computed(() => {
    let {
      type
    } = monitorStore.routeQuery
    let {
      times,
      dischargeCapacityCalculate,
      chargeCapacityCalculate,
      photovoltaicPowerCapacityCalculate,
      jk_1083,
      jk_1084
    } = monitorStore.electricData
    let options = {
      categories: JSON.parse(JSON.stringify(times ? times : [])),
      series: []
    };
    opts.value.xAxis.labelCount = monitorStore.queryInfo.type == 2 ? 4: 3
    // 光伏发电量
    if (isPhotovoltaicFn(type)) {
      options.series.push({
        name: uni.$t('光伏发电量'),
        data: JSON.parse(JSON.stringify(photovoltaicPowerCapacityCalculate ?
          photovoltaicPowerCapacityCalculate : []))
      })
    }
    // 充放电量
    if (isEnergyFn(type)) {
      options.series.push({
        name: uni.$t('充电量'),
        data: JSON.parse(JSON.stringify(chargeCapacityCalculate ? chargeCapacityCalculate : []))
      }, {
        name: uni.$t('放电量'),
        data: JSON.parse(JSON.stringify(dischargeCapacityCalculate ? dischargeCapacityCalculate : []))
      })
    }
    // 直流源
    if (type == 13) {
      options.series.push({
        name: uni.$t('正向电量'),
        data: JSON.parse(JSON.stringify(jk_1083 ? jk_1083 : []))
      }, {
        name: uni.$t('反向电量'),
        data: JSON.parse(JSON.stringify(jk_1084 ? jk_1084 : []))
      })
    }
    if (times) return JSON.parse(JSON.stringify(options));
    return {}
  })
  
  defineExpose({
    opts
  })
</script>

<style scoped lang="scss">

</style>