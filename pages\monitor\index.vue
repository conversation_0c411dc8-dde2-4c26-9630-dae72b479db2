<template>
  <view class="monitor">
    <z-paging ref="paging" v-model="tableData" show-refresher-update-time @query="queryList" hide-empty-view>
      <template #top>
        <u-navbar :title="$t('设备监控')" :bgColor="otherColor.bgColor" :placeholder="true" class="tabbar" :titleStyle="{
    				        color: '#000'
    				      }">
        </u-navbar>
        <view class="home-top" style="background: rgba(0, 0, 0, 0);">
          <view class="top flex align-items">
            <view class="top-item" @click="hansleSearchONandOffClick(1)">
              <image src="../../static/monitor-a.png" class="item-img"></image>
              <view class="item-text">{{ $t('设备总数') }}</view>
              <view class="item-num">{{ sumData.sumCount }}{{ $t('台') }}</view>
            </view>
            <view class="top-item" @click="hansleSearchONandOffClick(2)">
              <image src="../../static/monitor-c.png" class="item-img"></image>
              <view class="item-text">{{ $t('在线数') }}</view>
              <view class="item-num">{{ sumData.onLine || 0 }}{{ $t('台') }}</view>
            </view>
            <view class="top-item" @click="hansleSearchONandOffClick(3)">
              <image src="../../static/monitor-b.png" class="item-img"></image>
              <view class="item-text">{{ $t('离线数') }}</view>
              <view class="item-num">{{ sumData.offLine || 0 }}{{ $t('台') }}</view>
            </view>
          </view>
        </view>
      </template>
      <!-- 如果希望其他view跟着页面滚动，可以放在z-paging标签内 -->
      <view class="items">
        <view class="items-top flex justify-content align-items mb-20">
          <view class="flex align-items">
            <image src="../../static/monitor-d.png" class="u-m-r-10" style="width: 36rpx;height: 36rpx;"></image>
            <view style="font-weight: bold;">{{ $t('设备监控列表') }}</view>
          </view>
          <image src="../../static/select.png" style="width: 34rpx;height: 34rpx;" @click="isShowPopup = true"></image>
        </view>
        <template v-if="tableData.length">
          <u-swipe-action>
            <u-swipe-action-item :options="item.actionOptions" :disabled="item.disabled" v-for="item in tableData"
              :name="item.id" :key="item.id" class="u-m-b-20" @click="(data) => handleItemActionClick(data, item)">
              <view class="item flex align-items" @click="handleItemClick(item)">
                <view class="item-le mr-20">
                  <image src="../../static/device-system.png" mode="" class="le-img" v-if="isConverterFn(item.deviceType)">
                  </image>
                  <image src="../../static/converter.png" mode="" style="width: 100rpx;height: 180rpx;" v-else></image>
                </view>
                <view class="item-ri">
                  <view class="flex justify-content align-items">
                    <view class="flex align-items">
                      <image src="../../static/solve.svg" mode="" class="img-status" v-if="item.onLineState == '在线'">
                      </image>
                      <image src="../../static/solve-off.svg" mode="" class="img-status" v-else></image>
                      <view class="ri-title">{{ item.deviceName }}</view>
                    </view>
                  </view>
                  <view class="u-line-1">{{ $t('所属项目') }}：{{ item.projectName }}</view>
                  <view>{{ $t('实时功率') }}：<span class="ri-num">{{ item.power }}</span>kW</view>
                  <view>{{ $t('工作状态') }}：<span
                      v-if="item.onLineState == '在线'">{{ getListWorkState(item.workState) }}</span><span
                      v-else>--</span>
                  </view>
                  <view v-if="item.photovoltaicInstalledCapacity" class="u-line-1">{{ $t('光伏装机容量') }}：<span
                      class="ri-num">{{ item.photovoltaicInstalledCapacity ?? '--' }}</span>kWp</view>
                  <view v-else>{{ $t('电池容量') }}：<span class="ri-num">{{ item.deviceBatteryCapacity ?? '--' }}</span>kWh
                  </view>
                </view>
              </view>
            </u-swipe-action-item>
          </u-swipe-action>
        </template>

        <u-empty icon="../../static/empty.webp" width="300" height="300" textSize="14px" :text="$t('暂无数据')"
          style="height: 50vh" v-else>
        </u-empty>
      </view>
    </z-paging>

    <u-popup :show="isShowPopup" mode="right" @close="closePopup" @open="openPopup">
      <view class="popup-wrapper">
        <!-- <view class="popup-wrapper-title">筛选项</view> -->
        <view class="popup-wrapper-item">
          <view class="popup-wrapper-item-title">{{ $t('在线状态') }}</view>
          <view class="popup-wrapper-item-input">
            <view class="select-item" style="display: inline-block;"
              :style="{backgroundColor
            : item.value == selectForm.status ? '#1989FA': '#f8f8f8', color: item.value == selectForm.status ? '#fff': '#666'}" v-for="(item, index) in selectOptions"
              :key="index" @click="handleSelectItemClick(item.value, item.prop, index)">
              {{ item.label }}
            </view>
          </view>
        </view>
        <view class="popup-wrapper-item">
          <view class="popup-wrapper-item-title">{{ $t('设备类型') }}</view>
          <view class="popup-wrapper-item-input">
            <view class="flex u-flex-items-center u-p-l-15" @click="deviceTypeClick">
              <view style="font-size: 12px;margin-right: 6rpx;flex: 1" class="u-line-1">{{ slectDeviceTypeText }}</view>
              <u-icon name="arrow-down-fill" size="10px"></u-icon>
            </view>
          </view>
        </view>
        <view class="popup-wrapper-item" v-for="item in fieldOptions" :key="item.value">
          <view class="popup-wrapper-item-title">
            <view class="flex u-flex-items-center">
              <view style="font-size: 12px;margin-right: 6rpx;">{{ item.text }}</view>
            </view>
          </view>
          <view class="popup-wrapper-item-input"
            :style="{'border-bottom': item.value == 'nickName'? 'none': '1px solid #f8f8f8'}">
            <u--input v-model="selectForm[item.value]" :customStyle="{
                          borderRadius: '100px',
                          height: '50rpx',
                        }" :placeholderStyle="`{
                          fontSize: '12px'
                        }`" shape="circle" :placeholder="$t('请输入')" :adjustPosition="false"></u--input>
          </view>
        </view>
        <view class="popup-wrapper-oper flex u-flex-center">
          <u-button size="small" class="mr-20" @click="handlePopupResetClick">{{ $t('重置') }}</u-button>
          <u-button type="primary" size="small" @click="handlePopupConfirmClick">{{ $t('确定') }}</u-button>
        </view>
      </view>
    </u-popup>
    <u-picker :show="isShowAc" :defaultIndex="defaultSelectIndex" keyName="text" :columns="[acOptions]" itemHeight="88" :title="$t('选择设备')"
      @confirm="handleSelectConfirm" :closeOnClickOverlay="true" @close="handleSelectCancel"
      @cancel="handleSelectCancel" :cancelText="$t('取消')" :confirmText="$t('确认')"></u-picker>
  </view>
</template>

<script setup>
  import {
    ref,
    nextTick,
    getCurrentInstance,
    computed
  } from 'vue'
  import {
    onReady,
    onShow
  } from '@dcloudio/uni-app'
  import {
    $uGetRect
  } from '@/common/utils.js'
  import {
    getListWorkState
  } from '@/common/parseBinaryToText.js'
  import {
    deviceMonitoringList,
    getOnLineOrOffLineVo,
    recover,
    argumentsJsonFrpClose,
    vncParameterSettingByAc
  } from '@/api/monitor.js'
  import otherColor from '../../common/other.module.scss'
  import {
    isConverterFn,
    isGroupFn
  } from '@/hook/useDeviceType.js'
  import {
    isShowPerm
  } from '@/common/utils'

  /**
   * 获取设备监控列表
   */
  const tableData = ref([])
  const {
    proxy
  } = getCurrentInstance()
  const paging = ref({
    pageNum: 1,
    pageSize: 10,
    total: 0
  })
  const queryInfo = ref({
    status: undefined,
    deviceType: undefined
    // searchKey: 'deviceSerialNumber',
    // searchValue: undefined
  })
  const deviceMonitoringListFn1 = async ({
    pageNum,
    pageSize
  }) => {
    const res = await deviceMonitoringList({
      pageNum,
      pageSize,
      ...queryInfo.value,
      ...selectForm.value
    })
    let data = res.rows
    data.forEach(item => {
      item.power = parseFloat(item.power).toFixed(2)
      item.actionOptions = []
      if (isShowPerm(['system:sendMqtt:argumentsJsonFrp'])) item.actionOptions.push({
        text: uni.$t('远程控制'),
        style: {
          backgroundColor: `rgba(60, 156, 255, ${item.onLineState == '在线' ? 1: 0.7})`
        }
      })
      if (isShowPerm(['system:deviceMonitoring:clearData'])) item.actionOptions.push({
        text: uni.$t('恢复出厂设置'),
        style: {
          backgroundColor: '#f56c6c'
        }
      })
      item.disabled = item.actionOptions.length == 0
    })
    paging.value.total = res.total
    return data
  }


  const queryList = (pageNo, pageSize) => {
    // 组件加载时会自动触发此方法，因此默认页面加载时会自动触发，无需手动调用
    // 这里的pageNo和pageSize会自动计算好，直接传给服务器即可
    // 模拟请求服务器获取分页数据，请替换成自己的网络请求
    deviceMonitoringListFn1({
      pageNum: pageNo,
      pageSize
    }).then(res => {
      // 将请求的结果数组传递给z-paging
      proxy.$refs.paging.completeByTotal(res, paging.value.total)
    }).catch(res => {
      // 如果请求失败写this.$refs.paging.complete(false);
      // 注意，每次都需要在catch中写这句话很麻烦，z-paging提供了方案可以全局统一处理
      // 在底层的网络请求抛出异常时，写uni.$emit('z-paging-error-emit');即可
      proxy.$refs.paging.complete(false);
    })
  }

  /**
   * 获取设备在线数
   */
  const sumData = ref({})
  const getOnLineOrOffLineVoFn = async () => {
    const res = await getOnLineOrOffLineVo()
    sumData.value = res.data
  }
  /**
   * 跳转
   */
  const handleItemClick = (item) => {
    uni.navigateTo({
      url: `/pages/monitor/deviceDetails?id=${item.deviceSerialNumber}&type=${item.deviceType}&time=${item.timeZone ? encodeURIComponent(item.timeZone): encodeURIComponent('+08:00')}&name=${item.deviceName}&groupId=${encodeURIComponent(item.combinationDeviceSerialNumber)}&groupType=${encodeURIComponent(item.combinationDeviceType)}`
    })
  }

  /**
   * 搜索
   */
  const isShowSearch = ref(false)
  const handleSearchClick = () => {
    proxy.$refs.paging.reload()
    isShowSearch.value = false
  }
  /**
   * 搜索
   */
  const hansleSearchONandOffClick = (type) => {
    if (type == 1) {
      queryInfo.value.status = undefined
      selectForm.value.status = undefined
    } else if (type == 2) {
      queryInfo.value.status = 2
      selectForm.value.status = 2
    } else if (type == 3) {
      queryInfo.value.status = 3
      selectForm.value.status = 3
    }
    proxy.$refs.paging.reload()
  }

  /**
   * 弹出
   */
  const selectForm = ref({})
  const isShowPopup = ref(false)
  const closePopup = () => {
    uni.showTabBar()
    isShowPopup.value = false
  }
  const openPopup = () => {
    selectForm.value = {
      ...queryInfo.value,
      ...selectForm.value
    }
    isShowPopup.value = true
    uni.hideTabBar()
  }
  const fieldOptions = ref([
    {
      text: 'SN',
      value: 'deviceSerialNumber',
    },
    {
      text: uni.$t('设备名称'),
      value: 'deviceName',
    },
    {
      text: uni.$t('所属项目'),
      value: 'projectName',
    },
    {
      text: uni.$t('国家'),
      value: 'country',
    },
    {
      text: uni.$t('创建人员'),
      value: 'nickName',
    },
  ])
  const selectOptions = ref([{
      label: uni.$t('在线'),
      value: 2,
      prop: 'status'
    },
    {
      label: uni.$t('离线'),
      value: 3,
      prop: 'status'
    },
  ])
  const handleSelectItemClick = (value, prop, index) => {
    if (selectForm.value.status == value) selectForm.value.status = undefined
    else selectForm.value.status = value
  }
  const slectDeviceTypeText = ref(uni.$t('全部'))
  const deviceTypeClick = () => {
    uni.navigateTo({
      url: `/pages/property/device/typeList?value=${selectForm.value.deviceType}&type=single`
    })
  }
  uni.$on('typeItemClick', (data) => {
    if (!data) return
    selectForm.value.deviceType = data.value
    slectDeviceTypeText.value = data.label
  })
  const handlePopupConfirmClick = () => {
    queryInfo.value = {
      ...selectForm.value
    }
    proxy.$refs.paging.reload()
    isShowPopup.value = false
    uni.showTabBar()
  }
  const handlePopupResetClick = () => {
    selectForm.value = {
      status: undefined
    }
    proxy.$refs.paging.reload()
    isShowPopup.value = false
    uni.showTabBar()
  }

  const windowInfo = ref()
  const safeTop = ref()
  const safeBottom = ref()
  onShow(() => {
    getOnLineOrOffLineVoFn()
    windowInfo.value = uni.getWindowInfo()
    safeTop.value = windowInfo.value.statusBarHeight + 30 + 'rpx'
    // #ifdef APP-PLUS
    safeBottom.value = (windowInfo.value.windowBottom * 2) + 30 + 'rpx'
    // #endif
    // #ifdef H5
    safeBottom.value = 30 + 'rpx'
    // #endif
  })

  /**
   * 滑动
   */
  const isGroup = ref(false)
  const handleItemActionClick = (data, item) => {
    isGroup.value = isGroupFn(item.deviceType)
    if (data.index == 1) { // 恢复出厂设置
      clearData(item)
    } else { // 远程控制
      if (item.onLineState != '在线') return uni.$u.toast(uni.$t('设备离线，不可操作'))
      if (isGroup.value) {
        let acs = item.combinationDeviceSerialNumber.split(',')
        acOptions.value = acs.map(item => {
          return {
            value: item,
            text: item
          }
        })
        isShowAc.value = true
      } else getVncInfo(item.deviceSerialNumber)
    }
  }
  const selectAc = ref()
  const isShowAc = ref(false)
  const defaultSelectIndex = ref([0])
  const acOptions = ref([])
  const handleSelectConfirm = ({
    value
  }) => {
    getVncInfo(value[0].value)
    isShowAc.value = false
  }
  const handleSelectCancel = () => {
    defaultSelectIndex.value = [0]
    isShowAc.value = false
  }
  const getVncInfo = (ac) => {
    vncParameterSettingByAc({
      ac
    }).then(res => {
      if (res.code !== 200) return uni.$u.toast(res.msg)
      if (res.data) {
        if (res.data.onOff == 0 && res.data.status == 1) {
          closeVnc(ac, res.data)
        } else {
          uni.navigateTo({
            url: `/pages/monitor/vnc/setVnc?ac=${ac}&id=${res.data.id}`
          })
        }
      } else {
        uni.navigateTo({
          url: `/pages/monitor/vnc/setVnc?ac=${ac}`
        })
      }
    }).catch(err => {
      uni.$u.toast(err)
    })
  }
  const closeVnc = (ac, item) => {
    uni.showModal({
      title: uni.$t('系统提示'),
      content: uni.$t('该设备已开启远程控制，是否需要关闭远程控制'),
      confirmColor: otherColor.primaryColor,
      confirmText: uni.$t('确认关闭'),
      cancelText: uni.$t('查看端口'),
      success: (res) => {
        if (res.confirm) {
          uni.showLoading({
            title: `${uni.$t('正在关闭中')}...`
          })
          argumentsJsonFrpClose({
            ac,
            id: item.id
          }).then((res) => {
            if (res.code !== 200) uni.hideLoading()
            uni.hideLoading()
            uni.$u.toast(uni.$u('关闭远程成功'))
          }).catch(() => {
            uni.hideLoading()
          })
        } else if (res.cancel) {
          uni.navigateTo({
            url: `/pages/monitor/vnc/vncInfo?item=${encodeURIComponent(JSON.stringify(item))}`
          })
        }
      }
    })
  }
  const clearData = (row) => {
    uni.showModal({
      title: uni.$t('系统提示'),
      content: uni.$t('确定要恢复出厂设置吗？'),
      confirmColor: otherColor.primaryColor,
      success: (res) => {
        if (res.confirm) {
          recover({
            deviceSerialNumber: row.deviceSerialNumber,
            timeZone: row.timeZone,
            deviceType: row.deviceType
          }).then(res => {
            uni.$u.toast(uni.$t('恢复出厂设置成功，需要等 2 分钟后再查看'))
            proxy.$refs.paging.reload()
          })
        } else if (res.cancel) {
          console.log('用户点击取消');
        }
      }
    })
  }
</script>

<style scoped lang="scss">
  .monitor {
    width: 100%;
    height: 100vh;
    /* background-color: $uni-bg-color-grey; */
    overflow: hidden;

    .home-top {
      background: $transparent;
      height: 280rpx;
      width: 100%;
      display: flex;
      flex-direction: column-reverse;
      margin-bottom: 20rpx;
    }

    .top {
      justify-content: space-evenly;
      background-color: $uni-bg-color;
      height: 180rpx;
      border-radius: $uni-border-radius-lg;
      margin-bottom: 20rpx;
      text-align: center;
      margin: 0 30rpx;
      width: calc(100% - 60rpx);

      .top-item {
        font-size: 12px;
        margin-top: 8rpx;
        width: 33.3%;

        .item-num {
          font-weight: bold;
          color: $u-primary;
        }

        .item-img {
          width: 38rpx;
          height: 38rpx;
        }

        .item-text {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 70rpx;
        }
      }
    }

    .items {
      margin: 0 30rpx;
      background-color: $uni-bg-color;
      padding: 20rpx 30rpx;
      border-radius: $uni-border-radius-lg;
      padding-bottom: 30rpx;

      .scroll-Y {
        height: 95%;
      }

      .item {
        background-color: $uni-bg-color-grey;
        padding: 16rpx;
        border-radius: $uni-border-radius-lg;
        font-size: 12px;
        margin-bottom: 20rpx;
        line-height: 40rpx;

        .item-le {
          // flex: 1;
          width: 150rpx;
          display: flex;
          justify-content: center;
          align-items: center;

          .le-img {
            width: 150rpx;
            height: 160rpx;
          }
        }

        .img-status {
          width: 28rpx;
          height: 28rpx;
        }

        .item-ri {
          flex: 4;

          .ri-title {
            font-size: 12px;
            font-weight: bold;
            margin-left: 6rpx;
          }

          .ri-num {
            font-weight: bold;
            margin-right: 4rpx;
          }
        }
      }

      .item:last-child {
        margin-bottom: 0;
      }
    }
  }

  .popup-wrapper {
    width: 50vw;
    padding: 30rpx;
    position: relative;
    padding-top: v-bind(safeTop);
    height: 100%;

    &-title {
      font-weight: 600;
    }

    &-item {
      margin: 20rpx 0;

      &-title {
        font-size: 12px;
        margin-left: 6rpx;
      }

      &-input {
        margin-top: 20rpx;
        border-bottom: 1px solid #f8f8f8;
        padding-bottom: 10px;
      }
    }

    &-oper {
      width: calc(100% - 60rpx);
      position: absolute;
      bottom: v-bind(safeBottom);
    }
  }

  .select-item {
    height: 60rpx;
    line-height: 60rpx;
    padding: 0 30rpx;
    background: $uni-bg-color-grey;
    border-radius: 30rpx;
    font-size: 12px;
    margin-right: 20rpx;
  }

  .input-search {
    border-radius: 100px;
    border-color: transparent !important;
  }

  :deep(.u-navbar__content__left) {
    display: none;
  }

  :deep(.u-input) {
    flex: none
  }
</style>