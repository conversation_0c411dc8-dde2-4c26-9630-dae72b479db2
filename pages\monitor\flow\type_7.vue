<template>
  <view class="flow-item u-flex u-flex-center align-items bold">
    <view class="item-le">
      <view class="le-gird flex u-flex-column align-items">
        <image src="../../../static/flow_ac.png" mode=""></image>
        <view class="ml-5">{{ flowData.power }} kW</view>
      </view>
      <view class="le-gird-line" :style="[lineStyle('power')]">
        <template v-if="showCircle('power')">
          <u-icon name="play-right-fill" size="20rpx" color="#fb560a" class="rightToGird"
            v-if="flowData.power < -1"></u-icon>
          <u-icon name="play-left-fill" size="20rpx" color="#fb560a" class="leftToGird"
            v-else-if="flowData.power > 1"></u-icon>
        </template>
      </view>
    </view>
    <view class="item-ce">
      <image src="../../../static/flow_hmi.png"></image>
    </view>
    <view class="item-ri">
      <view class="ri-cell flex u-flex-column">
        <view style="height: 80rpx;width: 80rpx;display: flex;justify-content: center;align-items: center;">
          <zu-battery :battery="soc" width="50rpx" height="50rpx" fontSize="8.5px"
            style="margin-top: 10rpx;"></zu-battery>
        </view>
        <view class="u-m-r-10" v-if="isShowV54154">
          <span>{{ flowData.groupData[groupId[0]].cell }}</span> kW
        </view>
        <view class="u-m-r-10" v-else>
          <span>{{ flowData.cell }}</span> kW
        </view>
      </view>
      <view class="ri-cell-line" :style="[lineStyle('cell')]">
        <template v-if="showCircle('cell')">
          <u-icon name="play-right-fill" size="20rpx" color="#fb560a" class="rightToCell"
            v-if="flowData.cell < -1"></u-icon>
          <u-icon name="play-left-fill" size="20rpx" color="#fb560a" class="leftToCell"
            v-else-if="flowData.cell > 1"></u-icon>
        </template>
      </view>
    </view>
  </view>
</template>

<script setup>
  import {
    ref,
    computed
  } from 'vue'
  import {
    useMonitorStore
  } from '@/store/monitor.js'
  import {
    isGroupFn
  } from '@/hook/useDeviceType.js'

  const monitorStore = useMonitorStore()

  const deviceType = computed(() => monitorStore.routeQuery.type)
  const flowData = computed(() => {
    return deviceType.value == 10002 ? monitorStore.groupFlowData : monitorStore.flowData
  })
  const lineStyle = computed(() => (type) => {
    let color = showCircle.value(type) ? '#fb560a' : '#eeeeef'
    let backgroundColor = color
    return {
      backgroundColor
    }
  })
  // 流动拓补图展示圆
  const showCircle = computed(() => {
    let control = deviceType.value == 10002 ? monitorStore.groupControl[0] : monitorStore.control
    return (name) => {
      if (!control) return false
      if (control.onLineState == '离线') return false
      if (-1 < flowData.value[name] && flowData.value[name] < 1) {
        return false
      } else if (flowData.value[name] == 0) {
        return false
      } else {
        return true
      }
    }
  })

  const soc = computed(() => {
    let bms = monitorStore.pcs_bms
    let bmsBau = monitorStore.pcs_bmsBau
    if (bms.length) return (bms.reduce((pre, item) => pre += Number(item.bms_4022), 0) / bms.length).toFixed(1)
    else if (bmsBau.length) return (bmsBau.reduce((pre, item) => pre += Number(item.bms_4022), 0) / bmsBau.length)
      .toFixed(1)
    return 0
  })

  if (deviceType.value == 10002) {
    monitorStore.selectDynamicGraphGroupFn({
      deviceSerialNumber: monitorStore.routeQuery.id,
      deviceType: monitorStore.routeQuery.type,
      groupId: monitorStore.routeQuery.groupId,
      groupType: monitorStore.routeQuery.groupType
    })
  } else {
    monitorStore.selectDynamicGraphFn({
      deviceSerialNumber: monitorStore.routeQuery.id,
      deviceType: monitorStore.routeQuery.type
    })
  }

  const groupId = computed(() => monitorStore.routeQuery.groupId)
  const isShowV54154 = computed(() => {
    let type = monitorStore.routeQuery.type
    let isGroup = isGroupFn(type)
    let control = isGroup ? monitorStore.groupControl[0] : monitorStore.control
    let versionStart = control?.jk_1000?.split('V')[1].split('.')[0]
    let versionTwo = control?.jk_1000?.split('V')[1].split('.')[1]
    let versionThere = control?.jk_1000?.split('V')[1].split('.')[2]
    if (versionStart == 5)
      if (versionTwo == 4154) return true
    return false
  })
</script>

<style scoped lang="scss">
  image {
    width: 100%;
    height: 100%;
  }

  .flow-item {
    position: relative;
    height: 250rpx;
    font-size: 10px;

    .item-ce {
      width: 70rpx;
      height: 50rpx;
      z-index: 100;
      margin-top: 24rpx;
    }

    .item-le {
      position: relative;
      right: -18rpx;
      bottom: 50rpx;

      .le-gird {
        position: relative;
        top: 68rpx;
        left: -116rpx;
        z-index: 100;

        image {
          width: 80rpx;
          height: 100rpx;
        }
      }

      .le-gird-line {
        height: 4rpx;
        width: 230rpx;
        background-color: #fb560a;
        position: relative;

        .rightToGird {
          position: absolute;
          animation: rightToGird 3s linear infinite;
        }

        .leftToGird {
          position: absolute;
          animation: leftToGird 3s linear infinite;
        }

        @keyframes rightToGird {
          0% {
            top: -7rpx;
            left: -12rpx;
          }

          100% {
            top: -7rpx;
            left: calc(100% - 12rpx);
          }
        }

        @keyframes leftToGird {
          100% {
            top: -7rpx;
            left: -12rpx;
          }

          0% {
            top: -7rpx;
            left: calc(100% - 12rpx);
          }
        }
      }
    }

    .item-ri {
      position: relative;
      left: -18rpx;
      bottom: 50rpx;
      display: flex;
      flex-direction: column;
      align-items: flex-end;

      .ri-cell {
        margin-top: 20rpx;
        z-index: 0;
        position: relative;
        right: -70rpx;
        top: 64rpx;
        z-index: 100;
        align-items: flex-end;

        &-color {
          color: $u-primary;
        }

        image {
          width: 50rpx;
          height: 50rpx;
        }
      }

      .ri-cell-line {
        position: relative;
        height: 4rpx;
        width: 230rpx;
        background-color: #fb560a;

        .rightToCell {
          position: absolute;
          animation: rightToCell 3s linear infinite;
        }

        .leftToCell {
          position: absolute;
          animation: leftToCell 3s linear infinite;
        }

        @keyframes rightToCell {
          0% {
            top: -7rpx;
            left: -12rpx;
          }

          100% {
            top: -7rpx;
            left: calc(100% - 14rpx);
          }
        }

        @keyframes leftToCell {
          0% {
            top: -7rpx;
            left: calc(100% - 14rpx);
          }

          100% {
            top: -7rpx;
            left: -12rpx;
          }
        }
      }
    }
  }
</style>