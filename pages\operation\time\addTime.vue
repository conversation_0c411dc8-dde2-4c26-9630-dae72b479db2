<template>
  <view class="add-box">
    <u-navbar leftText="" :title="title" bgColor="#fff" leftIconSize="20px" :autoBack="true" :placeholder="true">
    </u-navbar>
    <u-form labelPosition="left" :model="form" :rules="rules" ref="formRef" labelWidth="auto">
      <view class="add-item">
        <u-form-item :label="$t('时区')" prop="timeZone" :borderBottom="true" required>
          <u-input v-model="form.timeZone" :placeholder="$t('请输入时区')" border="none" />
        </u-form-item>
        <u-form-item :label="$t('时区地址')" prop="timeZoneAddress" :borderBottom="true" required>
          <u-input v-model="form.timeZoneAddress" :placeholder="$t('请输入')" border="none" />
        </u-form-item>
        <u-form-item :label="$t('时区地址') + '(EN)'" prop="timeZoneAddressUs" :borderBottom="true" required>
          <u-input v-model="form.timeZoneAddressUs" :placeholder="$t('请输入')" border="none" />
        </u-form-item>
        <u-form-item :label="$t('时区地址') + '(IT)'" prop="timeZoneAddressIt" :borderBottom="true" required>
          <u-input v-model="form.timeZoneAddressIt" :placeholder="$t('请输入')" border="none" />
        </u-form-item>
      </view>
    </u-form>
    <u-button type="primary" style="border-radius: 100rpx;" class="u-m-t-40"
      @click="handleConfirmClick">{{ $t('确认') }}</u-button>
  </view>
</template>

<script setup>
  import {
    ref
  } from 'vue'
  import {
    onLoad,
    onShow
  } from '@dcloudio/uni-app'
  import {
    addTime,
    editTime
  } from '@/api/time'

  const title = ref(uni.$t('添加时区'))
  onLoad((options) => {
    if (options.item) {
      form.value = {
        ...JSON.parse(decodeURIComponent(options.item)),
      }
      title.value = uni.$t('修改时区')
    } else {
      title.value = uni.$t('添加时区')
    }
  })

  const formRef = ref(null)
  const form = ref({
    timeZoneAddress: '',
    timeZoneAddressUs: '',
    timeZoneAddressIt: '',
    timeZone: '',
  })
  const rules = ref({
    'timeZoneAddress': {
      type: 'string',
      required: true,
      message: uni.$t('请输入'),
      trigger: ['blur', 'change'],
    },
    'timeZoneAddressUs': {
      type: 'string',
      required: true,
      message: uni.$t('请输入'),
      trigger: ['blur', 'change'],
    },
    'timeZoneAddressIt': {
      type: 'string',
      required: true,
      message: uni.$t('请输入'),
      trigger: ['blur', 'change'],
    },
    'timeZone': [{
      type: 'string',
      required: true,
      message: uni.$t('请输入时区'),
      trigger: ['blur', 'change'],
    }, {
      pattern: /^[+-](0[0-9]|1[0-4]):(00|15|30|45)$/,
      message: uni.$t('格式不正确'),
      trigger: ['blur', 'change'],
    }]
  })

  const handleConfirmClick = () => {
    if (formRef.value) {
      formRef.value.validate().then(valid => {
        if (valid) {
          if (title.value == uni.$t('添加时区')) {
            addTime({
              timeZoneAddress: form.value.timeZoneAddress,
              timeZoneAddressIt: form.value.timeZoneAddressIt,
              timeZoneAddressUs: form.value.timeZoneAddressUs,
              timeZone: form.value.timeZone,
            }).then(res => {
              uni.$u.toast(uni.$t('添加成功'))
              uni.navigateBack()
            }).catch(() => {
              uni.$u.toast(uni.$t('添加失败'))
            })
          } else {
            editTime({
              id: form.value.id,
              timeZoneAddress: form.value.timeZoneAddress,
              timeZoneAddressIt: form.value.timeZoneAddressIt,
              timeZoneAddressUs: form.value.timeZoneAddressUs,
              timeZone: form.value.timeZone,
            }).then(res => {
              uni.$u.toast(uni.$t('修改成功'))
              uni.navigateBack()
            }).catch(() => {
              uni.$u.toast(uni.$t('修改失败'))
            })
          }
        }
      })
    }
  }
</script>

<style lang="scss" scoped>
  .add-box {
    width: 100%;
    height: 100vh;
    padding: 20rpx 30rpx;
    overflow: auto;
  }

  .add-item {
    background-color: #fff;
    padding: 0 30rpx;
    border-radius: 6px;
  }

  .btn {
    height: 50rpx;
    background: rgba(0, 0, 0, 0);
  }

  :deep(.u-input__content__field-wrapper__field) {
    text-align: right !important;
  }

  :deep(.u-button__text) {
    font-size: 12px !important;
  }

  :deep(.u-datetime-picker) {
    .u-input {
      padding: 0 !important;
      border: none;
    }
  }

  :deep(.rate) {
    .u-form-item__body__right__content__slot {
      flex-direction: row-reverse !important;
    }
  }
</style>