<template>
  <view class="plan-list">
    <z-paging ref="paging" v-model="tableData" show-refresher-update-time refresher-update-time-key="priceList"
      @query="queryList" hide-empty-view>
      <template #top>
        <u-navbar :title="$t('分时电价')" leftIconSize="20px" :autoBack="true" :placeholder="true" class="tabbar">
          <template #right>
            <image src="../../../static/add.webp" mode="" class="top-add" @click="handleAddClick"></image>
          </template>
        </u-navbar>
        <view class="list-top">
          <view class=" flex justify-content align-items">
            <view class="flex u-flex-items-center mr-20 u-p-l-15" @click="handleSearchList">
              <view style="font-size: 12px;margin-right: 6rpx;">{{ slectText }}</view>
              <u-icon name="arrow-down-fill" size="10px"></u-icon>
            </view>
            <u-input :placeholder="$t('请输入关键字')" v-model="queryInfo.keyword" :customStyle="newSearchCustomStyle" class="input-search" placeholderStyle="fontSize: 12px" confirmType="search" fontSize="12px"
              @confirm="handleSearchClick">
              <template #prefix>
                <u-icon name="search" size="36"></u-icon>
              </template>
            </u-input>
            <view class="flex u-flex-items-center u-m-r-10 u-p-l-15" @click="handleSearchClick">
              <view style="font-size: 12px;">{{ $t('搜索') }}</view>
            </view>
          </view>
        </view>
      </template>
      <view class="items">
        <view class="items-wrap1" v-for="item in tableData" :key="item.id">
          <view class="items-wrap1-item flex" @click="handleItemClick(item)">
            <view class="items-wrap1-item-left flex u-flex-items-center">
              <image src="../../../static/planList.webp" mode="" style="width: 30rpx;height: 30rpx"></image>
              <view class="u-line-1 ml-5">{{ item.name }}</view>
            </view>
          </view>
          <view class="flex u-flex-between u-p-b-20" @click="handleDetailsClick(item)">
            <view>
              <view class="items-wrap1-item1 flex">
                <view class="u-line-1">{{ $t('创建人员') }}：{{ item.createBy ? item.createBy: '--' }}</view>
              </view>
              <view class="items-wrap1-item1 flex">
                <view class="u-line-1">{{ $t('创建时间') }}：<span class="color-grey">{{ item.createTime }}</span></view>
              </view>
            </view>
            <u-icon name="arrow-right" color="#333"></u-icon>
          </view>
          <view class="flex items-wrap1-item-bo u-p-t-20 u-flex-around u-flex-items-center">
            <view class="flex u-flex-items-center" @click="handleEditClick(item)">
              <image src="../../../static/edit.webp" mode="" style="width: 30rpx;height: 30rpx;margin-right: 20rpx">
              </image>
              {{ $t('修改') }}
            </view>
            <u-line direction="col" length="20rpx"></u-line>
            <view class="flex u-flex-items-center" @click="handleDeleteClick(item)">
              <image src="../../../static/remove.webp" mode="" style="width: 30rpx;height: 30rpx;margin-right: 20rpx">
              </image>
              {{ $t('删除') }}
            </view>
          </view>
        </view>
      </view>
      <view
        style="background-color: #fff;margin: 0 30rpx;border-radius: 6px;padding: 30rpx 0;height: 80vh;padding-top: 200rpx;"
        v-if="!tableData.length">
        <u-empty icon="../../../static/empty.webp" width="300" height="300" textSize="14px" :text="$t('暂无数据')">
          </u-empty>
      </view>
    </z-paging>

    <u-picker :show="isShowSelect" :defaultIndex="defaultSelectIndex" keyName="text" :columns="[options]"
      itemHeight="88" @confirm="handleSelectConfirm" :closeOnClickOverlay="true" @close="handleSelectCancel"
      @cancel="handleSelectCancel" :cancelText="$t('取消')" :confirmText="$t('确认')"></u-picker>
  </view>
</template>

<script setup>
  import {
    ref,
    getCurrentInstance,
    computed
  } from 'vue'
  import {
    onShow,
    onPageScroll
  } from '@dcloudio/uni-app'
  import {
    priceList,
    deletePrice
  } from '@/api/price.js'
  import otherColor from '../../../common/other.module.scss'
  import {
    checkRole
  } from '@/common/utils.js'
  import { newSearchCustomStyle } from '@/constant'

  const {
    proxy
  } = getCurrentInstance()

  const tableData = ref([])
  const paging = ref({
    pageNum: 1,
    pageSize: 10,
    total: 0
  })
  const queryInfo = ref({
    search: 'name',
    keyword: undefined
  })
  const priceListFn1 = async ({
    pageNum,
    pageSize
  }) => {
    const res = await priceList({
      pageNum,
      pageSize,
      [queryInfo.value.search]: queryInfo.value.keyword
    })
    paging.value.total = res.total
    return res.rows
  }

  const queryList = (pageNo, pageSize) => {
    // 组件加载时会自动触发此方法，因此默认页面加载时会自动触发，无需手动调用
    // 这里的pageNo和pageSize会自动计算好，直接传给服务器即可
    // 模拟请求服务器获取分页数据，请替换成自己的网络请求
    priceListFn1({
      pageNum: pageNo,
      pageSize
    }).then(res => {
      // 将请求的结果数组传递给z-paging
      proxy.$refs.paging.completeByTotal(res, paging.value.total);
    }).catch(res => {
      // 如果请求失败写this.$refs.paging.complete(false);
      // 注意，每次都需要在catch中写这句话很麻烦，z-paging提供了方案可以全局统一处理
      // 在底层的网络请求抛出异常时，写uni.$emit('z-paging-error-emit');即可
      proxy.$refs.paging.complete(false);
    })
  }
  /**
   * 搜索
   */
  const isShowSelect = ref(false)
  const defaultSelectIndex = ref([0])
  const options = ref([{
      text: uni.$t('方案名称'),
      value: 'name'
    },
    {
      text: uni.$t('创建人员'),
      value: 'createBy'
    },
  ])
  const slectText = computed(() => {
    return options.value.find(item => item.value == queryInfo.value.search)?.text
  })
  const handleSelectConfirm = ({
    value
  }) => {
    if (value[0].value != queryInfo.value.search) {
      queryInfo.value.keyword = undefined
      proxy.$refs.paging.reload()
    }
    queryInfo.value.search = value[0].value
    isShowSelect.value = false
  }
  const handleSelectCancel = () => {
    defaultSelectIndex.value = [options.value.findIndex(item => item.value == queryInfo.value.search)]
    isShowSelect.value = false
  }
  const handleSearchList = () => {
    isShowSelect.value = true
  }
  const handleSearchClick = () => {
    proxy.$refs.paging.reload()
  }
  /**
   * 添加
   */
  const handleAddClick = () => {
    uni.navigateTo({
      url: '/pages/operation/price/addPrice'
    })
  }
  /**
   * 修改
   */
  const handleEditClick = (item) => {
    if (!checkRole(['admin']) && item.id == 1) {
      return uni.$u.toast(uni.$t('您无权限哦~'))
    }
    uni.navigateTo({
      url: `/pages/operation/price/addPrice?id=${item.id}`
    })
  }
  /**
   * 删除
   */
  const handleDeleteClick = (item) => {
    if (!checkRole(['admin']) && item.id == 1) {
      return uni.$u.toast(uni.$t('您无权限哦~'))
    }
    uni.showModal({
      title: uni.$t('系统提示'),
      content: uni.$t('是否确认删除该数据项？'),
      confirmColor: otherColor.primaryColor,
      success: (res) => {
        if (res.confirm) {
          deletePrice({
            electricIds: item.id
          }).then(() => {
            setTimeout(() => {
              uni.$u.toast(uni.$t('删除成功'))
            }, 20)
            proxy.$refs.paging.reload()
          }).catch(() => {
            uni.$u.toast(uni.$t('删除失败'))
          })
        } else if (res.cancel) {
          console.log('用户点击取消');
        }
      }
    });
  }
  /**
   * 详情
   */
  const handleDetailsClick = (item) => {
    uni.navigateTo({
      url: `/pages/operation/price/priceDetails?id=${item.id}`
    })
  }
</script>

<style lang="scss" scoped>
  .plan-list {
    width: 100%;
    height: 100vh;

    .top-add {
      width: 50rpx;
      height: 50rpx;
    }
  }

  .list-top {
    z-index: 998;
    margin: 20rpx 30rpx 20rpx 30rpx;
    border-radius: $uni-border-radius-lg;

    .top-img {
      width: 32rpx;
      height: 32rpx;
      margin-right: 14rpx;
    }

    .input-search {
      border-radius: 100px;
      border-color: transparent !important;
    }
  }

  .items {
    margin: 0rpx 30rpx;

    &-wrap1 {
      background-color: #fff;
      border-radius: 6px;
      padding: 0 30rpx 30rpx 30rpx;
      margin-bottom: 20rpx;

      .actived {
        font-weight: bold;
        color: $u-primary;
      }

      &-item {
        padding: 20rpx 0;
        border-bottom: 1px solid $uni-bg-color-grey;
        justify-content: space-between;

        &-left {
          width: 80%;
        }

        &-bo {
          border-top: 1px solid $uni-bg-color-grey;
          width: 100%;
        }
      }

      &-item1 {
        padding: 20rpx 0 0 0;
        justify-content: space-between;
      }
    }
  }
</style>