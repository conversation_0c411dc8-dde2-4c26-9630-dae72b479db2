<template>
  <view class="operation">
    <u-navbar :title="$t('operationManage')" :bgColor="otherColor.bgColor" :placeholder="true" class="tabbar">
    </u-navbar>
    <view class="operation-items" v-if="isShowMenu('Jfpg') || isShowMenu('Price') || isShowMenu('Pattern')">
      <view class="operation-title">{{ $t('运维方案') }}</view>
      <view class="operation-wrapper">
        <view class="operation-item flex u-flex-column u-flex-items-center" @click="handleItemClick('jfpg')"
          v-if="isShowMenu('Jfpg')">
          <image src="../../static/jfpg.png" mode="" class="operation-item-img"></image>
          <view class="operation-item-text">
            {{ $t('削峰填谷') }}
          </view>
        </view>
        <view class="operation-item flex u-flex-column u-flex-items-center" @click="handleItemClick('price')"
          v-if="isShowMenu('Price')">
          <image src="../../static/price.png" mode="" class="operation-item-img"></image>
          <view class="operation-item-text">
            {{ $t('分时电价') }}
          </view>
        </view>
        <view class="operation-item flex u-flex-column u-flex-items-center" @click="handleItemClick('backup')"
          v-if="isShowMenu('Pattern')">
          <image src="../../static/backup.png" mode="" class="operation-item-img"></image>
          <view class="operation-item-text">
            {{ $t('备电方案') }}
          </view>
        </view>
        <view class="operation-item flex u-flex-column u-flex-items-center" @click="handleItemClick('time')"
          v-if="isShowMenu('Time')">
          <image src="../../static/time.png" mode="" class="operation-item-img"></image>
          <view class="operation-item-text">
            {{ $t('时区管理') }}
          </view>
        </view>
        <view class="operation-item flex u-flex-column u-flex-items-center" @click="handleItemClick('currency')"
          v-if="isShowMenu('Currency')">
          <image src="../../static/currency.png" mode="" class="operation-item-img"></image>
          <view class="operation-item-text">
            {{ $t('货币管理') }}
          </view>
        </view>
      </view>
    </view>
    <view class="operation-items" v-if="isShowMenu('Log')">
      <view class="operation-title">{{ $t('操作日志') }}</view>
      <view class="operation-wrapper">
        <view class="operation-item flex u-flex-column u-flex-items-center" @click="handleItemClick('instructLog')">
          <image src="../../static/instructionRecord.png" mode="" class="operation-item-img"></image>
          <view class="operation-item-text">
            {{ $t('指令记录') }}
          </view>
        </view>
      </view>
    </view>
    <view class="operation-items">
      <view class="operation-title">{{ $t('其他') }}</view>
      <view class="operation-wrapper">
        <view class="operation-item flex u-flex-column u-flex-items-center" @click="handleItemClick('question')">
          <image src="../../static/FAQ.png" mode="" class="operation-item-img"></image>
          <view class="operation-item-text">
            {{ $t('常见问题') }}
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
  import otherColor from '../../common/other.module.scss'
  import {
    isShowRoute
  } from '@/common/utils.js'

  const handleItemClick = (type) => {
    if (type == 'jfpg') {
      uni.navigateTo({
        url: '/pages/operation/jfpg/index'
      })
    } else if (type == 'price') {
      uni.navigateTo({
        url: '/pages/operation/price/index'
      })
    } else if (type == 'backup') {
      uni.navigateTo({
        url: '/pages/operation/backup/index'
      })
    } else if (type == 'instructLog') {
      uni.navigateTo({
        url: '/pages/operation/instructLog/index'
      })
    } else if (type == 'question') {
      uni.navigateTo({
        url: '/pages/operation/question/index'
      })
    } else if (type == 'time') {
      uni.navigateTo({
        url: '/pages/operation/time/index'
      })
    } else if (type == 'currency') {
      uni.navigateTo({
        url: '/pages/operation/currency/index'
      })
    }
  }

  const isShowMenu = (type) => {
    let routes = uni.cache.getItem('routes')
    let children = routes.find(item => item.name == 'Operation').children
    return children.findIndex(item => item.name == type) !== -1
  }
</script>

<style lang="scss" scoped>
  .operation {
    width: 100%;
    height: 100vh;
    overflow: hidden;

    &-items {
      padding: 30rpx;
      background-color: #fff;
      border-radius: 30rpx;
      margin: 20rpx 30rpx;
    }

    &-title {
      font-weight: 600;
      margin-bottom: 20rpx;
    }

    &-wrapper {
      display: grid;
      grid-template-columns: repeat(4, 20%);
      grid-gap: 20px;
      justify-items: start;
    }

    &-item {
      // margin-right: 80rpx;
      font-size: 12px;

      // color: $uni-text-color-grey;
      &-img {
        width: 80rpx;
        height: 80rpx;
        margin-bottom: 15rpx;
      }

      &-text {
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        word-break: break-all;
      }
    }
  }

  :deep(.u-navbar__content__left) {
    display: none;
  }
</style>