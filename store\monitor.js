import {
  defineStore
} from 'pinia'
import {
  ref
} from 'vue'
import {
  deviceMonitoringDetailTop,
  deviceMonitoringDetailRight,
  powerAnalysisStatistics,
  selectDynamicGraph,
  electricStatistics,
  exportDeviceMonitoring,
  // 组合类型
  powerAnalysisStatisticsGroup,
  selectDynamicGraphGroup
} from '@/api/monitor'
import {
  bindAliasQuery
} from '@/api/alias'
import {
  handleExport
} from '@/common/utils.js'
import {
  isGroupFn,
  isPowerFn,
  isBusFn
} from '@/hook/useDeviceType'
import {
  maxBy,
  minBy,
  isEmpty
} from 'lodash'

export const useMonitorStore = defineStore('monitor', () => {
  const routeQuery = ref({})
  /**
   * baseInfo
   */
  const baseInfo = ref({
    deviceName: '',
    deviceSerialNumber: '',
    deviceModel: '',
    projectName: '',
    deviceBatteryCapacity: '',
    deviceRatedPower: '',
    onLineState: '',
    dayOutputOfPlant: null,
    dayElectricityConsumption: null,
    projectAddress: '',
    deviceFactoryVersion: ''
  })
  const deviceMonitoringDetailTopFn = async (queryInfo) => {
    const res = await deviceMonitoringDetailTop({
      deviceSerialNumber: queryInfo.deviceSerialNumber,
      deviceType: queryInfo.deviceType
    })
    let data = res.data
    data.deviceBatteryCapacity = data.deviceBatteryCapacity ? Number(data.deviceBatteryCapacity).toFixed(2) :
      '0.00'
    data.deviceRatedPower = data.deviceRatedPower ? Number(data.deviceRatedPower).toFixed(2) : '0.00'
    data.photovoltaicInstalledCapacity = data.photovoltaicInstalledCapacity ? Number(data
      .photovoltaicInstalledCapacity).toFixed(2) : '0.00'
    data.dayPhotovoltaicPowerCapacityCalculate = data.dayPhotovoltaicPowerCapacityCalculate ? Number(data
      .dayPhotovoltaicPowerCapacityCalculate).toFixed(2) : '0.00'
    data.dayOutputOfPlant = data.dayOutputOfPlant ? Number(data.dayOutputOfPlant).toFixed(2) : '0.00'
    data.dayElectricityConsumption = data.dayElectricityConsumption ? Number(data.dayElectricityConsumption)
      .toFixed(2) : '0.00'
    baseInfo.value = data
  }
  /**
   * pcs
   */
  const control = ref({})
  const pcs_ac = ref([])
  const pcs_dc = ref([])
  const pcs_sts = ref([])
  const pcs_bms = ref([])
  const pcs_ele = ref([])
  const pcs_io = ref([])
  const pcs_cp = ref([])
  const pcs_cell = ref([])
  const pcs_bmsBau = ref([])
  const pcs_stsIo = ref([])
  const groupControl = ref([])
  const deviceMonitoringDetailRightFn = async (queryInfo) => {
    if (queryInfo.type == 'pcs') {
      pcs_ac.value = []
      pcs_dc.value = []
      pcs_sts.value = []
    } else if (queryInfo.type == 'bms') {
      pcs_bms.value = []
    } else if (queryInfo.type == 'electricMeter') {
      pcs_ele.value = []
    } else if (queryInfo.type == 'peripherals') {
      pcs_io.value = []
    } else if (queryInfo.type == 'chargingPile') {
      pcs_cp.value = []
    } else if (queryInfo.type == 'BMSCell') {
      pcs_cell.value = []
    } else if (queryInfo.type == 'bms-bau') {
      pcs_bmsBau.value = []
    } else if (queryInfo.type == 'Firefighting') {
      pcs_stsIo.value = []
    }
    const res = await deviceMonitoringDetailRight({
      deviceSerialNumber: queryInfo.deviceSerialNumber,
      type: queryInfo.type,
      deviceType: queryInfo.deviceType
    })
    let isGroup = isGroupFn(queryInfo.deviceType)
    if (queryInfo.type == 'control') { // 本地控制器
      if (isGroup) {
        let data = res.data
        data.forEach(item => {
          if (item && item['jk_1031']) {
            let power = parseFloat(item['jk_1031']) + parseFloat(item['jk_1032']) + parseFloat(item[
              'jk_1033'])
            item.power = power.toFixed(2)
          }
        })
        groupControl.value = data
      } else {
        let controlData = res.data ? res.data : {}
        if (controlData && controlData['jk_1031']) {
          let power = parseFloat(controlData['jk_1031']) + parseFloat(controlData['jk_1032']) + parseFloat(
            controlData[
              'jk_1033'])
          controlData.power = power.toFixed(2)
        }
        control.value = controlData
      }
    } else if (queryInfo.type == 'pcs') {
      if (isGroup) {
        let data = res.data
        let groupId = queryInfo.groupId
        data.forEach((item, index) => {
          let label = ''
          if (index == 0) {
            label = `${uni.$t('主机')}-${index + 1 < 10 ? '0' + (index + 1) : index + 1}`
          } else {
            label = `${uni.$t('从机')}-${index < 10 ? '0' + index : index}`
          }
          // 逻辑
          let keys = Object.keys(item)
          let ac = []
          let dc = []
          let sts = []
          if (keys.indexOf('infoRightAC') == -1) {
            ac = []
          } else {
            let acData = item.infoRightAC
            acData.forEach(item => {
              item.label = label
            })
            ac = acData
          }
          pcs_ac.value = [
            ...JSON.parse(JSON.stringify(pcs_ac.value)),
            ...ac
          ]
          if (keys.indexOf('infoRightDC') == -1) {
            dc = []
          } else {
            let dcData = item.infoRightDC
            dcData.forEach(item => {
              item.label = label
            })
            dc = dcData
          }
          pcs_dc.value = [
            ...pcs_dc.value,
            ...dc
          ]
          if (keys.indexOf('infoRightSTS') == -1) {
            sts = []
          } else {
            let stsData = item.infoRightSTS
            stsData.forEach(item => {
              item.label = label
            })
            sts = stsData
          }
          pcs_sts.value = [
            ...pcs_sts.value,
            ...sts
          ]
        })
      } else {
        let keys = Object.keys(res.data)
        let ac = []
        let dc = []
        let sts = []
        if (keys.indexOf('infoRightAC') == -1) {
          ac = []
        } else {
          ac = res.data.infoRightAC
        }
        pcs_ac.value = ac
        if (keys.indexOf('infoRightDC') == -1) {
          dc = []
        } else {
          dc = res.data.infoRightDC
        }
        pcs_dc.value = dc
        if (keys.indexOf('infoRightSTS') == -1) {
          sts = []
        } else {
          sts = res.data.infoRightSTS
        }
        pcs_sts.value = sts
      }
    } else if (queryInfo.type == 'bms') {
      if (isGroup) {
        let data = res.data
        let groupId = queryInfo.groupId
        data.forEach((item, index) => {
          let label = ''
          if (index == 0) {
            label = `${uni.$t('主机')}-${index + 1 < 10 ? '0' + (index + 1) : index + 1}`
          } else {
            label = `${uni.$t('从机')}-${index < 10 ? '0' + index : index}`
          }
          // 逻辑
          let bms = []
          if (!Object.keys(item).length) {
            bms = []
          } else {
            let bmsData = item.infoRightBMS
            bmsData.forEach(item => {
              item.label = label
            })
            bms = bmsData
          }
          pcs_bms.value = [
            ...pcs_bms.value,
            ...bms
          ]
        })
      } else {
        let bms = []
        if (!Object.keys(res.data).length) {
          bms = []
        } else {
          bms = res.data.infoRightBMS
        }
        pcs_bms.value = bms
      }
    } else if (queryInfo.type == 'electricMeter') {
      if (isGroup) {
        let data = res.data
        let groupId = queryInfo.groupId
        data.forEach((item, index) => {
          let label = ''
          if (index == 0) {
            label = `${uni.$t('主机')}-${index + 1 < 10 ? '0' + (index + 1) : index + 1}`
          } else {
            label = `${uni.$t('从机')}-${index < 10 ? '0' + index : index}`
          }
          // 逻辑
          let ele = []
          if (!Object.keys(item).length) {
            ele = []
          } else {
            let eleData = item.infoRightElectricMeter
            eleData.forEach(item => {
              item.label = label
            })
            ele = eleData
          }
          pcs_ele.value = [
            ...pcs_ele.value,
            ...ele
          ]
        })
      } else {
        let ele = []
        if (!Object.keys(res.data).length) {
          ele = []
        } else {
          ele = res.data.infoRightElectricMeter
        }
        pcs_ele.value = ele
      }
    } else if (queryInfo.type == 'peripherals') {
      if (isGroup) {
        let data = res.data
        let groupId = queryInfo.groupId
        data.forEach((item, index) => {
          let label = ''
          if (index == 0) {
            label = `${uni.$t('主机')}-${index + 1 < 10 ? '0' + (index + 1) : index + 1}`
          } else {
            label = `${uni.$t('从机')}-${index < 10 ? '0' + index : index}`
          }
          // 逻辑
          let io = []
          if (!Object.keys(item).length) {
            io = []
          } else {
            let ioData = item.infoRightPeripherals
            ioData.forEach(item => {
              item.label = label
            })
            io = ioData
          }
          pcs_io.value = [
            ...pcs_io.value,
            ...io
          ]
        })
      } else {
        let io = []
        if (!Object.keys(res.data).length) {
          io = []
        } else {
          io = res.data.infoRightPeripherals
        }
        pcs_io.value = io
      }
    } else if (queryInfo.type == 'chargingPile') {
      if (isGroup) {
        let data = res.data
        let groupId = queryInfo.groupId
        data.forEach((item, index) => {
          let label = ''
          if (index == 0) {
            label = `${uni.$t('主机')}-${index + 1 < 10 ? '0' + (index + 1) : index + 1}`
          } else {
            label = `${uni.$t('从机')}-${index < 10 ? '0' + index : index}`
          }
          // 逻辑
          let cp = []
          if (!Object.keys(item).length) {
            cp = []
          } else {
            let cpData = item.infoRightChargingPiles
            cpData.forEach(item => {
              item.label = label
            })
            cp = cpData
          }
          pcs_cp.value = [
            ...pcs_cp.value,
            ...cp
          ]
        })
      } else {
        let cp = []
        if (!Object.keys(res.data).length) {
          cp = []
        } else {
          cp = res.data.infoRightChargingPiles
        }
        pcs_cp.value = cp
      }
    } else if (queryInfo.type == 'BMSCell') {
      if (isGroup) {
        let data = res.data
        let groupId = queryInfo.groupId
        data.forEach((item, index) => {
          let label = ''
          if (index == 0) {
            label = `${uni.$t('主机')}-${index + 1 < 10 ? '0' + (index + 1) : index + 1}`
          } else {
            label = `${uni.$t('从机')}-${index < 10 ? '0' + index : index}`
          }
          // 逻辑
          let cell = []
          if (!Object.keys(item).length) {
            cell = []
          } else {
            let cellData = item.infoRightBMSCell
            cellData.forEach(item => {
              item.label = label
            })
            cell = cellData
          }
          pcs_cell.value = [
            ...pcs_cell.value,
            ...cell
          ]
        })
      } else {
        let cell = []
        if (!Object.keys(res.data).length) {
          cell = []
        } else {
          cell = res.data.infoRightBMSCell
        }
        pcs_cell.value = cell
      }
    } else if (queryInfo.type == 'bms-bau') {
      if (isGroup) {
        let data = res.data
        let groupId = queryInfo.groupId
        data.forEach((item, index) => {
          let label = ''
          if (index == 0) {
            label = `${uni.$t('主机')}-${index + 1 < 10 ? '0' + (index + 1) : index + 1}`
          } else {
            label = `${uni.$t('从机')}-${index < 10 ? '0' + index : index}`
          }
          // 逻辑
          let bmsBau = []
          if (!Object.keys(item).length) {
            bmsBau = []
          } else {
            let bmsBauData = item['infoRightBMS-BAU']
            bmsBauData.forEach(item => {
              item.label = label
            })
            bmsBau = bmsBauData
          }
          pcs_bmsBau.value = [
            ...pcs_bmsBau.value,
            ...bmsBau
          ]
        })
      } else {
        let bmsBau = []
        if (!Object.keys(res.data).length) {
          bmsBau = []
        } else {
          bmsBau = res.data['infoRightBMS-BAU']
        }
        pcs_bmsBau.value = bmsBau
      }
    } else if (queryInfo.type == 'Firefighting') {
      if (isGroup) {
        let data = res.data
        let groupId = queryInfo.groupId
        data.forEach((item, index) => {
          let label = ''
          if (index == 0) {
            label = `${uni.$t('主机')}-${index + 1 < 10 ? '0' + (index + 1) : index + 1}`
          } else {
            label = `${uni.$t('从机')}-${index < 10 ? '0' + index : index}`
          }
          // 逻辑
          let stsIo = []
          if (!Object.keys(item).length) {
            stsIo = []
          } else {
            let stsIoData = item.infoRightFirefighting
            stsIoData.forEach(item => {
              item.label = label
            })
            stsIo = stsIoData
          }
          pcs_stsIo.value = [
            ...pcs_stsIo.value,
            ...stsIo
          ]
        })
      } else {
        let stsIo = []
        if (!Object.keys(res.data).length) {
          stsIo = []
        } else {
          stsIo = res.data.infoRightFirefighting
        }
        pcs_stsIo.value = stsIo
      }
    }
  }
  /**
   * 折线图
   */
  const powerLineData = ref({})
  const lineQueryInfo = ref({})
  const powerAnalysisStatisticsFn = async (queryInfo) => {
    powerLineData.value = {
      times: [],
      powers: [],
      cells: [],
      loads: [],
      photovoltaics: [],
      bus: [],
      ac: null
    }
    const res = await powerAnalysisStatistics({
      ...lineQueryInfo.value,
      deviceSerialNumber: queryInfo.deviceSerialNumber,
      timeZone: queryInfo.timeZone
    })
    let times = res.data.map(item => uni.$u.timeFormat(item.sdt, 'hh:MM'))
    let cells = res.data.map(item => item.jk_1071) // 电池功率
    // 电网功率，有sts时1031~1033，无1051
    let powerBoolean = isPowerFn(queryInfo.deviceType)
    let powers = res.data.map(item => {
      if (powerBoolean) {
        if (item.jk_1051 !== null) return item.jk_1051.toFixed(2)
        return item.jk_1051
      } else {
        if (item.jk_1031 !== null) return (item.jk_1031 + item.jk_1032 + item.jk_1033).toFixed(2)
        return item.jk_1015
      }
    })
    // 负载功率
    let loads = res.data.map(item => {
      if (item.jk_1015 !== null) return (item.jk_1015 + item.jk_1016 + item.jk_1017).toFixed(2)
      return item.jk_1015
    })
    let photovoltaics = res.data.map(item => item.jk_1074) // 光伏功率
    // 直流母线功率，储能变流器，用1056，不是用1077
    let busBoolean = isBusFn(queryInfo.deviceType)
    let bus = res.data.map(item => {
      if (busBoolean) {
        if (item.jk_1056 !== null) return item.jk_1056.toFixed(2)
        return item.jk_1056
      } else {
        if (item.jk_1077 !== null) return item.jk_1077.toFixed(2)
        return item.jk_1077
      }
    })
    // soc
    let soc = res.data.map(item => item.bms_4022s)
    powerLineData.value = {
      times,
      powers,
      cells,
      loads,
      photovoltaics,
      bus,
      chargingPile: res.data.map(item => item.deviceMonitoringInfoRightChargingPiles && item
        .deviceMonitoringInfoRightChargingPiles.length ? item.deviceMonitoringInfoRightChargingPiles : []),
      soc,
      ac: res.data.length ? res.data[0].ac : null,
      em_10012s: res.data.map(item => isEmpty(item.em_10012s) ? 0.00 : Object.values(item.em_10012s).reduce((
        pre, cur) => pre + cur, 0))
    }
  }
  /**
   * 流动图
   */
  const flowData = ref({
    power: 0, // 电网功率
    cell: 0, // 电池功率
    load: 0, // 负载功率
    photovoltaic: 0 // 光伏功率
  })
  const selectDynamicGraphFn = async (queryInfo) => {
    const res = await selectDynamicGraph({
      deviceSerialNumber: queryInfo.deviceSerialNumber,
    })
    // 电网功率，有sts时1031~1033，无1051
    let powerBoolean = isPowerFn(queryInfo.deviceType)
    let power = powerBoolean ? (res.data.jk_1051 !== null ? res.data.jk_1051.toFixed(2) : '0.00') : ((res.data
      .jk_1031 + res.data.jk_1032 + res.data.jk_1033).toFixed(2))
    // 直流母线功率，储能变流器，用1056，不是用1077
    let busBoolean = isBusFn(queryInfo.deviceType)
    let bus = busBoolean ? (res.data.jk_1056 !== null ? res.data.jk_1056.toFixed(2) : '0.00') : (res.data
      .jk_1077 !== null ? res.data.jk_1077.toFixed(2) : '0.00')

    flowData.value = {
      power, // 电网功率
      cell: res.data.jk_1071 !== null ? res.data.jk_1071.toFixed(2) : '0.00', //电池功率
      load: (res.data.jk_1015 + res.data.jk_1016 + res.data.jk_1017).toFixed(2), // 负载功率
      photovoltaic: res.data.jk_1074 !== null ? res.data.jk_1074.toFixed(2) : '0.00', // 光伏功率
      bus: bus, // 直流母线功率
      chargingPiles: res.data.deviceMonitoringInfoRightChargingPiles ? res.data
        .deviceMonitoringInfoRightChargingPiles.map(item => {
          item.name = `${parseInt(item.dc) - 191000 + 1}#${uni.$t('充电桩')}`
          item.chargingPile_19006 = item.chargingPile_19006 !== null ? Number(item.chargingPile_19006)
            .toFixed(2) : 0.00
          item.chargingPile_19017 = item.chargingPile_19017 !== null ? Number(item.chargingPile_19017)
            .toFixed(2) : 0.00
          return item
        }) : [],
      em_10012s: isEmpty(res.data.em_10012s) ? 0.00 : Object.values(res.data.em_10012s).reduce((pre, cur) =>
        pre + cur, 0)
    }
  }
  /**
   * 电量统计
   */
  const electricData = ref({})
  const queryInfo = ref({
    deviceSerialNumber: '',
    type: 2,
    endTime: '',
    startTime: ''
  })
  const electricStatisticsFn = async (deviceSerialNumber) => {
    electricData.value = {}
    const res = await electricStatistics({
      ...queryInfo.value,
      deviceSerialNumber
    })
    electricData.value = {
      times: res.data.map(item => queryInfo.value.type == 2 ? uni.$u.timeFormat(item.dateTime, 'mm-dd') : item
        .dateTime),
      dischargeCapacityCalculate: res.data.map(item => item.dischargeCapacityCalculate), // 放电量
      chargeCapacityCalculate: res.data.map(item => item.chargeCapacityCalculate),
      photovoltaicPowerCapacityCalculate: res.data.map(item => item.photovoltaicPowerCapacityCalculate),
      jk_1083: res.data.map(item => item.jk_1083),
      jk_1084: res.data.map(item => item.jk_1084),
    }
  }
  /**
   * 导出报表
   */
  const exportDeviceMonitoringFn = async (queryInfo) => {
    const res = await exportDeviceMonitoring({
      deviceSerialNumber: queryInfo.deviceSerialNumber,
      startTime: queryInfo.startTime,
      endTime: queryInfo.endTime,
      type: queryInfo.type,
      deviceType: queryInfo.deviceType
    })
    handleExport(res, queryInfo.fileName)
  }

  /**
   * 组合类型
   */
  // 流动图
  const groupFlowData = ref({})
  const selectDynamicGraphGroupFn = async (queryInfo) => {
    groupFlowData.value = {}
    const res = await selectDynamicGraphGroup({
      deviceSerialNumber: queryInfo.deviceSerialNumber,
    })
    let groupData = {}
    let groupPower = 0
    let groupCell = 0
    let groupLoad = 0
    let groupPhotovoltaic = 0
    let groupEm10012s = 0
    queryInfo.groupId.forEach((item, index) => {
      let data = res.data.powerAnalysisStatistics[index]
      let type = queryInfo.groupType[index]
      let cell = data.jk_1071 !== null ? data.jk_1071.toFixed(2) : 0.00 // 电池功率
      groupCell += Number(cell)
      // 电网功率，有sts时1031~1033，无1051
      let powerBoolean = isPowerFn(type)
      let power = powerBoolean ? (data.jk_1051 !== null ? data.jk_1051.toFixed(2) : 0.00) : ((data.jk_1031 +
        data.jk_1032 + data.jk_1033).toFixed(2))
      groupPower += Number(power)
      // 负载功率
      let load = (data.jk_1015 + data.jk_1016 + data.jk_1017).toFixed(2)
      groupLoad += Number(load)
      let photovoltaic = data.jk_1074 !== null ? data.jk_1074.toFixed(2) : 0.00 // 光伏功率
      groupPhotovoltaic += Number(photovoltaic)
      // 电表有功功率
      let em_10012s = isEmpty(data.em_10012s) ? 0.00 : Object.values(data.em_10012s).reduce((pre, cur) =>
        pre + cur, 0)
      groupEm10012s += Number(em_10012s)
      let label = ''
      if (index == 0) {
        label = `${uni.$t('主机')} - ${index + 1 < 10 ? '0' + (index + 1) : index + 1}`
      } else {
        label = `${uni.$t('从机')} - ${index < 10 ? '0' + index : index}`
      }
      groupData[item] = {
        power,
        cell,
        load,
        photovoltaic,
        label,
        em_10012s
      }
    })
    groupFlowData.value = {
      power: res.data.groupData ? (res.data.zh_88100 !== null ? res.data.zh_88100.toFixed(2) : 0.00) :
        groupPower.toFixed(2), // 电网功率
      cell: res.data.groupData ? (res.data.zh_88103 !== null && res.data.zh_88103 ? res.data.zh_88103.toFixed(
        2) : 0.00) : groupCell.toFixed(2), //电池功率
      load: res.data.groupData ? (res.data.zh_88101 !== null ? res.data.zh_88101.toFixed(2) : 0.00) :
        groupLoad.toFixed(2), // 负载功率
      photovoltaic: res.data.groupData ? (res.data.zh_88102 !== null ? res.data.zh_88102.toFixed(2) : 0.00) :
        groupPhotovoltaic.toFixed(2), // 光伏功率
      // bus: bus, // 直流母线功率
      groupData,
      em_10012s: res.data.groupData ? 0.00 : groupEm10012s.toFixed(2)
    }
  }
  // 折线图
  const powerGroupLineData = ref({
    times: [],
    powers: [],
    cells: [],
    loads: [],
    photovoltaics: [],
    groupData: []
  })
  const lineGroupQueryInfo = ref({})
  const powerAnalysisStatisticsGroupFn = async (queryInfo) => {
    powerGroupLineData.value = {}
    const res = await powerAnalysisStatisticsGroup({
      ...lineGroupQueryInfo.value,
      deviceSerialNumber: queryInfo.deviceSerialNumber,
      timeZone: queryInfo.timeZone
    })
    if (!res.data.length) return
    let groupData = {}
    queryInfo.groupId.forEach((item, index) => {
      let data = res.data.map(item => item.powerAnalysisStatistics[index])
      let type = queryInfo.groupType[index]
      let cells = data.map(item => item.jk_1071 !== null ? item.jk_1071 : 0) // 电池功率
      // 电网功率，有sts时1031~1033，无1051
      let powerBoolean = isPowerFn(type)
      let powers = data.map(item => {
        if (powerBoolean) {
          if (item.jk_1051 !== null) return item.jk_1051.toFixed(2)
          return item.jk_1051 !== null ? item.jk_1051 : 0
        } else {
          if (item.jk_1031 !== null) return (item.jk_1031 + item.jk_1032 + item.jk_1033).toFixed(2)
          return item.jk_1015 !== null ? item.jk_1015 : 0
        }
      })
      // 负载功率
      let loads = data.map(item => {
        if (item.jk_1015 !== null) return (item.jk_1015 + item.jk_1016 + item.jk_1017).toFixed(2)
        return item.jk_1015 !== null ? item.jk_1015 : 0
      })
      let photovoltaics = data.map(item => item.jk_1074 !== null ? item.jk_1074 : 0) // 光伏功率
      let socs = data.map(item => item.bms_4022s) // soc
      let label = ''
      if (index == 0) {
        label = `${uni.$t('主机')}(${index + 1 < 10 ? '0' + (index + 1) : index + 1})`
      } else {
        label = `${uni.$t('从机')}(${index < 10 ? '0' + index : index})`
      }
      groupData[item] = {
        powers,
        cells,
        loads,
        photovoltaics,
        socs,
        label,
        em_10012s: data.map(item => isEmpty(item.em_10012s) ? 0.00 : Object.values(item.em_10012s).reduce(
          (pre, cur) => pre + cur, 0))
      }
    })
    let groupPowerS = res.data.map(item => item.powerAnalysisStatistics.reduce((pre, cur, curIndex) => {
      let powerBoolean = isPowerFn(queryInfo.groupType[curIndex])
      if (powerBoolean) {
        if (cur.jk_1051 !== null) return Number(Number(pre) + cur.jk_1051).toFixed(2)
        return Number(Number(pre) + cur.jk_1051)
      } else {
        if (cur.jk_1031 !== null) return Number(Number(pre) + (cur.jk_1031 + cur.jk_1032 + cur.jk_1033))
          .toFixed(2)
        return Number(Number(pre) + cur.jk_1015)
      }
    }, 0))
    let groupCellS = res.data.map(item => item.powerAnalysisStatistics.reduce((pre, cur) => Number(Number(pre) +
      cur.jk_1071).toFixed(2), 0))
    let groupLoadS = res.data.map(item => item.powerAnalysisStatistics.reduce((pre, cur) => {
      if (item.jk_1015 !== null) return Number(Number(pre) + (cur.jk_1015 + cur.jk_1016 + cur.jk_1017))
        .toFixed(2)
      return Number(Number(pre) + cur.jk_1015)
    }, 0))
    let groupPhotovoltaicS = res.data.map(item => item.powerAnalysisStatistics.reduce((pre, cur) => Number(
      Number(pre) + cur.jk_1074).toFixed(2), 0))
    let groupEm10012s = res.data.map(item => item.powerAnalysisStatistics.reduce((pre, cur) => {
      let em_10012s = isEmpty(cur.em_10012s) ? 0.00 : Object.values(cur.em_10012s).reduce((pre, cur) =>
        pre + cur, 0)
      return Number(Number(pre) + em_10012s).toFixed(2)
    }, 0))
    powerGroupLineData.value = {
      times: res.data.map(item => uni.$u.timeFormat(item.sdt, 'hh:MM')),
      powers: res.data[0].groupData ? res.data.map(item => item.zh_88100) : groupPowerS,
      cells: res.data[0].groupData ? res.data.map(item => item.zh_88103) : groupCellS,
      loads: res.data[0].groupData ? res.data.map(item => item.zh_88101) : groupLoadS,
      photovoltaics: res.data[0].groupData ? res.data.map(item => item.zh_88102) : groupPhotovoltaicS,
      // bus
      groupData,
      ac: queryInfo.deviceSerialNumber,
      em_10012s: res.data[0].groupData ? [] : groupEm10012s,
    }
  }


  /**
   * 横竖屏切换
   */
  const orientation = ref({
    landscape: false,
    isLine: false
  })

  /**
   *  获取测点
   */
  const aliasArr = ref([])
  const bindAliasQueryFn = async (queryInfo) => {
    const res = await bindAliasQuery(queryInfo)
    aliasArr.value = res.data
    return res.data
  }


  return {
    routeQuery,
    // 基本信息
    baseInfo,
    deviceMonitoringDetailTopFn,
    // 本地控制器、ac、dc、bms
    control,
    groupControl,
    pcs_ac,
    pcs_dc,
    pcs_sts,
    pcs_bms,
    pcs_ele,
    pcs_io,
    pcs_cp,
    pcs_cell,
    pcs_bmsBau,
    pcs_stsIo,
    deviceMonitoringDetailRightFn,
    // 折线图
    powerLineData,
    lineQueryInfo,
    powerAnalysisStatisticsFn,
    // 流动图
    flowData,
    selectDynamicGraphFn,
    // 柱状图、导出
    electricData,
    queryInfo,
    electricStatisticsFn,
    exportDeviceMonitoringFn,
    // 组合类型
    groupFlowData,
    selectDynamicGraphGroupFn,
    powerGroupLineData,
    lineGroupQueryInfo,
    powerAnalysisStatisticsGroupFn,
    // 横竖屏
    orientation,
    // 测点
    aliasArr,
    bindAliasQueryFn
  }
})