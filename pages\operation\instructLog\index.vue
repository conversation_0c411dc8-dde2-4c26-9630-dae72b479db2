<template>
  <view class="plan-list">
    <z-paging ref="paging" v-model="tableData" show-refresher-update-time refresher-update-time-key="instructList"
      @query="queryList" hide-empty-view>
      <template #top>
        <u-navbar :title="$t('指令记录')" leftIconSize="20px" :autoBack="true" :placeholder="true" class="tabbar">
        </u-navbar>
        <view class="list-top">
          <view class=" flex justify-content align-items">
            <view class="flex u-flex-items-center mr-20 u-p-l-15" @click="handleSearchList">
              <view style="font-size: 12px;margin-right: 6rpx;">{{ slectText }}</view>
              <u-icon name="arrow-down-fill" size="10px"></u-icon>
            </view>
            <u-input :placeholder="$t('请输入关键字')" v-model="queryInfo.keyword" :customStyle="newSearchCustomStyle" class="input-search" placeholderStyle="fontSize: 12px" confirmType="search" fontSize="12px"
              @confirm="handleSearchClick">
              <template #prefix>
                <u-icon name="search" size="36"></u-icon>
              </template>
            </u-input>
            <view class="flex u-flex-items-center u-m-r-10 u-p-l-15" @click="handleSearchClick">
              <view style="font-size: 12px;">{{ $t('搜索') }}</view>
            </view>
          </view>
        </view>
        <view class="select-type flex">
          <scroll-view scroll-x="true" style="white-space: nowrap;width: 100%">
            <view class="select-type-item" style="display: inline-block;" :style="{backgroundColor
            : index == current ? '#1989FA': '#fff', color: index == current ? '#fff': '#666'}"
              v-for="(item, index) in selectOptions" :key="index"
              @click="handleSelectItemClick(item.value, item.prop, index)">
              {{ item.label }}
            </view>
          </scroll-view>
        </view>
      </template>
      <view class="items">
        <view class="items-wrap1" v-for="item in tableData" :key="item.id">
          <view class="items-wrap1-item flex" @click="handleItemClick(item)">
            <view class="items-wrap1-item-left flex u-flex-items-center">
              <image src="../../../static/solve.svg" mode="" style="width: 30rpx;height: 30rpx"
                v-if="item.status == 0" />
              <image src="../../../static/solve-off.svg" mode="" style="width: 30rpx;height: 30rpx"
                v-else-if="item.status == 1" />
              <image src="../../../static/await.svg" mode="" style="width: 28rpx;height: 28rpx" v-else />
              <view class="u-line-1 ml-5">{{ getTypeText(item.type) }}</view>
            </view>
            <view class="flex u-flex-items-center">
              <u-tag :text="$t('执行成功')" size="mini" type="success" plain plainFill v-if="item.status == 0"></u-tag>
              <u-tag :text="$t('执行失败')" size="mini" type="error" plain plainFill v-else-if="item.status == 1"></u-tag>
              <u-tag :text="$t('已下发，等待执行')" size="mini" plain plainFill v-else></u-tag>
            </view>
          </view>
          <view class="flex u-flex-between u-p-b-20" @click="handleDetailsClick(item)">
            <view>
              <view class="items-wrap1-item1 flex">
                <view class="u-line-1">{{ $t('序列号') }}：{{ item.ac }}</view>
              </view>
              <view class="items-wrap1-item1 flex">
                <view class="u-line-1">{{ $t('下发参数') }}：{{ item.parameterSetting }}</view>
              </view>
              <view class="items-wrap1-item1 flex">
                <view class="u-line-1">{{ $t('下发参数值') }}：{{ item.parameterSettingValue }}</view>
              </view>
              <view class="items-wrap1-item1 flex">
                <view class="u-line-1">{{ $t('下发时间') }}：<span class="color-grey">{{ item.createTime }}</span></view>
              </view>
            </view>
            <u-icon name="arrow-right" color="#333" v-if="item.type == 5 || item.type == 8"></u-icon>
          </view>
          <view class="flex items-wrap1-item-bo u-p-t-20 u-flex-between u-flex-items-center">
            <view class="flex u-flex-items-center">
              <image src="../../../static/operUser.webp" mode="" style="width: 30rpx;height: 30rpx;margin-right: 6rpx">
              </image>
              {{ item.nickName }}
            </view>
            <view class="flex u-flex-items-center">
              {{ item.updateTime ? item.updateTime : '-' }}
            </view>
          </view>
        </view>
      </view>
      <view style="background-color: #fff;margin: 0 30rpx;border-radius: 6px;padding: 30rpx 0;height: 80vh;padding-top: 200rpx;"
        v-if="!tableData.length">
        <u-empty icon="../../../static/empty.webp" width="300" height="300" textSize="14px" :text="$t('暂无数据')">
        </u-empty>
      </view>
    </z-paging>

    <u-picker :show="isShowSelect" :defaultIndex="defaultSelectIndex" keyName="text" :columns="[options]"
      itemHeight="88" @confirm="handleSelectConfirm" :closeOnClickOverlay="true" @close="handleSelectCancel"
      @cancel="handleSelectCancel" :cancelText="$t('取消')" :confirmText="$t('确认')"></u-picker>
  </view>
</template>

<script setup>
  import {
    ref,
    getCurrentInstance,
    computed
  } from 'vue'
  import {
    onShow,
    onPageScroll
  } from '@dcloudio/uni-app'
  import {
    commandLogList
  } from '@/api/instruct.js'
  import otherColor from '../../../common/other.module.scss'
  import {
    checkRole
  } from '@/common/utils.js'
  import { newSearchCustomStyle } from '@/constant'

  const {
    proxy
  } = getCurrentInstance()

  const tableData = ref([])
  const paging = ref({
    pageNum: 1,
    pageSize: 10,
    total: 0
  })
  const queryInfo = ref({
    search: 'ac',
    keyword: undefined,
    status: undefined,
    type: undefined
  })
  const commandLogListFn1 = async ({
    pageNum,
    pageSize
  }) => {
    const res = await commandLogList({
      pageNum,
      pageSize,
      [queryInfo.value.search]: queryInfo.value.keyword,
      status: queryInfo.value.status,
      type: queryInfo.value.type
    })
    paging.value.total = res.total
    return res.rows
  }

  const queryList = (pageNo, pageSize) => {
    // 组件加载时会自动触发此方法，因此默认页面加载时会自动触发，无需手动调用
    // 这里的pageNo和pageSize会自动计算好，直接传给服务器即可
    // 模拟请求服务器获取分页数据，请替换成自己的网络请求
    commandLogListFn1({
      pageNum: pageNo,
      pageSize
    }).then(res => {
      // 将请求的结果数组传递给z-paging
      proxy.$refs.paging.completeByTotal(res, paging.value.total);
    }).catch(res => {
      // 如果请求失败写this.$refs.paging.complete(false);
      // 注意，每次都需要在catch中写这句话很麻烦，z-paging提供了方案可以全局统一处理
      // 在底层的网络请求抛出异常时，写uni.$emit('z-paging-error-emit');即可
      proxy.$refs.paging.complete(false);
    })
  }
  const typeOptions = ref([{
      label: uni.$t('全部'),
      value: undefined
    },
    {
      value: 1,
      label: uni.$t('系统设置参数')
    },
    {
      value: 6,
      label: uni.$t('系统开关机')
    },
    {
      value: 2,
      label: uni.$t('MAC参数')
    },
    {
      value: 3,
      label: uni.$t('MDC参数')
    },
    {
      value: 4,
      label: uni.$t('电池参数')
    },
    {
      value: 5,
      label: uni.$t('设备升级')
    },
    {
      value: 8,
      label: uni.$t('下发远程')
    },
  ])
  const getTypeText = computed(() => {
    return (type) => {
      if (type) return typeOptions.value.find(item => item.value == type).label
    }
  })
  const selectOptions = ref([{
      label: uni.$t('全部'),
      value: undefined,
      prop: undefined
    },
    {
      label: uni.$t('执行成功'),
      value: 0,
      prop: 'status'
    },
    {
      label: uni.$t('执行失败'),
      value: 1,
      prop: 'status'
    },
    {
      label: uni.$t('系统设置参数'),
      value: 1,
      prop: 'type'
    },
    {
      label: uni.$t('系统开关机'),
      value: 6,
      prop: 'type'
    },
    {
      label: uni.$t('MAC参数'),
      value: 2,
      prop: 'type'
    }, {
      label: uni.$t('MDC参数'),
      value: 3,
      prop: 'type'
    }, {
      label: uni.$t('电池参数'),
      value: 4,
      prop: 'type'
    }, {
      label: uni.$t('设备升级'),
      value: 5,
      prop: 'type'
    }, {
      label: uni.$t('下发远程'),
      value: 8,
      prop: 'type'
    },
  ])
  const current = ref(0)
  const handleSelectItemClick = (value, prop, index) => {
    current.value = index
    if (prop) {
      queryInfo.value[prop] = value
      if (prop == 'status') {
        queryInfo.value.type = undefined
      } else {
        queryInfo.value.status = undefined
      }
    } else {
      queryInfo.value.status = undefined
      queryInfo.value.type = undefined
    }
    proxy.$refs.paging.reload()
  }
  /**
   * 搜索
   */
  const isShowSelect = ref(false)
  const defaultSelectIndex = ref([0])
  const options = ref([{
      text: uni.$t('序列号'),
      value: 'ac'
    },
    {
      text: uni.$t('下发参数'),
      value: 'parameterSettingI18n'
    },
    {
      text: uni.$t('操作人员'),
      value: 'nickName'
    },
  ])
  const slectText = computed(() => {
    return options.value.find(item => item.value == queryInfo.value.search)?.text
  })
  const handleSelectConfirm = ({
    value
  }) => {
    if (value[0].value != queryInfo.value.search) {
      queryInfo.value.keyword = undefined
      proxy.$refs.paging.reload()
    }
    queryInfo.value.search = value[0].value
    isShowSelect.value = false
  }
  const handleSelectCancel = () => {
    defaultSelectIndex.value = [options.value.findIndex(item => item.value == queryInfo.value.search)]
    isShowSelect.value = false
  }
  const handleSearchList = () => {
    isShowSelect.value = true
  }
  const handleSearchClick = () => {
    proxy.$refs.paging.reload()
  }
  /**
   * 详情
   */
  const handleDetailsClick = (item) => {
    if (item.type == 5 || item.type == 8) {
      uni.navigateTo({
        url: `/pages/operation/instructLog/instructDetails?ac=${item.ac}&uuid=${item.uuid}&type=${item.type}`
      })
    }
  }
</script>

<style lang="scss" scoped>
  .plan-list {
    width: 100%;
    height: 100vh;

    .top-add {
      width: 50rpx;
      height: 50rpx;
    }
  }

  .list-top {
    z-index: 998;
    margin: 20rpx 30rpx 20rpx 30rpx;
    border-radius: $uni-border-radius-lg;

    .top-img {
      width: 32rpx;
      height: 32rpx;
      margin-right: 14rpx;
    }

    .input-search {
      border-radius: 100px;
      border-color: transparent !important;
    }
  }

  .select-type {
    margin: 0 30rpx 20rpx 30rpx;
    width: calc(100% - 60rpx);
    overflow-x: auto;

    &-item {
      height: 60rpx;
      line-height: 60rpx;
      padding: 0 30rpx;
      background: #fff;
      border-radius: 30rpx;
      font-size: 12px;
      margin-right: 20rpx;
    }
  }

  .items {
    margin: 0rpx 30rpx;

    &-wrap1 {
      background-color: #fff;
      border-radius: 6px;
      padding: 0 30rpx 30rpx 30rpx;
      margin-bottom: 20rpx;

      .actived {
        font-weight: bold;
        color: $u-primary;
      }

      &-item {
        padding: 20rpx 0;
        border-bottom: 1px solid $uni-bg-color-grey;
        justify-content: space-between;

        &-left {
          width: 50%;
        }

        &-bo {
          border-top: 1px solid $uni-bg-color-grey;
          width: 100%;
        }
      }

      &-item1 {
        padding: 20rpx 0 0 0;
        justify-content: space-between;
      }
    }
  }
</style>