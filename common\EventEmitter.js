class EventEmitter {
  constructor() {
    this.events = {};
  }
  // 事件监听
  on(evtName, callback) {
    if (this.events[evtName]) {
      this.events[evtName].push(callback);
    } else {
      this.events[evtName] = [];
    }
    return this;
  }
  // 触发
  emit(evtName, ...args) {
    const callbacks = this.events[evtName];
    if (callbacks) {
      callbacks.forEach((fn) => fn.apply(this, args));
    }
    return this;
  }
  // 删除订阅
  off(eventName, callback) {
    this.events[eventName] = this.events[eventName] = this.events[
      eventName
    ].filter((cb) => {
      return cb !== callback;
    });
  }
  // 订阅一次
  once(eventName, callback) {
    const proxyCallback = (...args) => {
      // 订阅事件，只定义一次
      callback.apply(this, args);
      // 回调函数执行完成之后就删除事件订阅
      this.off(eventName, proxyCallback);
    };
    this.on(eventName, proxyCallback);
  }
}

// 导出工厂
const mitt = (function(){
  return new EventEmitter()
})()
export default mitt
