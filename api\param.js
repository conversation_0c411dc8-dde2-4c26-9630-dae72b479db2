/**
 * MAC
 */
// 获取MAC参数信息
export const macInfo = (queryInfo) => uni.$u.http.get(`/system/MACsetting/getInfoByAc/${queryInfo.ac}`)

// 新增MAC参数
export const addMAC = (data) => uni.$u.http.post('/system/MACsetting', data)

// 参数下发MAC
export const sendParamMAC = (data) => uni.$u.http.post('/system/sendMqtt/argumentsJsonMAC', data, {
  timeout: 65000
})

// 修改
export const editMAC = (data) => uni.$u.http.put('/system/MACsetting', data)

/**
 *
 * MDC
 */
// 获取MDC参数信息
export const mdcInfo = (queryInfo) => uni.$u.http.get(`/system/MDCsetting/getInfoByAc/${queryInfo.ac}`)

// 新增MDC参数
export const addMDC = (data) => uni.$u.http.post('/system/MDCsetting', data)

// 参数下发MDC
export const sendParamMDC = (data) => uni.$u.http.post('/system/sendMqtt/argumentsJsonMDC', data, {
  timeout: 65000
})

// 修改
export const editMDC = (data) => uni.$u.http.put('/system/MDCsetting', data)

/**
 *
 * 电池
 */
// 获取电池参数信息
export const bmsInfo = (queryInfo) => uni.$u.http.get(`/system/batterySetting/getInfoByAc/${queryInfo.ac}`)

// 新增电池参数
export const addBMS = (data) => uni.$u.http.post('/system/batterySetting', data)

// 参数下发电池
export const sendParamBMS = (data) => uni.$u.http.post('/system/sendMqtt/argumentsJsonBattery', data, {
  timeout: 65000
})

// 修改
export const editBMS = (data) => uni.$u.http.put('/system/batterySetting', data)

/**
 *
 * 策略
 */
// 获取策略参数信息
export const systemInfo = (queryInfo) => uni.$u.http.get(`/system/policySetting/getInfoByAc/${queryInfo.ac}`)

// 新增策略参数
export const addSystem = (data) => uni.$u.http.post('/system/policySetting', data)

// 参数下发策略
export const sendParamSystem = (data) => uni.$u.http.post('/system/sendMqtt/argumentsJsonPolicy', data, {
  timeout: 65000
})

// 修改
export const editSystem = (data) => uni.$u.http.put('/system/policySetting', data)

/**
 * 升级
 */
// 参数下发
export const sendParamUpdate = (data) => uni.$u.http.post('/system/sendMqtt/upgradeJson', data, {
  timeout: 65000
})

/**
 *
 * 系统开关机
 */
// 获取系统开关机参数信息
export const onOffInfo = (queryInfo) => uni.$u.http.get(`/system/setting/getInfoByAc/${queryInfo.ac}`)

// 新增系统开关机参数
export const addOnOff = (data) => uni.$u.http.post('/system/setting', data)

// 参数下发系统开关机
export const sendParamOnOff = (data) => uni.$u.http.post('/system/sendMqtt/argumentsJsonSystem', data, {
  timeout: 65000
})

// 修改
export const editOnOff = (data) => uni.$u.http.put('/system/setting', data)


// 获取设备参数值
export const getJsonData = (data) => uni.$u.http.post('/system/sendMqtt/argumentsJsonTimerTask', data)

// 获取电芯数据配置详细信息
export const getCellConfig = (queryInfo) => uni.$u.http.get(`/system/BMSCellConfig/getInfoByAc/${queryInfo.ac}`)
