<template>
  <view class="edit-pwd">
    <u-form labelPosition="left" :model="form" :rules="rules" ref="formRef" :labelStyle="formLabelStyle" errorType="toast">
      <u-form-item prop="oldPassword" leftIcon="../../static/old-pwd.png">
        <u-input v-model="form.oldPassword" :password="isOldPwd" border="none" :placeholder="$t('请输入旧密码')"
          :adjustPosition="false" :placeholderStyle="`{
          color: ${otherColor.loginPlaceholderColor}
        }`" :color="otherColor.loginInputColor">
          <template #suffix>
            <image src="../../static/login-close.png" style="width: 32rpx;height: 32rpx"
              @click="handleShowPwd('show', 'old')" v-if="isOldPwd"></image>
            <image src="../../static/login-eye.png" style="width: 32rpx;height: 32rpx" @click="handleShowPwd('hide', 'old')"
              v-else></image>
          </template>
        </u-input>
      </u-form-item>
      <u-form-item prop="newPassword" leftIcon="../../static/new-pwd.png">
        <u-input v-model="form.newPassword" :password="isNewPwd" border="none" :placeholder="$t('请输入新密码')"
          :adjustPosition="false" :placeholderStyle="`{
          color: ${otherColor.loginPlaceholderColor}
        }`" :color="otherColor.loginInputColor">
          <template #suffix>
            <image src="../../static/login-close.png" style="width: 32rpx;height: 32rpx"
              @click="handleShowPwd('show', 'new')" v-if="isNewPwd"></image>
            <image src="../../static/login-eye.png" style="width: 32rpx;height: 32rpx" @click="handleShowPwd('hide', 'new')"
              v-else></image>
          </template>
        </u-input>
      </u-form-item>
      <u-form-item prop="confirmPassword" leftIcon="../../static/login-pwd.png">
        <u-input v-model="form.confirmPassword" :password="isConfirmPwd" border="none" :placeholder="$t('请确认新密码')"
          :adjustPosition="false" :placeholderStyle="`{
          color: ${otherColor.loginPlaceholderColor}
        }`" :color="otherColor.loginInputColor">
          <template #suffix>
            <image src="../../static/login-close.png" style="width: 32rpx;height: 32rpx"
              @click="handleShowPwd('show', 'confirm')" v-if="isConfirmPwd"></image>
            <image src="../../static/login-eye.png" style="width: 32rpx;height: 32rpx" @click="handleShowPwd('hide', 'confirm')"
              v-else></image>
          </template>
        </u-input>
      </u-form-item>
    </u-form>
    <u-button :text="$t('确认')" type="primary" :throttleTime="1000" shape="circle" class="u-m-t-40"
      @click="handleLoginClick" style="height: 90rpx"></u-button>
    <view class="ft12 u-m-t-20 color-grey" style="width: 100%;text-align: center;" @click="handleforgetPwd">
      {{ $t('不记得原来的密码？') }}
    </view>
  </view>
</template>

<script setup>
  import {
    ref,
    reactive,
    getCurrentInstance,
    computed
  } from 'vue';
  import {
    useLoginStore
  } from '@/store/login.js'
  import {
    languageArr
  } from '@/locale/index.js'
  import {
    initService
  } from '@/common/request'
  import {
    onShow
  } from '@dcloudio/uni-app'
  import otherColor from '../../common/other.module.scss'
  import { formLabelStyle } from '@/constant'

  const loginStore = useLoginStore()
  const {
    proxy
  } = getCurrentInstance()

  // 使用 reactive 创建响应式状态  
  const form = ref({
    oldPassword: '',
    newPassword: '',
    confirmPassword: '',
  })
  const rules = ref({
    oldPassword: {
      type: 'string',
      required: true,
      message: uni.$t('请输入密码'),
      trigger: ['blur', 'change'],
    },
    newPassword: {
      type: 'string',
      required: true,
      message: uni.$t('请输入密码'),
      trigger: ['blur', 'change'],
    },
    confirmPassword: {
      type: 'string',
      required: true,
      message: uni.$t('请输入密码'),
      trigger: ['blur', 'change'],
    },
  })
  // 使用 ref 创建响应式引用  
  const formRef = ref(null);

  const handleLoginClick = () => {
    if (formRef.value) {
      formRef.value.validate().then(async valid => {
        if (valid) {
          await loginStore.UpdateUserPwd({
            oldPassword: form.value.oldPassword,
            newPassword: form.value.oldPassword,
          })
          uni.showModal({
            title: uni.$t('系统提示'),
            content: uni.$t('修改密码成功，请重新登录！'),
            confirmColor: otherColor.primaryColor,
            success: (res) => {
              if (res.confirm) {
                uni.clearStorageSync()
                initLang()
                uni.reLaunch({
                  // url: '/pages/profile/index-copy'
                  url: '/pages/common/login'
                })
                uni.$u.toast(uni.$t('清空成功'))
              }
            }
          })
        }
      })
    }
  }
  /**
   * 是否显示密码
   */
  const isOldPwd = ref(true)
  const isNewPwd = ref(true)
  const isConfirmPwd = ref(true)
  const handleShowPwd = (type, typePwd) => {
    if (typePwd == 'old') {
      isOldPwd.value = type == 'show' ? false : true
    } else if (typePwd == 'new') {
      isNewPwd.value = type == 'show' ? false : true
    } else if (typePwd == 'confirm') {
      isConfirmPwd.value = type == 'show' ? false : true
    }
  }
  
  const handleforgetPwd = () => {
    uni.$u.toast(uni.$t('功能开发中~'))
  }
  
  onShow(() => {
  })
</script>

<style lang="scss" scoped>
  page {
    height: 100vh;
  }
  .edit-pwd {
    height: calc(100% - 60rpx);
    overflow: hidden;
    padding: 30rpx 30rpx 0 30rpx;
  }
  :deep(.u-input) {
      padding: 8rpx 0 !important;
    }
    :deep(.u-form-item) {
      background: #fff;
      border-radius: 100rpx;
      padding: 0 40rpx;
      margin-bottom: 40rpx;
    }
</style>
