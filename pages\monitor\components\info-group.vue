<template>
  <view class="data-time">
    <view>{{ $t('上报时间') }}</view>
    <view>{{ control[0]?.sdt }}({{ baseInfo.timeZone }})</view>
  </view>
  <view class="device-info">
    <view class="base-info">
      <view class="info-item flex justify-content align-items">
        <view class="bold">{{ $t('基本信息') }}</view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view class="color-grey">{{ $t('设备名称') }}</view>
        <view>{{ baseInfo.deviceName }}</view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view class="color-grey">{{ $t('设备型号') }}</view>
        <view>{{ baseInfo.deviceModel }}</view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view class="color-grey">{{ $t('设备类型') }}</view>
        <view>{{ getDeviceType(monitorStore.routeQuery.type, true) }}</view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view class="color-grey">{{ $t('所属项目') }}</view>
        <view>{{ baseInfo.projectName }}</view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view class="color-grey">{{ $t('时区地址') }}</view>
        <view>{{ baseInfo[getPropFn()] }}</view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view class="color-grey">{{ $t('设备地址') }}</view>
        <view>{{ baseInfo.projectAddress }}</view>
      </view>
    </view>

    <u-collapse :value="controlValue" class="group-info">
      <u-collapse-item v-for="(controlItem, index) in control" :key="controlItem.ac" :name="controlItem.ac">
        <template #title>
          <view class="info-ti">{{ controlItem.label }}</view>
        </template>
        <template #right-icon>
          <view><u-icon name="play-right-fill" size="12px"></u-icon></view>
        </template>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('设备类型') }}</view>
          <view>{{ controlItem.type }}
          </view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('额定容量') }}</view>
          <view v-if="bms.length">{{ bms[index]['bms_4040'] || '--' }}
            <span class="item-unit">kWh</span>
          </view>
          <view v-else>--
          </view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="controlItem.ac == groupId[0]">
          <view class="color-grey">{{ $t('额定功率') }}</view>
          <view>{{ baseInfo.deviceRatedPower }}
            <span class="item-unit">kW</span>
          </view>
        </view>
        <view class="info-item flex justify-content align-items"
          v-if="isShowPhotovoltaicInstalledCapacity(index) && controlItem.ac == groupId[0]">
          <view class="color-grey">{{ $t('光伏装机容量') }}</view>
          <view>{{ baseInfo.photovoltaicInstalledCapacity }}
            <span class="item-unit">kWp</span>
          </view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('序列号') }}</view>
          <view :class="['flex', 'align-items', 'u-flex-end']" style="text-align: center;">
            <view class="u-m-r-5">{{ controlItem.ac }}</view>
            <u-copy :content="controlItem.ac" :notice="$t('复制成功')" class="flex align-items" style="width: auto;">
              <image src="../../../static/copy.png" class="copy-img-width"></image>
            </u-copy>
          </view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('系统工作模式') }}</view>
          <view v-if="controlItem['jk_1001']">{{ parse.getOnAndOff(controlItem['jk_1001']) }}</view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('系统故障状态') }}</view>
          <view v-if="controlItem['jk_1002']">{{ controlItem['jk_1002'] == '1' ? $t('故障') : $t('正常') }}</view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('系统告警状态') }}</view>
          <view v-if="controlItem['jk_1005']">{{ controlItem['jk_1005'] == '1' ? $t('告警') : $t('正常') }}</view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('并离网状态') }}</view>
          <view v-if="controlItem['jk_1001']">{{ parse.get1001Bit14(controlItem['jk_1001']) }}</view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('AC运行状态') }}</view>
          <view v-if="controlItem['jk_1092']">{{ controlItem['jk_1092'] == "1" ? $t('运行') : $t('关闭') }}
          </view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('DC运行状态') }}</view>
          <view v-if="controlItem['jk_1093']">{{ controlItem['jk_1093'] == "1" ? $t('运行') : $t('关闭') }}
          </view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('系统状态') }}</view>
          <view v-if="controlItem['jk_1001']">{{ parse.getWorkState(controlItem['jk_1001']) }}</view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('运行模式') }}</view>
          <view>{{ get11056Fn(controlItem['jk_1105'], controlItem['jk_1001'])['1105'] }}</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('策略状态') }}</view>
          <view>{{ get11056Fn(controlItem['jk_1105'], controlItem['jk_1001'])['1001bit15'] }}</view>
        </view>
        <!-- <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('电池状态') }}</view>
          <view v-if="controlItem['jk_1004']">{{ controlItem['jk_1004'] == '1' ? $t('故障') :
        	          $t('正常') }}</view>
          <view v-else>--</view>
        </view> -->
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('电池高压状态') }}</view>
          <view v-if="controlItem['jk_1094']">{{ controlItem['jk_1094'] == '1' ? $t('上高压') :
        	          $t('未上高压') }}</view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('通讯状态') }}</view>
          <view v-if="controlItem['onLineState'] == '在线'">{{ $t('在线') }}</view>
          <view v-else-if="controlItem['onLineState'] == '离线'" style="color: #f56c6c;">{{ $t('离线') }}</view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="isShowV75019(index)">
          <view class="color-grey">{{ $t('空调状态') }}</view>
          <view v-if="controlItem['jk_1095']">{{ get1095Fn(controlItem['jk_1095']) }}</view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="isShowV75019(index)">
          <view class="color-grey">{{ $t('水浸状态') }}</view>
          <view v-if="controlItem['jk_1003']">{{ parse.get1003(controlItem['jk_1003'], 2) }}</view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="isShowV75019(index)">
          <view class="color-grey">{{ $t('烟感状态') }}</view>
          <view v-if="controlItem['jk_1003']">{{ parse.get1003(controlItem['jk_1003'], 3) }}</view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('防雷') }}</view>
          <view v-if="controlItem['jk_1003']">{{ parse.get1003(controlItem['jk_1003'], 4) }}</view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="isShowV75019(index)">
          <view class="color-grey">{{ $t('消防动作') }}</view>
          <view v-if="controlItem['jk_1003']">{{ parse.get1003(controlItem['jk_1003'], 5) }}</view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('储能功率') }}</view>
          <view>{{ controlItem['power'] }}<span class="item-unit">kW</span></view>
        </view>
        <!-- <view class="info-item flex justify-content align-items">
        	<view class="color-grey">负载功率</view>
        	<view>23<view class="item-unit">kW</view></view>
        </view>
        <view class="info-item flex justify-content align-items">
        	<view class="color-grey">光伏功率</view>
        	<view>23<view class="item-unit">kW</view></view>
        </view> -->
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('软件版本') }}</view>
          <view v-if="controlItem['jk_1000']">{{ controlItem['jk_1000'] }}</view>
          <view v-else>--</view>
        </view>
      </u-collapse-item>
    </u-collapse>

  </view>
</template>

<script setup>
  import {
    computed,
    ref,
    getCurrentInstance,
    toRefs
  } from 'vue'
  import {
    useMonitorStore
  } from '@/store/monitor.js'
  import * as parse from '@/common/parseBinaryToText.js'
  import {
    isGroupFn,
    isPhotovoltaicFn,
    isPowerFn,
    isGirdFn,
    getDeviceType
  } from '@/hook/useDeviceType.js'

  const {
    proxy
  } = getCurrentInstance()
  const monitorStore = useMonitorStore()

  const baseInfo = computed(() => monitorStore.baseInfo)
  const controlValue = ref([])
  const control = computed(() => {
    let data = monitorStore.groupControl
    data.forEach((item, index) => {
      controlValue.value.push(item.ac)
      let label = ''
      if (index == 0) {
        label = `${uni.$i18n().t('主机')} - ${index + 1 < 10 ? '0' + (index + 1) : index + 1}`
      } else {
        label = `${uni.$i18n().t('从机')} - ${index < 10 ? '0' + index : index}`
      }
      item.label = label
      let type = groupType.value[index]
      let typeText = getDeviceType(type, false)
      item.type = typeText
    })

    return data
  })
  const bms = computed(() => {
    let data = monitorStore.pcs_bms
    let bmsBau = monitorStore.pcs_bmsBau
    return data.length ? data : (bmsBau.length ? bmsBau : [])
  })
  const groupType = computed(() => monitorStore.routeQuery.groupType)
  const groupId = computed(() => monitorStore.routeQuery.groupId)
  const get11056Fn = computed(() => {
    return (pt1, pt2) => {
      switch (pt1) {
        case '0':
          return {
            '1001bit15': parse.get1001Bit15(pt2) == uni.$i18n().t('停止') ? uni.$i18n().t('未使用') : parse
              .get1001Bit15(pt2),
              '1105': uni.$i18n().t('手动模式'),
          }
        case '1':
          return {
            '1001bit15': parse.get1001Bit15(pt2),
              '1105': uni.$i18n().t('削峰填谷'),
          }
        case '2':
          return {
            '1001bit15': parse.get1001Bit15(pt2),
              '1105': uni.$i18n().t('后备模式'),
          }
        case '3':
          return {
            '1001bit15': parse.get1001Bit15(pt2),
              '1105': uni.$i18n().t('动态扩容'),
          }
        case '4':
          return {
            '1001bit15': parse.get1001Bit15(pt2),
              '1105': uni.$i18n().t('光伏消纳'),
          }
        default:
          return {
            '1001bit15': '--',
            '1105': '--',
          }
      }
    }
  })
  const get1095Fn = computed(() => {
    return (num) => {
      switch (num) {
        case '0':
          return uni.$i18n().t('关闭')
        case '1':
          return uni.$i18n().t('风机')
        case '2':
          return uni.$i18n().t('制冷')
        case '3':
          return uni.$i18n().t('加热')
        case '4':
          return uni.$i18n().t('除湿')
      }
    }
  })
  const isShowPhotovoltaicInstalledCapacity = computed(() => {
    return (index) => {
      let type = groupType.value[index]
      return isPhotovoltaicFn(type)
    }
  })
  const isShowLoad = computed(() => {
    return (index) => {
      let type = groupType.value[index]
      return isPowerFn(type)
    }
  })
  const isShowGird = computed(() => {
    return (index) => {
      let type = groupType.value[index]
      return isGirdFn(type)
    }
  })

  const isShowV75019 = computed(() => {
    return (index) => {
      let versionStart = control.value[index]?.jk_1000?.split('V')[1].split('.')[0]
      let versionTwo = control.value[index]?.jk_1000?.split('V')[1].split('.')[1]
      let versionThere = control.value[index]?.jk_1000?.split('V')[1].split('.')[2]
      if (versionStart == 7)
        if (versionTwo == 5019) return false
      return true
    }
  })
  const getPropFn = () => {
    const lang = uni.cache.getItem('language')
    switch (lang) {
      case 'zh':
        return 'timeZoneAddress'
      case 'en':
        return 'timeZoneAddressUs'
      case 'it':
        return 'timeZoneAddressIt'
      default:
        return 'timeZoneAddress'
    }
  }
</script>

<style scoped lang="scss">
  .data-time {
    display: flex;
    justify-content: space-between;
    background-color: #fff;
    padding: 20rpx 30rpx;
    margin-bottom: 20rpx;
    margin: 20rpx 30rpx;
    font-size: 10px;
    border-radius: 6px;
  }

  .device-info {

    .base-info,
    .group-info {
      /* background-color: $uni-bg-color; */
      background-color: #fff;
      border-radius: $uni-border-radius-lg;
      padding: 0 30rpx;
      margin: 20rpx 30rpx;
    }

    .info-item {
      padding: 30rpx 0;
      border-bottom: 1px solid $uni-bg-color-grey;

      .item-unit {
        font-size: 12px;
        color: $uni-text-color-grey;
        margin-left: 4rpx;
      }

      view:nth-child(2n) {
        width: 50%;
        text-align: right;
      }
    }

    .info-item:last-child {
      border-bottom: none;
    }
  }

  .info-ti {
    font-size: 14px;
    font-weight: bold;
  }

  :deep(.u-collapse-item__content__text) {
    padding: 0;
  }

  :deep(.u-cell__body) {
    padding: 30rpx 0;
  }

  :deep(.u-cell--clickable) {
    background-color: $uni-bg-color;
  }
</style>