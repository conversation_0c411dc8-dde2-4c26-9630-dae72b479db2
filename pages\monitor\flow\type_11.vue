<template>
  <view class="flow-item u-flex u-flex-center align-items bold">
    <view class="item-le">
      <view class="le-gird flex align-items">
        <image src="../../../static/flow_ac.png" mode=""></image>
        <view class="ml-5">{{ flowData.power }} kW</view>
      </view>
      <view class="le-gird-line mb-20" :style="[lineStyle('power')]">
        <template v-if="showCircle('power')">
          <u-icon name="arrow-down-fill" size="20rpx" color="#fb560a" class="rightToGird"
            v-if="flowData.power < -1"></u-icon>
          <u-icon name="play-left-fill" size="20rpx" color="#fb560a" class="leftToGird"
            v-else-if="flowData.power > 1"></u-icon>
        </template>
      </view>
      <view class="le-cp-line" :style="[lineStyle('pile')]">
        <template v-if="showCircle('pile')">
          <u-icon name="arrow-up-fill" size="20rpx" color="#fb560a" class="rightToCp" v-if="flowData.pile < 0"></u-icon>
          <u-icon name="play-left-fill" size="20rpx" color="#fb560a" class="leftToCp"
            v-else-if="flowData.pile > 0"></u-icon>
        </template>
      </view>
      <view class="le-cp flex align-items">
        <image src="../../../static/flow_cp.png" mode=""></image>
        <view class="ml-5">{{ flowData.pile }} kW</view>
      </view>
    </view>
    <view class="item-ce">
      <image src="../../../static/flow_hmi.png"></image>
    </view>
    <view class="item-ri u-m-t-20">
      <view class="ri-pv flex align-items u-flex-row-reverse">
        <image src="../../../static/flow_pv.png" mode=""></image>
        <view class="u-m-r-10">{{ flowData.photovoltaic }} kW</view>
      </view>
      <view class="ri-pv-line mb-20" :style="[lineStyle('photovoltaic')]">
        <template v-if="showCircle('photovoltaic')">
          <u-icon name="arrow-down-fill" size="20rpx" color="#fb560a" class="rightToPv"
            v-if="flowData.photovoltaic > 1"></u-icon>
          <u-icon name="play-right-fill" size="20rpx" color="#fb560a" class="leftToPv"
            v-else-if="flowData.photovoltaic < -1"></u-icon>
        </template>
      </view>
      <view class="ri-cell-line" :style="[lineStyle('cell')]">
        <template v-if="showCircle('cell')">
          <u-icon name="play-right-fill" size="20rpx" color="#fb560a" class="rightToCell"
            v-if="-flowData.cell < -1"></u-icon>
          <u-icon name="arrow-up-fill" size="20rpx" color="#fb560a" class="leftToCell"
            v-else-if="-flowData.cell > 1"></u-icon>
        </template>
      </view>
      <view class="ri-cell flex align-items u-flex-row-reverse">
        <view
          style="height: 85rpx;width: 85rpx;display: flex;justify-content: center;align-items: center;margin-top: 10rpx;">
          <zu-battery :battery="soc" width="54rpx" height="54rpx" fontSize="8px"
            style="margin-bottom: 10rpx;"></zu-battery>
        </view>
        <view class="u-m-r-10">
          <span>{{ flowData.cell }}</span> kW
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
  import {
    ref,
    computed
  } from 'vue'
  import {
    useMonitorStore
  } from '@/store/monitor.js'
  import {
    isShow3502Bit10
  } from '@/common/parseBinaryToText.js'

  const monitorStore = useMonitorStore()

  const deviceType = computed(() => monitorStore.routeQuery.type)
  const flowData = computed(() => {
    let data = deviceType.value == 10000 ? monitorStore.groupFlowData : monitorStore.flowData
    if (data.chargingPiles && data.chargingPiles.length) {
      data.pile = data.chargingPiles.reduce((pre, current) => pre + Number(current.chargingPile_19003), 0)
    } else {
      data.pile = null
    }
    return data
  })
  const lineStyle = computed(() => (type) => {
    let color = showCircle.value(type) ? '#fb560a' : '#eeeeef'
    let borderColor = ''
    switch (type) {
      case 'power':
        borderColor = `transparent transparent ${color} ${color}`
        break;
      case 'pile':
        borderColor = `${color} transparent transparent ${color}`
        break;
      case 'photovoltaic':
        borderColor = `transparent ${color} ${color} transparent`
        break;
      case 'cell':
        borderColor = `${color} ${color} transparent transparent`
        break;
    }
    return {
      borderColor
    }
  })
  // 流动拓补图展示圆
  const showCircle = computed(() => {
    let control = deviceType.value == 10000 ? monitorStore.groupControl[0] : monitorStore.control
    return (name) => {
      if (!control) return false
      if (control.onLineState == '离线') return false
      if (-1 <= flowData.value[name] && flowData.value[name] <= 1) {
        return false
      } else if (flowData.value[name] == 0) {
        return false
      } else {
        return true
      }
    }
  })

  const soc = computed(() => {
    let bms = monitorStore.pcs_bms
    let bmsBau = monitorStore.pcs_bmsBau
        if (bms.length) return (bms.reduce((pre, item) => pre += Number(item.bms_4022), 0) / bms.length).toFixed(1)
        else if (bmsBau.length) return (bmsBau.reduce((pre, item) => pre += Number(item.bms_4022), 0) / bmsBau.length).toFixed(1)
        return 0
  })

  monitorStore.selectDynamicGraphFn({
    deviceSerialNumber: monitorStore.routeQuery.id,
    deviceType: monitorStore.routeQuery.type
  })
</script>

<style scoped lang="scss">
  image {
    width: 100%;
    height: 100%;
  }

  .flow-item {
    position: relative;
    height: 400rpx;
    font-size: 10px;

    .item-ce {
      width: 70rpx;
      height: 50rpx;
      z-index: 100;
      margin-top: 24rpx;
    }

    .item-le {
      position: relative;
      right: -18rpx;

      .le-gird {
        position: relative;
        left: -30rpx;
        top: 14rpx;
        z-index: 100;

        image {
          width: 80rpx;
          height: 100rpx;
        }
      }

      .le-gird-line {
        height: 80rpx;
        width: 250rpx;
        border: 4rpx solid;
        border-color: transparent transparent #fb560a #fb560a;
        border-radius: 0 20rpx;
        position: relative;

        .rightToGird {
          position: absolute;
          animation: rightToGird 3s linear infinite;
        }

        .leftToGird {
          position: absolute;
          animation: leftToGird 3s linear infinite;
        }

        @keyframes rightToGird {
          0% {
            top: -14rpx;
            left: -12rpx;
            transform: rotate(0);
          }
        
          47% {
            top: calc(100% - 40rpx);
            left: -12rpx;
            transform: rotate(0);
          }
        
          50% {
            top: calc(100% - 10rpx);
            left: -12rpx;
            transform: rotate(-45deg);
          }
        
          53% {
            top: calc(100% - 8rpx);
            left: 8rpx;
            transform: rotate(-90deg);
          }
        
          100% {
            top: calc(100% - 8rpx);
            left: calc(100% - 12rpx);
            transform: rotate(-90deg);
          }
        }
        
        @keyframes leftToGird {
          100% {
            top: -14rpx;
            left: -13rpx;
            transform: rotate(90deg);
          }
        
          53% {
            top: calc(100% - 48rpx);
            left: -13rpx;
            transform: rotate(90deg);
          }
        
          50% {
            top: calc(100% - 16rpx);
            left: -10rpx;
            transform: rotate(45deg);
          }
        
          47% {
            top: calc(100% - 7rpx);
            left: -10rpx;
            transform: rotate(0);
          }
        
          0% {
            top: calc(100% - 7rpx);
            left: calc(100% - 8rpx);
            transform: rotate(0);
          }
        }
      }

      .le-cp {
        position: relative;
        left: -30rpx;
        bottom: 14rpx;

        image {
          width: 80rpx;
          height: 70rpx;
        }
      }

      .le-cp-line {
        position: relative;
        height: 90rpx;
        width: 250rpx;
        border: 2px solid;
        border-color: #fb560a transparent transparent #fb560a;
        border-radius: 20rpx 0;

        .rightToCp {
          position: absolute;
          animation: rightToCp 3s linear infinite;
        }

        .leftToCp {
          position: absolute;
          animation: leftToCp 3s linear infinite;
        }

        @keyframes rightToCp {
          100% {
            top: -12rpx;
            left: calc(100% - 10rpx);
            transform: rotate(90deg);
          }

          53% {
            top: -12rpx;
            left: 0;
            transform: rotate(90deg);
          }

          50% {
            top: 0rpx;
            left: -10rpx;
            transform: rotate(-45deg);
          }

          47% {
            top: 14rpx;
            left: -12rpx;
            transform: rotate(0);
          }

          0% {
            top: calc(100% - 14rpx);
            left: -12rpx;
            transform: rotate(0);
          }
        }

        @keyframes leftToCp {
          100% {
            top: calc(100% - 8rpx);
            left: -10rpx;
            transform: rotate(-90deg);
          }

          53% {
            top: 8rpx;
            left: -10rpx;
            transform: rotate(-90deg);
          }

          50% {
            top: 0;
            left: -8rpx;
            transform: rotate(-45deg);
          }

          47% {
            top: -11rpx;
            left: 8rpx;
            transform: rotate(0);
          }

          0% {
            top: -11rpx;
            left: calc(100% - 8rpx);
            transform: rotate(0);
          }
        }
      }
    }

    .item-ri {
      position: relative;
      left: -18rpx;
      // display: flex;
      // flex-direction: column;
      // align-items: flex-end;

      .ri-pv {
        margin-top: 20rpx;
        z-index: 0;
        position: relative;
        right: -32rpx;
        top: 10rpx;
        z-index: 100;

        image {
          width: 80rpx;
          height: 90rpx;
        }
      }

      .ri-pv-line {
        position: relative;
        height: 80rpx;
        width: 250rpx;
        border: 2px solid;
        border-color: transparent #fb560a #fb560a transparent;
        border-radius: 10px 0;

        .rightToPv {
          position: absolute;
          animation: rightToPv 3s linear infinite;
        }

        .leftToPv {
          position: absolute;
          animation: leftToPv 3s linear infinite;
        }

        @keyframes rightToPv {
          0% {
            top: -12rpx;
            left: calc(100% - 8rpx);
            transform: rotate(0);
          }
        
          47% {
            top: calc(100% - 30rpx);
            left: calc(100% - 8rpx);
            transform: rotate(0);
          }
        
          50% {
            top: calc(100% - 16rpx);
            left: calc(100% - 14rpx);
            transform: rotate(45deg);
          }
        
          53% {
            top: calc(100% - 8rpx);
            left: calc(100% - 18rpx);
            transform: rotate(90deg);
          }
        
          100% {
            top: calc(100% - 8rpx);
            left: -10rpx;
            transform: rotate(90deg);
          }
        }
        
        @keyframes leftToPv {
          0% {
            top: calc(100% - 6rpx);
            left: -7px;
            transform: rotate(0);
          }
        
          47% {
            top: calc(100% - 6rpx);
            left: calc(100% - 20rpx);
            transform: rotate(0);
          }
        
          50% {
            top: calc(100% - 14rpx);
            left: calc(100% - 14rpx);
            transform: rotate(-45deg);
          }
        
          53% {
            top: calc(100% - 20rpx);
            left: calc(100% - 6rpx);
            transform: rotate(-90deg);
          }
        
          100% {
            top: -8rpx;
            left: calc(100% - 6rpx);
            transform: rotate(-90deg);
          }
        }
      }

      .ri-cell {
        position: relative;
        right: -41rpx;
        bottom: 24rpx;

        &-color {
          color: $u-primary;
        }

        image {
          width: 85rpx;
          height: 85rpx;
        }
      }

      .ri-cell-line {
        position: relative;
        height: 90rpx;
        width: 250rpx;
        border: 2px solid;
        border-color: #fb560a #fb560a transparent transparent;
        border-radius: 0 10px;

        .rightToCell {
          position: absolute;
          animation: rightToCell 3s linear infinite;
        }

        .leftToCell {
          position: absolute;
          animation: leftToCell 3s linear infinite;
        }

        @keyframes rightToCell {
          0% {
            top: -12rpx;
            left: -8rpx;
            transform: rotate(0);
          }
        
          47% {
            top: -12rpx;
            left: calc(100% - 20rpx);
            transform: rotate(0);
          }
        
          50% {
            top: -4rpx;
            left: calc(100% - 14rpx);
            transform: rotate(45deg);
          }
        
          53% {
            top: 8rpx;
            left: calc(100% - 10rpx);
            transform: rotate(90deg);
          }
        
          100% {
            top: calc(100% - 8rpx);
            left: calc(100% - 10rpx);
            transform: rotate(90deg);
          }
        }
        
        @keyframes leftToCell {
          0% {
            top: calc(100% - 8rpx);
            left: calc(100% - 10rpx);
            transform: rotate(0);
          }
        
          47% {
            top: 8rpx;
            left: calc(100% - 10rpx);
            transform: rotate(0);
          }
        
          50% {
            top: -8rpx;
            left: calc(100% - 14rpx);
            transform: rotate(-45deg);
          }
        
          53% {
            top: -12rpx;
            left: calc(100% - 20rpx);
            transform: rotate(-90deg);
          }
        
          100% {
            top: -12rpx;
            left: -8rpx;
            transform: rotate(-90deg);
          }
        }
      }
    }
  }
</style>