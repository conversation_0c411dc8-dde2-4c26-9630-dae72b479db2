<template>
  <view class="device-param">
    <template v-if="isGroup">
      <u-subsection :list="groupIdName" mode="subsection" :current="current" keyName="label" fontSize="14px"
        style="width: 100%;" :activeColor="otherColor.primaryColor" :inactiveColor="otherColor.mainColor"
        @change="handleIdChange"></u-subsection>
    </template>
    <template v-for="item in paramList" :key="item.name">
      <u-button type="primary" style="margin: 20rpx 0;border-radius: 12rpx;"
        @click="handleClick(item)">{{ item.name }}</u-button>
    </template>
  </view>
</template>

<script setup>
  import {
    ref,
    toRefs,
    computed,
    watch
  } from 'vue'
  import {
    useMonitorStore
  } from '@/store/monitor.js'
  import {
    useParamStore
  } from '@/store/param.js'
  import {
    isGroupFn
  } from '@/hook/useDeviceType.js'
  import otherColor from '../../../common/other.module.scss'
  import {
    isShowPerm
  } from '@/common/utils'

  // const {
  //   pcs_ac,
  //   pcs_dc,
  //   pcs_bms,
  //   pcs_io,
  //   aliasArr,
  //   routeQuery,
  //   control,
  //   groupControl,
  //   pcs_bmsBau
  // } = toRefs(useMonitorStore())
  const monitorStore = useMonitorStore()

  const isGroup = computed(() => isGroupFn(monitorStore.routeQuery.type))
  const current = ref(0)
  const groupIdName = computed(() => {
    let groupId = monitorStore.routeQuery.groupId
    return groupId.map((item, index) => {
      let label = ''
      if (index == 0) {
        label = `${uni.$t('主机')} - ${index + 1 < 10 ? '0' + (index + 1) : index + 1}`
      } else {
        label = `${uni.$t('从机')} - ${index < 10 ? '0' + index : index}`
      }
      return {
        ac: item,
        label
      }
    })
  })

  const getAliasArrFn = () => {
    monitorStore.aliasArr = []
    let groupId = monitorStore.routeQuery.groupId
    if (isGroup.value) {
      groupId.forEach(async (item) => {
        const data = await useMonitorStore().bindAliasQueryFn({
          ac: item,
          enable: 1,
          type: 1
        })
        monitorStore.aliasArr = [...monitorStore.aliasArr, ...data]
      })
    } else {
      useMonitorStore().bindAliasQueryFn({
        ac: monitorStore.routeQuery.id,
        enable: 1,
        type: 1
      }).then(data => {
        monitorStore.aliasArr = [...monitorStore.aliasArr, ...data]
      })
    }
  }
  getAliasArrFn()

  const isExist = computed(() => {
    return (type) => {
      if (isGroup.value) {
        return monitorStore[type]?.findIndex(item => item.ac == monitorStore.routeQuery.groupId[current
          .value]) !== -1
      } else {
        return monitorStore[type].length
      }
    }
  })
  const paramList = ref([])
  const getParamListFn = () => {
    let data = []
    if (isShowPerm(['system:sendMqtt:argumentsJsonPolicy'])) {
      data.push({
          name: uni.$t('策略类参数设置')
        },
        {
          name: uni.$t('系统开关机')
        }
      )
    }
    if (monitorStore.pcs_ac.length && isExist.value('pcs_ac') && isShowPerm(['system:sendMqtt:argumentsJsonMAC']))
      data.push({
        name: uni.$t('MAC参数设置')
      })
    if (monitorStore.pcs_dc.length && isExist.value('pcs_dc') && isShowPerm(['system:sendMqtt:argumentsJsonMDC']))
      data.push({
        name: uni.$t('MDC参数设置')
      })
    if ((monitorStore.pcs_bms.length && isExist.value('pcs_bms')) && isShowPerm([
        'system:sendMqtt:argumentsJsonBattery'
      ]) || (monitorStore.pcs_bmsBau.length && isExist
        .value('pcs_bmsBau')) && isShowPerm(['system:sendMqtt:argumentsJsonBattery'])) data.push({
      name: uni.$t('电池参数设置')
    })
    // if (isShowPerm(['system:sendMqtt:upgradeJson'])) {
    //   data.push({
    //     name: uni.$t('在线升级')
    //   })
    // }
    // // 别名
    // if (monitorStore.pcs_io.length) {
    //   const set = new Set()
    //   monitorStore.pcs_io.forEach(item => {
    //     for (let i = 1; i < 11; i++) {
    //       if (item[`peripherals_180${i < 10 ? '0' + i : i}`]) {
    //         let value = monitorStore.aliasArr.find(item => item.point ==
    //           `181${(i - 1) < 10 ? '0' + (i - 1) : (i - 1)}`)
    //         if (value) {
    //           set.add({
    //             name: value.alias,
    //             point: value.point,
    //             pointId: value.pointId
    //           })
    //         }
    //       }
    //     }
    //   })
    //   data = [...data, ...set]
    // }
    return data
  }
  paramList.value = getParamListFn()
  const handleIdChange = (e) => {
    current.value = e
    paramList.value = getParamListFn()
  }

  const handleClick = ({
    name
  }) => {
    switch (name) {
      case uni.$t('策略类参数设置'):
        uni.navigateTo({
          url: `/pages/monitor/param/strategy?index=${current.value}`
        })
        break;
      case uni.$t('系统开关机'):
        uni.navigateTo({
          url: `/pages/monitor/param/onOff?index=${current.value}`
        })
        break;
      case uni.$t('MAC参数设置'):
        uni.navigateTo({
          url: `/pages/monitor/param/MAC?index=${current.value}`
        })
        break;
      case uni.$t('MDC参数设置'):
        uni.navigateTo({
          url: `/pages/monitor/param/MDC?index=${current.value}`
        })
        break;
      case uni.$t('电池参数设置'):
        uni.navigateTo({
          url: `/pages/monitor/param/BMS?index=${current.value}`
        })
        break;
      case uni.$t('在线升级'):
        break;
      default:
        uni.navigateTo({
          url: '/pages/monitor/param/IoOnOff'
        })
    }
  }

  const getDeviceLocalData = () => {
    let groupId = monitorStore.routeQuery.groupId
    if (isGroup.value) {
      Promise.all(groupId.map((item, index) => {
        let data = monitorStore.groupControl[index]
        if (data.onLineState && data['onLineState'] != '离线') {
          return useParamStore().getJsonDataFn({
            ac: item,
            types: [1, 2, 5, 6, 7]
          })
        }
      }))
    } else {
      if (monitorStore.control.onLineState && monitorStore.control['onLineState'] != '离线') {
        useParamStore().getJsonDataFn({
          ac: monitorStore.routeQuery.id,
          types: [1, 2, 5, 6, 7]
        })
      }
    }
  }
  getDeviceLocalData()
</script>

<style scoped lang="scss">
  .device-param {
    padding: 30rpx
  }
</style>