<template>
  <view class="device-info">
    <view class="base-info">
      <view class="info-item flex justify-content align-items">
        <view class="bold flex align-items">
          <image src="../../../static/detail.png" class="item-img"></image>
          <span>{{ $t('备电信息') }}</span>
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('方案名称') }}</view>
        <view class="u-line-1">{{ info.name }}
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('电网充电') }}</view>
        <view>{{ info.setting1901 == '1' ? $t('使能') : $t('不使能') }}
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('发电机') }}</view>
        <view>{{ info.setting1902 == '1' ? $t('使能') : $t('不使能') }}
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('发电机充电') }}</view>
        <view>{{ info.setting1903 == '1' ? $t('使能') : $t('不使能') }}
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('电池充电功率') }}</view>
        <view>{{ info.setting1904 || '--' }}
          <span class="item-unit">kW</span>
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('备电保持SOC') }}</view>
        <view>{{ info.setting1905 || '--' }}
          <span class="item-unit">%</span>
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('创建人员') }}</view>
        <view>{{ info.createBy }}
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('创建时间') }}</view>
        <view>{{ info.createTime }}
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
  import { ref, computed } from 'vue'
  import { onLoad, onShow } from '@dcloudio/uni-app'
  import { lookBackup } from '@/api/backup.js'
  
  const id = ref()
  onLoad((options) => {
    id.value = options.id
  })
  onShow(() => {
    getInfoFn()
  })
  
  const info = ref({})
  const getInfoFn = async () => {
    const res = await lookBackup({ id: id.value })
    info.value = res.data
  }
</script>

<style lang="scss" scoped>
  .device-info {
    height: 100vh;
    overflow: auto;
    .base-info, .group-info {
      background-color: $uni-bg-color;
      border-radius: $uni-border-radius-lg;
      padding: 0 30rpx;
      margin: 20rpx 30rpx;
    }
  
    .info-item {
      padding: 30rpx 0;
      border-bottom: 1px solid $detail-border-color;
      
      .item-img {
        width: 30rpx;
        height: 30rpx;
        margin-right: 10rpx;
      }
  
      .item-unit {
        font-size: 12px;
        // color: $uni-text-color-grey;
        margin-left: 4rpx;
      }
  
      view:nth-child(2n) {
        width: 50%;
        text-align: right;
      }
    }
  
    .info-item:last-child {
      border-bottom: none;
    }
  }
</style>
