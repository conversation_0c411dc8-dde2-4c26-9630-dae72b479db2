// 获取列表
export const ossList = (queryInfo) => uni.$u.http.get('/system/OSSFile/list', {
  params: queryInfo
})

// 新增
export const addOss = (data) => uni.$u.http.post('/system/OSSFile', data)

// 修改
export const editOss = (data) => uni.$u.http.put('/system/OSSFile', data)

// 删除
export const deleteOss = (queryInfo) => uni.$u.http.delete(`/system/OSSFile/${queryInfo.id}`)

// 上传文件
export const uploadOss = (data) => uni.$u.http.post('/common/uploadOSS', data)

// 下载文件
export const downloadOss = (queryInfo) => uni.$u.http.get('/common/downloadOSS', {
  params: queryInfo,
  responseType: "blob",
  timeout: 60000
})
