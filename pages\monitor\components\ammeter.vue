<template>
  <view class="data-time">
    <view>{{ $t('上报时间') }}</view>
    <view>{{ ele[0]?.sdt }}({{ baseInfo.timeZone }})</view>
  </view>
  <view class="device-info">
    <u-collapse :value="eleValue">
      <u-collapse-item v-for="(eleItem, index) in ele" :key="eleItem.dc" :name="eleItem.dc">
        <template #title>
          <view class="info-ti">{{ eleItem.name }}</view>
        </template>
        <template #right-icon>
          <view><u-icon name="play-right-fill" size="12px"></u-icon></view>
        </template>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('设备类型') }}</view>
          <view v-if="eleItem['dc']">{{ getEleDcType(eleItem['dc']).text }}</view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('电表在线状态') }}</view>
          <view v-if="getStatus(index) == $t('离线')" style="color: #f56c6c;">{{ $t('离线') }}</view>
          <view v-else>{{ getStatus(index) }}</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('功率因数') }}</view>
          <view v-if="eleItem['em_10011']">{{ eleItem['em_10011'] }}</view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="getEleDcType(eleItem['dc']).type != '6'">
          <view class="color-grey">{{ $t('有功功率') }}</view>
          <view v-if="eleItem['em_10012']">{{ eleItem['em_10012'] }}<span class="item-unit">kW</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="getEleDcType(eleItem['dc']).type != '6'">
          <view class="color-grey">{{ $t('无功功率') }}</view>
          <view v-if="eleItem['em_10013']">{{ eleItem['em_10013'] }}<span class="item-unit">kVar</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="getEleDcType(eleItem['dc']).type != '6'">
          <view class="color-grey">{{ $t('视在功率') }}</view>
          <view v-if="eleItem['em_10014']">{{ eleItem['em_10014'] }}<span class="item-unit">kVA</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="getEleDcType(eleItem['dc']).type != '6'">
          <view class="color-grey">{{ $t('A相电压') }}</view>
          <view v-if="eleItem['em_10005']">{{ eleItem['em_10005'] }}<span class="item-unit">V</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="getEleDcType(eleItem['dc']).type != '6'">
          <view class="color-grey">{{ $t('B相电压') }}</view>
          <view v-if="eleItem['em_10006']">{{ eleItem['em_10006'] }}<span class="item-unit">V</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="getEleDcType(eleItem['dc']).type != '6'">
          <view class="color-grey">{{ $t('C相电压') }}</view>
          <view v-if="eleItem['em_10007']">{{ eleItem['em_10007'] }}<span class="item-unit">V</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="getEleDcType(eleItem['dc']).type != '6'">
          <view class="color-grey">{{ $t('A相电流') }}</view>
          <view v-if="eleItem['em_10008']">{{ eleItem['em_10008'] }}<span class="item-unit">A</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="getEleDcType(eleItem['dc']).type != '6'">
          <view class="color-grey">{{ $t('B相电流') }}</view>
          <view v-if="eleItem['em_10009']">{{ eleItem['em_10009'] }}<span class="item-unit">A</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="getEleDcType(eleItem['dc']).type != '6'">
          <view class="color-grey">{{ $t('C相电流') }}</view>
          <view v-if="eleItem['em_10010']">{{ eleItem['em_10010'] }}<span class="item-unit">A</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="getEleDcType(eleItem['dc']).type != '6'">
          <view class="color-grey">{{ $t('AB线电压') }}</view>
          <view v-if="eleItem['em_10041']">{{ eleItem['em_10041'] }}<span class="item-unit">V</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="getEleDcType(eleItem['dc']).type != '6'">
          <view class="color-grey">{{ $t('BC线电压') }}</view>
          <view v-if="eleItem['em_10042']">{{ eleItem['em_10042'] }}<span class="item-unit">V</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="getEleDcType(eleItem['dc']).type != '6'">
          <view class="color-grey">{{ $t('CA线电压') }}</view>
          <view v-if="eleItem['em_10043']">{{ eleItem['em_10043'] }}<span class="item-unit">V</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="getEleDcType(eleItem['dc']).type == '1'">
          <view class="color-grey">{{ $t('正向有功电度') }}</view>
          <view v-if="eleItem['em_10015']">{{ eleItem['em_10015'] }}<span class="item-unit">kWh</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="getEleDcType(eleItem['dc']).type == '1'">
          <view class="color-grey">{{ $t('反向有功电度') }}</view>
          <view v-if="eleItem['em_10016']">{{ eleItem['em_10016'] }}<span class="item-unit">kWh</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="getEleDcType(eleItem['dc']).type == '1'">
          <view class="color-grey">{{ $t('正向无功电度') }}</view>
          <view v-if="eleItem['em_10017']">{{ eleItem['em_10017'] }}<span class="item-unit">kVarh</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="getEleDcType(eleItem['dc']).type == '1'">
          <view class="color-grey">{{ $t('反向无功电度') }}</view>
          <view v-if="eleItem['em_10018']">{{ eleItem['em_10018'] }}<span class="item-unit">kVarh</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="getEleDcType(eleItem['dc']).type == '1'">
          <view class="color-grey">{{ $t('尖正向有功电度') }}</view>
          <view v-if="eleItem['em_10031']">{{ eleItem['em_10031'] }}<span class="item-unit">kWh</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="getEleDcType(eleItem['dc']).type == '1'">
          <view class="color-grey">{{ $t('尖反向有功电度') }}</view>
          <view v-if="eleItem['em_10032']">{{ eleItem['em_10032'] }}<span class="item-unit">kWh</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="getEleDcType(eleItem['dc']).type == '1'">
          <view class="color-grey">{{ $t('尖正向无功电度') }}</view>
          <view v-if="eleItem['em_10033']">{{ eleItem['em_10033'] }}<span class="item-unit">kVarh</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="getEleDcType(eleItem['dc']).type == '1'">
          <view class="color-grey">{{ $t('尖反向无功电度') }}</view>
          <view v-if="eleItem['em_10034']">{{ eleItem['em_10034'] }}<span class="item-unit">kVarh</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="getEleDcType(eleItem['dc']).type == '1'">
          <view class="color-grey">{{ $t('峰正向有功电度') }}</view>
          <view v-if="eleItem['em_10019']">{{ eleItem['em_10019'] }}<span class="item-unit">kWh</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="getEleDcType(eleItem['dc']).type == '1'">
          <view class="color-grey">{{ $t('峰反向有功电度') }}</view>
          <view v-if="eleItem['em_10020']">{{ eleItem['em_10020'] }}<span class="item-unit">kWh</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="getEleDcType(eleItem['dc']).type == '1'">
          <view class="color-grey">{{ $t('峰正向无功电度') }}</view>
          <view v-if="eleItem['em_10021']">{{ eleItem['em_10021'] }}<span class="item-unit">kVarh</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="getEleDcType(eleItem['dc']).type == '1'">
          <view class="color-grey">{{ $t('峰反向无功电度') }}</view>
          <view v-if="eleItem['em_10022']">{{ eleItem['em_10022'] }}<span class="item-unit">kVarh</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="getEleDcType(eleItem['dc']).type == '1'">
          <view class="color-grey">{{ $t('平正向有功电度') }}</view>
          <view v-if="eleItem['em_10027']">{{ eleItem['em_10027'] }}<span class="item-unit">kWh</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="getEleDcType(eleItem['dc']).type == '1'">
          <view class="color-grey">{{ $t('平反向有功电度') }}</view>
          <view v-if="eleItem['em_10028']">{{ eleItem['em_10028'] }}<span class="item-unit">kWh</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="getEleDcType(eleItem['dc']).type == '1'">
          <view class="color-grey">{{ $t('平正向无功电度') }}</view>
          <view v-if="eleItem['em_10029']">{{ eleItem['em_10029'] }}<span class="item-unit">kVarh</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="getEleDcType(eleItem['dc']).type == '1'">
          <view class="color-grey">{{ $t('平反向无功电度') }}</view>
          <view v-if="eleItem['em_10030']">{{ eleItem['em_10030'] }}<span class="item-unit">kVarh</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="getEleDcType(eleItem['dc']).type == '1'">
          <view class="color-grey">{{ $t('谷正向有功电度') }}</view>
          <view v-if="eleItem['em_10023']">{{ eleItem['em_10023'] }}<span class="item-unit">kWh</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="getEleDcType(eleItem['dc']).type == '1'">
          <view class="color-grey">{{ $t('谷反向有功电度') }}</view>
          <view v-if="eleItem['em_10024']">{{ eleItem['em_10024'] }}<span class="item-unit">kWh</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="getEleDcType(eleItem['dc']).type == '1'">
          <view class="color-grey">{{ $t('谷正向无功电度') }}</view>
          <view v-if="eleItem['em_10025']">{{ eleItem['em_10025'] }}<span class="item-unit">kVarh</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="getEleDcType(eleItem['dc']).type == '1'">
          <view class="color-grey">{{ $t('谷反向无功电度') }}</view>
          <view v-if="eleItem['em_10026']">{{ eleItem['em_10026'] }}<span class="item-unit">kVarh</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="getEleDcType(eleItem['dc']).type == '6'">
          <view class="color-grey">{{ $t('直流电压') }}</view>
          <view v-if="eleItem['em_10038']">{{ eleItem['em_10038'] }}<span class="item-unit">V</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="getEleDcType(eleItem['dc']).type == '6'">
          <view class="color-grey">{{ $t('直流电流') }}</view>
          <view v-if="eleItem['em_10039']">{{ eleItem['em_10039'] }}<span class="item-unit">A</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="getEleDcType(eleItem['dc']).type == '6'">
          <view class="color-grey">{{ $t('直流功率') }}</view>
          <view v-if="eleItem['em_10040']">{{ eleItem['em_10040'] }}<span class="item-unit">kW</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="getEleDcType(eleItem['dc']).type != '6'">
          <view class="color-grey">{{ $t('频率') }}</view>
          <view v-if="eleItem['em_10035']">{{ eleItem['em_10035'] }}<span class="item-unit">Hz</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('正向总电量') }}</view>
          <view v-if="eleItem['em_10036']">{{ eleItem['em_10036'] }}<span class="item-unit">kWh</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('反向总电量') }}</view>
          <view v-if="eleItem['em_10037']">{{ eleItem['em_10037'] }}<span class="item-unit">kWh</span></view>
          <view v-else>--</view>
        </view>
      </u-collapse-item>
    </u-collapse>
  </view>
</template>

<script setup>
  import {
    ref,
    computed,
    getCurrentInstance
  } from 'vue'
  import {
    useMonitorStore
  } from '@/store/monitor.js'
  import {
    useLoginStore
  } from '@/store/login.js'
  import {
    get4058
  } from '@/common/parseBinaryToText.js'
  import otherColor from '../../../common/other.module.scss'
  import {
    isGroupFn
  } from '@/hook/useDeviceType.js'
  import {
    onShow
  } from '@dcloudio/uni-app'
  import {
    emTypeOptions
  } from '@/constant/index.js'

  const {
    proxy
  } = getCurrentInstance()
  const monitorStore = useMonitorStore()
  const userStore = useLoginStore()
  const baseInfo = computed(() => monitorStore.baseInfo)

  const isGroup = computed(() => isGroupFn(monitorStore.routeQuery.type))

  const aliasArr = ref([])
  const getAliasArrFn = () => {
    aliasArr.value = []
    let groupId = monitorStore.routeQuery.groupId
    if (isGroup.value) {
      groupId.forEach(async (item) => {
        const data = await monitorStore.bindAliasQueryFn({
          ac: item,
          enable: 1,
          type: 1
        })
        aliasArr.value = [...aliasArr.value, ...data]
      })
    } else {
      monitorStore.bindAliasQueryFn({
        ac: monitorStore.routeQuery.id,
        enable: 1,
        type: 1
      }).then(data => {
        aliasArr.value = [...aliasArr.value, ...data]
      })
    }
  }
  getAliasArrFn()

  // 设备类型
  const getEleType = computed(() => {
    return (type) => {
      if (type == '1') {
        return uni.$t('计量点电表')
      } else if (type == '2') {
        return uni.$t('储能电表')
      } else if (type == '3') {
        return uni.$t('PCC电表')
      } else if (type == '4') {
        return uni.$t('光伏电表')
      } else if (type == '5') {
        return uni.$t('负载电表')
      } else if (type == '6') {
        return uni.$t('直流电表')
      } else if (type == '7') {
        return uni.$t('市电电表')
      }
    }
  })
  const getEleDcType = computed(() => {
    return (dc) => {
      let dcNum = Number(dc)
      let value = emTypeOptions.find(range => dcNum >= range.min && dcNum <= range.max)
      if (value) {
        return {
          text: uni.$t(value.textKey),
          type: value.type
        }
      } else {
        return {
          text: uni.$t('未知电表'),
          type: '0'
        }
      }
    }
  })

  const eleValue = ref([])
  const ele = computed(() => {
    let data = monitorStore.pcs_ele
    eleValue.value = data.map(item => item.dc)
    data.forEach(item => {
      let value = aliasArr.value.find(alias => alias.point == item.dc)
      if (value) {
        item.name = isGroup.value ? `${item.label}_${value.alias}` : value.alias
      } else {
        item.name = isGroup.value ? `${item.label}_${getEleDcType.value(item.dc).text}_${item.dc}` :
          `${getEleDcType.value(item.dc).text}_${item.dc}`
      }
    })
    return data
  })

  const getStatus = computed(() => {
    return (index) => {
      if (ele.value[index].isAnalysis == 0) {
        return ele.value.onLineState == '在线' ? uni.$i18n().t('在线') : uni.$i18n().t('离线')
      } else if (ele.value[index].isAnalysis == 1) {
        return ele.value[index]['em_10003'] == '1' ? uni.$i18n().t('告警') : uni.$i18n().t('正常')
      } else {
        return '--'
      }
    }
  })
</script>

<style scoped lang="scss">
  .data-time {
    display: flex;
    justify-content: space-between;
    background-color: #fff;
    padding: 20rpx 30rpx;
    margin-bottom: 20rpx;
    margin: 20rpx 30rpx;
    font-size: 10px;
    border-radius: 6px;
  }

  .device-info {
    /* background-color: $uni-bg-color; */
    background-color: #fff;
    border-radius: $uni-border-radius-lg;
    padding: 0 30rpx;
    margin: 20rpx 30rpx;

    .info-ti {
      font-weight: bold;
    }

    .info-item {
      padding: 30rpx 0;
      border-bottom: 1px solid $uni-bg-color-grey;

      .item-unit {
        font-size: 12px;
        color: $uni-text-color-grey;
        margin-left: 4rpx;
      }

      view:nth-child(2n) {
        width: 50%;
        text-align: right;
      }
    }

    .info-item:last-child {
      border-bottom: none;
    }
  }

  :deep(.u-collapse-item__content__text) {
    padding: 0;
    color: #000;
  }

  :deep(.u-cell__body) {
    padding: 30rpx 0;
  }

  :deep(.u-cell--clickable) {
    background-color: $uni-bg-color;
  }
</style>