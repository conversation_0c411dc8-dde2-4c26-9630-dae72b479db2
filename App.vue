<script>
  import {
    init as initLang
  } from './locale/index' // 国际化
  import { initService } from '@/common/request'
  import {
    useLoginStore
  } from '@/store/login.js'
  import otherColor from './common/other.module.scss'
  
  export default {
    onLaunch: function() {
      uni.setTabBarStyle({
        color: otherColor.mainColor,
        selectedColor: otherColor.primaryColor
      })
      initLang() // 初始化语言，应用无缓存按照系统语言，有缓存按照缓存语言
      initService() // 初始化请求
      /**
       * 判断是否有token，无token跳转登录
       */
      const isLogin = () => {
        const token = uni.cache.getItem('token')
        if (!token) {
          uni.reLaunch({
            url: '/pages/common/login'
          })
        } else {
          uni.switchTab({
          	url: '/pages/index/index'
          })
        }
      }
      isLogin()
      
      console.log('App Launch')
    },
    onShow: function() {
      console.log('App Show')
    },
    onHide: function() {
      console.log('App Hide')
    }
  }
</script>

<style lang="scss">
  /*每个页面公共css */
  /* 注意要写在第一行，同时给style标签加入lang="scss"属性 */
  @import "@/uni_modules/uview-plus/index.scss";
  @import "./common/index.scss";
</style>