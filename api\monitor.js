/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-07-01 18:42:32
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-06-11 15:05:34
 * @FilePath: \elecloud_mobile_copy\api\monitor.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// 查询设备监控列表
export const deviceMonitoringList = (queryInfo) => uni.$u.http.get('/system/deviceMonitoring/list', {
  params: queryInfo,
  custom: {
    loading: false
  }
})

// 获取设备在线离线数量
export const getOnLineOrOffLineVo = () => uni.$u.http.get('/system/deviceMonitoring/getOnLineOrOffLineVo', {
  custom: {
    loading: false
  }
})

// 获取设备详细信息(上)
export const deviceMonitoringDetailTop = (queryInfo) => uni.$u.http.get(
  `/system/deviceMonitoring/top/${queryInfo.deviceSerialNumber}/${queryInfo.deviceType}`, {
  custom: {
    loading: false
  }
})

// 获取设备详细信息（右）
export const deviceMonitoringDetailRight = (queryInfo) => uni.$u.http.get(
  `/system/deviceMonitoring/right/${queryInfo.deviceSerialNumber}/${queryInfo.type}/${queryInfo.deviceType}`, {
  custom: {
    loading: false
  }
})

// 功率分析统计
export const powerAnalysisStatistics = (queryInfo) => uni.$u.http.post(
  `/system/deviceMonitoring/powerAnalysisStatistics`, queryInfo, {
  custom: {
    loading: false
  }
})

// 功率分析统计 - 组合类型
export const powerAnalysisStatisticsGroup = (queryInfo) => uni.$u.http.post(
  `/system/deviceMonitoring/GroupPowerAnalysisStatistics`, queryInfo, {
  custom: {
    loading: false
  }
})

// 数据概括-动态图
export const selectDynamicGraph = (queryInfo) => uni.$u.http.get(
  `/system/deviceMonitoring/selectDynamicGraph/${queryInfo.deviceSerialNumber}`, {
  custom: {
    loading: false
  }
})

// 数据概括-动态图 - 组合类型
export const selectDynamicGraphGroup = (queryInfo) => uni.$u.http.get(
  `/system/deviceMonitoring/selectGroupDynamicGraph/${queryInfo.deviceSerialNumber}`, {
  custom: {
    loading: false
  }
})

// 获取用电量、放电量统计
export const electricStatistics = (data) => uni.$u.http.post('/system/deviceMonitoring/electricStatistics', data, {
  custom: {
    loading: false
  }
})


// 导出电量
export const exportDeviceMonitoring = (data) => uni.$u.http.post('/system/deviceMonitoring/export', data, {
  responseType: "blob"
})

// 恢复出厂设备
export const recover = (queryInfo) => uni.$u.http.get(
  `/system/deviceMonitoring/clearData/${queryInfo.deviceSerialNumber}/${queryInfo.timeZone}/${queryInfo.deviceType}`)

/**
 * 远程控制 VNC
 */
// 获取所有端口号
export const listAll = (queryInfo) => uni.$u.http.get('/system/VNCPort/listAll', queryInfo)

// 获取id
export const vncParameterSettingByAc = (queryInfo) => uni.$u.http.get(`/system/vncParameterSetting/getInfoByAc/${queryInfo.ac}`)

// 开启远程
export const argumentsJsonFrpOpen = (data) => uni.$u.http.post('/system/sendMqtt/argumentsJsonFrpOpen', data, {
  timeout: 120000
})

// 关闭远程
export const argumentsJsonFrpClose = (data) => uni.$u.http.post('/system/sendMqtt/argumentsJsonFrpClose', data)