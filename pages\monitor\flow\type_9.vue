<template>
  <view class="flow-item u-flex u-flex-center align-items bold">
    <view class="item-le">
      <view class="le-gird flex align-items">
        <image src="../../../static/flow_ac.png" mode="" v-if="isShowDiesel"></image>
        <template v-else>
          <view style="width: 80rpx;height: 100rpx;" class="flex u-flex-column-reverse" v-if="isShowDieselValue">
            <image src="../../../static/flow_diesel.png" style="width: 90rpx;height: 70rpx;"></image>
          </view>
          <image src="../../../static/flow_ac.png" mode="" v-else></image>
        </template>
        <view class="ml-5">{{ flowData.power }} kW</view>
      </view>
      <view class="le-gird-line mb-20" :style="[lineStyle('power')]">
        <template v-if="showCircle('power')">
          <u-icon name="arrow-down-fill" size="20rpx" color="#fb560a" class="rightToGird"
            v-if="flowData.power < -1"></u-icon>
          <u-icon name="play-left-fill" size="20rpx" color="#fb560a" class="leftToGird"
            v-else-if="flowData.power > 1"></u-icon>
        </template>
      </view>
      <view class="le-load-line" :style="[lineStyle('load')]">
        <template v-if="showCircle('load')">
          <u-icon name="arrow-up-fill" size="20rpx" color="#fb560a" class="rightToLoad"
            v-if="flowData.load < -1"></u-icon>
          <u-icon name="play-left-fill" size="20rpx" color="#fb560a" class="leftToLoad"
            v-else-if="flowData.load > 1"></u-icon>
        </template>
      </view>
      <view class="le-load flex align-items">
        <image src="../../../static/flow_load.png" mode=""></image>
        <view class="ml-5">{{ flowData.load }} kW</view>
      </view>
    </view>
    <view class="item-ce">
      <image src="../../../static/flow_hmi.png"></image>
    </view>
    <view class="item-ri">
      <view class="ri-cell flex u-flex-column align-items">
        <view style="height: 80rpx;width: 80rpx;display: flex;justify-content: center;align-items: center;">
          <zu-battery :battery="soc" width="50rpx" height="50rpx" fontSize="8.5px"
            style="margin-top: 10rpx;"></zu-battery>
        </view>
        <view class="u-m-r-10" v-if="isShowV54154">
          <span v-if="flowData.groupData">{{ flowData.groupData[groupId[0]].cell }}</span> kW
        </view>
        <view class="u-m-r-10" v-else>
          <span>{{ flowData.cell }}</span> kW
        </view>
      </view>
      <view class="ri-cell-line" :style="[lineStyle('cell')]">
        <template v-if="showCircle('cell')">
          <u-icon name="play-right-fill" size="20rpx" color="#fb560a" class="rightToCell"
            v-if="flowData.cell < -1"></u-icon>
          <u-icon name="play-left-fill" size="20rpx" color="#fb560a" class="leftToCell"
            v-else-if="flowData.cell > 1"></u-icon>
        </template>
      </view>
    </view>
  </view>
</template>

<script setup>
  import {
    ref,
    computed
  } from 'vue'
  import {
    useMonitorStore
  } from '@/store/monitor.js'
  import {
    isGroupFn
  } from '@/hook/useDeviceType.js'
  import {
    isShow3502Bit10
  } from '@/common/parseBinaryToText.js'
  import {
    emTypeOptions
  } from '@/constant/index.js'

  const monitorStore = useMonitorStore()
  
  const isGroup = computed(() => isGroupFn(monitorStore.routeQuery.type))
  const flowData = computed(() => {
    let data = null
    if (isGroup.value) {
      data = monitorStore.groupFlowData
      if (isDeviceMasterTypeRT07.value) data.load = (data.cell - data.em_10012s).toFixed(2)
    } else {
      data = monitorStore.flowData
      if (isEmTypePCC.value) data.load = (data.cell - data.em_10012s).toFixed(
        2) // 储能系统(纯并网)、PCC电表，电池功率-电表有功功率,取绝对值
    }
    return data
  })
  const lineStyle = computed(() => (type) => {
    let color = showCircle.value(type) ? '#fb560a' : '#eeeeef'
    let borderColor = ''
    switch (type) {
      case 'power':
        borderColor = `transparent transparent ${color} ${color}`
        break;
      case 'load':
        borderColor = `${color} transparent transparent ${color}`
        break;
    }
    return {
      borderColor,
      backgroundColor: type == 'cell' ? color : ''
    }
  })
  // 流动拓补图展示圆
  const showCircle = computed(() => {
    let control = isGroup.value ? monitorStore.groupControl[0] : monitorStore.control
    return (name) => {
      if (!control) return false
      if (control.onLineState == '离线') return false
      if (-1 < flowData.value[name] && flowData.value[name] < 1) {
        return false
      } else if (flowData.value[name] == 0) {
        return false
      } else {
        return true
      }
    }
  })

  // 是否显示发电机
  const isShowDieselValue = computed(() => {
    let sts = monitorStore.pcs_sts
    if (sts.length) return isShow3502Bit10(sts[0]['sts_3502'])
    return false
  })
  const isShowDiesel = computed(() => {
    let baseInfo = monitorStore.baseInfo
    return baseInfo.showDiesel
  })

  const soc = computed(() => {
    let bms = monitorStore.pcs_bms
    let bmsBau = monitorStore.pcs_bmsBau
    if (bms.length) return (bms.reduce((pre, item) => pre += Number(item.bms_4022), 0) / bms.length).toFixed(1)
    else if (bmsBau.length) return (bmsBau.reduce((pre, item) => pre += Number(item.bms_4022), 0) / bmsBau.length)
      .toFixed(1)
    return 0
  })

  if (isGroup.value) {
    monitorStore.selectDynamicGraphGroupFn({
      deviceSerialNumber: monitorStore.routeQuery.id,
      deviceType: monitorStore.routeQuery.type,
      groupId: monitorStore.routeQuery.groupId,
      groupType: monitorStore.routeQuery.groupType
    })
  } else {
    monitorStore.selectDynamicGraphFn({
      deviceSerialNumber: monitorStore.routeQuery.id,
      deviceType: monitorStore.routeQuery.type
    })
  }

  const groupId = computed(() => monitorStore.routeQuery.groupId)
  const isShowV54154 = computed(() => {
    let control = isGroup.value ? monitorStore.groupControl[0] : monitorStore.control
    let versionStart = control?.jk_1000?.split('V')[1].split('.')[0]
    let versionTwo = control?.jk_1000?.split('V')[1].split('.')[1]
    let versionThere = control?.jk_1000?.split('V')[1].split('.')[2]
    if (versionStart == 5)
      if (versionTwo == 4154) return true
    return false
  })
  
  const isEmTypePCC = computed(() => {
    let data = monitorStore.pcs_ele
    return monitorStore.routeQuery.type == 7 && data.some(item => {
      return emTypeOptions.findIndex(range => item.dc >= range.min && item.dc <= range.max) !== -1
    })
  })
  // 组合设备类型，主机为RT07储能系统(纯并网)、PCC电表，电池功率-电表有功功率,取绝对值
  const isDeviceMasterTypeRT07 = computed(() => {
    let groupType = monitorStore.routeQuery.groupType
    let data = monitorStore.pcs_ele
    let isEmTypePCC = data.some(item => {
      return emTypeOptions.findIndex(range => item.dc >= range.min && item.dc <= range.max) !== -1
    })
    if (groupType[0] == 7 && isEmTypePCC) return true
    return false
  })
</script>

<style scoped lang="scss">
  image {
    width: 100%;
    height: 100%;
  }

  .flow-item {
    position: relative;
    height: 400rpx;
    font-size: 10px;

    .item-ce {
      width: 70rpx;
      height: 50rpx;
      z-index: 100;
      margin-top: 12rpx;
    }

    .item-le {
      position: relative;
      right: -18rpx;

      .le-gird {
        position: relative;
        left: -30rpx;
        top: 14rpx;
        z-index: 100;

        image {
          width: 80rpx;
          height: 100rpx;
        }
      }

      .le-gird-line {
        height: 80rpx;
        width: 250rpx;
        border: 4rpx solid;
        border-color: transparent transparent #fb560a #fb560a;
        border-radius: 0 20rpx;
        position: relative;

        .rightToGird {
          position: absolute;
          animation: rightToGird 3s linear infinite;
        }

        .leftToGird {
          position: absolute;
          animation: leftToGird 3s linear infinite;
        }

        @keyframes rightToGird {
          0% {
            top: -14rpx;
            left: -12rpx;
            transform: rotate(0);
          }

          47% {
            top: calc(100% - 40rpx);
            left: -12rpx;
            transform: rotate(0);
          }

          50% {
            top: calc(100% - 12rpx);
            left: -12rpx;
            transform: rotate(-45deg);
          }

          53% {
            top: calc(100% - 8rpx);
            left: 8rpx;
            transform: rotate(-90deg);
          }

          100% {
            top: calc(100% - 8rpx);
            left: calc(100% - 12rpx);
            transform: rotate(-90deg);
          }
        }

        @keyframes leftToGird {
          100% {
            top: -14rpx;
            left: -13rpx;
            transform: rotate(90deg);
          }

          53% {
            top: calc(100% - 48rpx);
            left: -13rpx;
            transform: rotate(90deg);
          }

          50% {
            top: calc(100% - 40rpx);
            left: -10rpx;
            transform: rotate(45deg);
          }

          47% {
            top: calc(100% - 7rpx);
            left: -10rpx;
            transform: rotate(0);
          }

          0% {
            top: calc(100% - 7rpx);
            left: calc(100% - 8rpx);
            transform: rotate(0);
          }
        }
      }

      .le-load {
        position: relative;
        left: -44rpx;
        bottom: 14rpx;

        image {
          width: 90rpx;
          height: 80rpx;
        }
      }

      .le-load-line {
        position: relative;
        height: 90rpx;
        width: 250rpx;
        border: 2px solid;
        border-color: #fb560a transparent transparent #fb560a;
        border-radius: 20rpx 0;

        .rightToLoad {
          position: absolute;
          animation: rightToLoad 3s linear infinite;
        }

        .leftToLoad {
          position: absolute;
          animation: leftToLoad 3s linear infinite;
        }

        @keyframes rightToLoad {
          100% {
            top: -12rpx;
            left: calc(100% - 10rpx);
            transform: rotate(90deg);
          }

          53% {
            top: -12rpx;
            left: 0;
            transform: rotate(90deg);
          }

          50% {
            top: 0;
            left: -10rpx;
            transform: rotate(-45deg);
          }

          47% {
            top: 14rpx;
            left: -10rpx;
            transform: rotate(0);
          }

          0% {
            top: calc(100% - 14rpx);
            left: -10rpx;
            transform: rotate(0);
          }
        }

        @keyframes leftToLoad {
          100% {
            top: calc(100% - 8rpx);
            left: -10rpx;
            transform: rotate(-90deg);
          }

          53% {
            top: 8rpx;
            left: -10rpx;
            transform: rotate(-90deg);
          }

          50% {
            top: 0;
            left: -8rpx;
            transform: rotate(-45deg);
          }

          47% {
            top: -11rpx;
            left: 8rpx;
            transform: rotate(0);
          }

          0% {
            top: -11rpx;
            left: calc(100% - 8rpx);
            transform: rotate(0);
          }
        }
      }
    }

    .item-ri {
      position: relative;
      left: -18rpx;
      top: -58rpx;
      display: flex;
      flex-direction: column;
      align-items: flex-end;

      .ri-cell {
        margin-top: 20rpx;
        z-index: 0;
        position: relative;
        right: -70rpx;
        top: 64rpx;
        z-index: 100;

        &-color {
          color: $u-primary;
        }

        image {
          width: 80rpx;
          height: 80rpx;
        }
      }

      .ri-cell-line {
        position: relative;
        height: 4rpx;
        width: 230rpx;
        background-color: #fb560a;

        .rightToCell {
          position: absolute;
          animation: rightToCell 3s linear infinite;
        }

        .leftToCell {
          position: absolute;
          animation: leftToCell 3s linear infinite;
        }

        @keyframes rightToCell {
          0% {
            top: -7rpx;
            left: -12rpx;
          }

          100% {
            top: -7rpx;
            left: calc(100% - 14rpx);
          }
        }

        @keyframes leftToCell {
          0% {
            top: -7rpx;
            left: calc(100% - 14rpx);
          }

          100% {
            top: -7rpx;
            left: -12rpx;
          }
        }
      }
    }
  }
</style>