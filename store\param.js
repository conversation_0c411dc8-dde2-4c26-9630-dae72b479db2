import {
  defineStore
} from 'pinia'
import {
  ref
} from 'vue'
import {
  macInfo,
  addMAC,
  sendParamMAC,
  editMAC,
  mdcInfo,
  addMDC,
  sendParamMDC,
  editMDC,
  bmsInfo,
  addBMS,
  sendParamBMS,
  editBMS,
  systemInfo,
  addSystem,
  sendParamSystem,
  editSystem,
  sendParamUpdate,
  onOffInfo,
  addOnOff,
  sendParamOnOff,
  editOnOff,
  getJsonData
} from '@/api/param.js'
import {
  allJfpg
} from '@/api/jfpg.js'
import {
  allPrice
} from '@/api/price.js'
import {
  backupAll
} from '@/api/backUp.js'
import {
  ossList
} from '@/api/update.js'

export const useParamStore = defineStore('param', () => {
  /**
   *
   * MAC
   */
  const macInfoData = ref({})
  const macInfoFn = async (queryInfo) => {
    const res = await macInfo(queryInfo)
    macInfoData.value = res.data
    return res.data
  }
  const addMACFn = async (data) => {
    const res = await addMAC(data)
  }
  const editMACFn = async (data) => {
    const res = await editMAC(data)
  }
  // 参数下发
  const sendParamMACFn = async (data) => {
    const res = await sendParamMAC(data)
    if (res.code !== 200) throw res.msg
  }
  /**
   *
   * 获取削峰填谷及分时电价下拉框数据
   */
  const jfpgOptions = ref([])
  const allJfpgFn = async (queryInfo) => {
    const res = await allJfpg(queryInfo)
    jfpgOptions.value = res.data
  }
  const priceOptions = ref([])
  const allPriceFn = async (queryInfo) => {
    const res = await allPrice(queryInfo)
    priceOptions.value = res.data
  }
  const backupOptions = ref([])
  const allBackUpFn = async (queryInfo) => {
    const res = await backupAll(queryInfo)
    backupOptions.value = res.data
  }
  /**
   *
   * MDC
   */
  const mdcInfoData = ref({})
  const mdcInfoFn = async (queryInfo) => {
    const res = await mdcInfo(queryInfo)
    mdcInfoData.value = res.data
    return res.data
  }
  const addMDCFn = async (data) => {
    await addMDC(data)
  }
  const editMDCFn = async (data) => {
    await editMDC(data)
  }
  // 参数下发
  const sendParamMDCFn = async (data) => {
    const res = await sendParamMDC(data)
    if (res.code !== 200) throw res.msg
  }
  /**
   *
   * BMS
   */
  const bmsInfoData = ref({})
  const bmsInfoFn = async (queryInfo) => {
    const res = await bmsInfo(queryInfo)
    bmsInfoData.value = res.data
    return res.data
  }
  const addBMSFn = async (data) => {
    await addBMS(data)
  }
  const editBMSFn = async (data) => {
    await editBMS(data)
  }
  // 参数下发
  const sendParamBMSFn = async (data) => {
    const res = await sendParamBMS(data)
    if (res.code !== 200) throw res.msg
  }
  /**
   *
   * 策略
   */
  const systemInfoData = ref({})
  const systemInfoFn = async (queryInfo) => {
    const res = await systemInfo(queryInfo)
    systemInfoData.value = res.data
    return res.data
  }
  const addSystemFn = async (data) => {
    await addSystem(data)
  }
  const editSystemFn = async (data) => {
    await editSystem(data)
  }
  // 参数下发
  const sendParamSystemFn = async (data) => {
    const res = await sendParamSystem(data)
    if (res.code !== 200) throw res.msg
  }
  /**
   * 升级
   */
  // 参数下发
  const sendParamUpdateFn = async (data) => {
    const res = await sendParamUpdate(data)
    if (res.code !== 200) throw res.msg
  }
  const ossOptions = ref([])
  const allOssListFn = async () => {
    const res = await ossList({
      pageNum: 1,
      pageSize: 100
    })
    ossOptions.value = res.rows
  }
  /**
   *
   * 系统开关机
   */
  const onOffInfoData = ref({})
  const onOffInfoFn = async (queryInfo) => {
    const res = await onOffInfo(queryInfo)
    onOffInfoData.value = res.data
    return res.data
  }
  const addOnOffFn = async (data) => {
    await addOnOff(data)
  }
  const editOnOffFn = async (data) => {
    await editOnOff(data)
  }
  // 参数下发
  const sendParamOnOffFn = async (data) => {
    const res = await sendParamOnOff(data)
    if (res.code !== 200) throw res.msg
  }
  /**
   * 获取设备参数，无返回结果
   */
  const getJsonDataFn = async (data) => {
    const res = await getJsonData(data)
    if (res.code !== 200) throw res.msg
  }

  return {
    // MAC
    macInfoData,
    macInfoFn,
    addMACFn,
    editMACFn,
    sendParamMACFn,
    // MDC,
    mdcInfoData,
    mdcInfoFn,
    addMDCFn,
    editMDCFn,
    sendParamMDCFn,
    // BMS
    bmsInfoData,
    bmsInfoFn,
    addBMSFn,
    editBMSFn,
    sendParamBMSFn,
    // 升级
    sendParamUpdateFn,
    // 策略
    systemInfoData,
    systemInfoFn,
    addSystemFn,
    editSystemFn,
    sendParamSystemFn,
    // 开关机
    onOffInfoFn,
    onOffInfoData,
    addOnOffFn,
    editOnOffFn,
    sendParamOnOffFn,
    // 列表
    jfpgOptions,
    allJfpgFn,
    priceOptions,
    allPriceFn,
    backupOptions,
    allBackUpFn,
    ossOptions,
    // 拉取参数
    getJsonDataFn
  }
})