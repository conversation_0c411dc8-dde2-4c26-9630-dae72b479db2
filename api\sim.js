// 获取sim列表
export const simList = (queryInfo) => uni.$u.http.get('/system/sim/list', {
  params: queryInfo
})

// 新增sim
export const addSim = (data) => uni.$u.http.post('/system/sim', data)

// sim充值
export const rechargeSim = (queryInfo) => uni.$u.http.get('/system/sim/recharge', {
  params: queryInfo
})

// 获取详情
export const getSimInfo = (queryInfo) => uni.$u.http.get('/system/sim/detail', {
  params: queryInfo
})

// 分配sim
export const allotSIM = (data) => uni.$u.http.post('/system/sim/allotSim', data)

// 删除sim
export const deleteSim = (queryInfo) => uni.$u.http.delete(`/system/sim/${queryInfo.id}`)
