import App from './App'
import uviewPlus from '@/uni_modules/uview-plus' // 组件库
import { initRequest } from './common/request' // 请求
import { cache } from './common/storage.js' // 缓存
import messages from './locale/index' // 国际化

uni.cache = cache

let i18nConfig = {
  locale: uni.getLocale(),
  messages
}

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
import VueI18n from 'vue-i18n'
Vue.use(VueI18n)
const i18n = new VueI18n(i18nConfig)
Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
  ...App,
  i18n
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
import * as Pinia from 'pinia';
import { createI18n, useI18n } from 'vue-i18n'

const i18n = createI18n(i18nConfig)

export function createApp() {
  const app = createSSRApp(App)
	app.use(uviewPlus)
	uni.$u.setConfig({
		// 修改$u.config对象的属性
		config: {
			// 修改默认单位为rpx，相当于执行 uni.$u.config.unit = 'rpx'
			unit: 'rpx'
		},
	})
	// 引入请求封装
	initRequest(app)
	app.use(Pinia.createPinia());
  app.use(i18n)
  uni.$i18n = useI18n
  uni.$t = i18n.global.t
  
  return {
    app,
		Pinia, // 此处必须将 Pinia 返回
  }
}
// #endif