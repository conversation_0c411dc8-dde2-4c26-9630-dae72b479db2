<template>
  <view class="data-time">
    <view>{{ $t('上报时间') }}</view>
    <view>{{ io[0]?.sdt }}({{ baseInfo.timeZone }})</view>
  </view>
  <view class="device-info">
    <u-collapse :value="ioValue">
      <u-collapse-item v-for="(ioItem, index) in io" :key="ioItem.dc" :name="ioItem.dc">
        <template #title>
          <view class="info-ti">{{ ioItem.name }}</view>
        </template>
        <template #right-icon>
          <view><u-icon name="play-right-fill" size="12px"></u-icon></view>
        </template>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('通讯状态') }}</view>
          <view v-if="getStatus(index) == $t('离线')" style="color: #f56c6c;">{{ $t('离线') }}</view>
          <view v-else>{{ getStatus(index) }}</view>
        </view>
        <block v-for="i in 10">
          <view class="info-item flex justify-content align-items"
            v-if="ioItem[`180${i < 10 ? '0' + i : i}_alias`] != $t('未知别名')" :key="i">
            <view class="color-grey">{{ ioItem[`180${i < 10 ? '0' + i : i}_alias`] }}</view>
            <view v-if="ioItem[`peripherals_180${i < 10 ? '0' + i : i}`]">
              {{ ioItem[`peripherals_180${i < 10 ? '0' + i : i}`] == 1 ? $t('闭合'): $t('断开') }}</view>
            <view v-else>--</view>
          </view>
        </block>
      </u-collapse-item>
    </u-collapse>
  </view>
</template>

<script setup>
  import {
    ref,
    computed,
    getCurrentInstance
  } from 'vue'
  import {
    useMonitorStore
  } from '@/store/monitor.js'
  import {
    useLoginStore
  } from '@/store/login.js'
  import {
    get4058
  } from '@/common/parseBinaryToText.js'
  import otherColor from '../../../common/other.module.scss'
  import { isGroupFn } from '@/hook/useDeviceType.js'
  import { onShow } from '@dcloudio/uni-app'

  const {
    proxy
  } = getCurrentInstance()
  const monitorStore = useMonitorStore()
  const userStore = useLoginStore()
  const baseInfo = computed(() => monitorStore.baseInfo)
  
  const isGroup = computed(() => isGroupFn(monitorStore.routeQuery.type))

  const aliasArr = ref([])
  const getAliasArrFn = () => {
    aliasArr.value = []
    let groupId = monitorStore.routeQuery.groupId
    if (isGroup.value) {
      groupId.forEach(async (item) => {
        const data = await monitorStore.bindAliasQueryFn({
          ac: item,
          enable: 1,
          type: 0,
          mode: 0
        })
        aliasArr.value = [...aliasArr.value, ...data]
      })
    } else {
      monitorStore.bindAliasQueryFn({
        ac: monitorStore.routeQuery.id,
        enable: 1,
        type: 0,
        mode: 0
      }).then(data => {
        aliasArr.value = [...aliasArr.value, ...data]
      })
    }
  }
  getAliasArrFn()

  const ioValue = ref([])
  const io = computed(() => {
    let data = monitorStore.pcs_io
    ioValue.value = data.map(item => item.dc)
    data.forEach(item => {
      item.name = isGroup.value ? `${item.label}_${uni.$t('外设')}`: uni.$t('外设')
      for (let i = 1; i < 11; i++) {
        let value = aliasArr.value.find(item => item.point == `180${i < 10 ? '0' + i : i}`)
        if (value) {
          item[`180${i < 10 ? '0' + i : i}_alias`] = value.alias
        } else {
          item[`180${i < 10 ? '0' + i : i}_alias`] = uni.$t('未知别名')
        }
      }
    })
    return data
  })
  
  const getStatus = computed(() => {
    return (index) => {
      if (io.value[index].isAnalysis == 0) {
        return io.value.onLineState == '在线' ? uni.$i18n().t('在线') : uni.$i18n().t('离线')
      } else if (io.value[index].isAnalysis == 1) {
        return io.value[index]['peripherals_18000'] == 1 ? uni.$i18n().t('告警') : uni.$i18n().t('正常')
      } else {
        return '--'
      }
    }
  })
</script>

<style scoped lang="scss">
  .data-time {
    display: flex;
    justify-content: space-between;
    background-color: #fff;
    padding: 20rpx 30rpx;
    margin-bottom: 20rpx;
    margin: 20rpx 30rpx;
    font-size: 10px;
    border-radius: 6px;
  }
  .device-info {
    /* background-color: $uni-bg-color; */
    background-color: #fff;
    border-radius: $uni-border-radius-lg;
    padding: 0 30rpx;
    margin: 20rpx 30rpx;

    .info-ti {
      font-weight: bold;
    }

    .info-item {
      padding: 30rpx 0;
      border-bottom: 1px solid $uni-bg-color-grey;

      .item-unit {
        font-size: 12px;
        color: $uni-text-color-grey;
        margin-left: 4rpx;
      }

      view:nth-child(2n) {
        width: 50%;
        text-align: right;
      }
    }

    .info-item:last-child {
      border-bottom: none;
    }
  }

  :deep(.u-collapse-item__content__text) {
    padding: 0;
    color: #000;
  }

  :deep(.u-cell__body) {
    padding: 30rpx 0;
  }

  :deep(.u-cell--clickable) {
    background-color: $uni-bg-color;
  }
</style>