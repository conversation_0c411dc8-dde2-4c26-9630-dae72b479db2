<template>
  <view class="scheme-list">
    <u-navbar :title="title" leftIconSize="30" :autoBack="true" :placeholder="true" class="tabbar" :titleStyle="{
    		        color: '#000'
    		      }">
      <template #right>
        <u-button type="primary" size="mini" style="height: 50rpx;"
          @click="handleCconfirmClick">{{ $t('分配') }}</u-button>
      </template>
    </u-navbar>

    <view class="scheme-list-wrap u-p-t-10 u-p-b-10 u-m-b-20">
      <u-search v-model="searchValue" :clearabled="true" searchIconSize="30rpx" height="60rpx"
        :placeholder="placeholder" @search="handleSearchClick" @custom="handleSearchClick"
        :actionText="$t('搜索')"></u-search>
      <view class="ft12 u-m-t-20 color-grey" v-if="initData.type == 0">
        {{$t('注：分配项目，会把项目所绑定的设备也一起被分配给用户。')}}
      </view>
    </view>

    <view class="scheme-list-wrap">
      <view class="scheme-list-wrap-item flex" v-for="item in userList" :key="item.userId"
        @click="handleItemClick(item)">
        <view class="u-line-1 scheme-list-wrap-item-left" :class="{ 'actived': item.userId == initData.userId }">
          {{ item.userName }}
        </view>
        <view v-if="item.userId == initData.userId">
          <u-icon name="checkbox-mark" color="#3c9cff"></u-icon>
        </view>
      </view>
    </view>
    <view v-if="!userList.length"
      style="height: calc(100% - 140rpx - 44px);background: #fff;padding-top: 300rpx;border-radius: 6px;">
      <u-empty icon="../../static/empty.webp" width="300" height="300" textSize="14px" :text="$t('暂无数据')">
      </u-empty>
    </view>
  </view>
</template>

<script setup>
  import {
    ref,
    toRefs,
    computed
  } from 'vue'
  import {
    onLoad,
    onShow,
    onUnload
  } from '@dcloudio/uni-app'
  import {
    listUser
  } from '@/api/login'
  import {
    allotDevice
  } from '@/api/device'
  import {
    allotSIM
  } from '@/api/sim'

  const initData = ref()
  const title = ref(uni.$t('分配项目'))
  onLoad((options) => {
    initData.value = {
      ...options
    }
    if (initData.value.type == 0) {
      title.value = uni.$t('分配项目')
    } else {
      title.value = uni.$t('分配SIM卡')
    }
  })
  onShow(() => {
    listUserFn()
  })

  const selectData = ref()
  const handleItemClick = (item) => {
    initData.value.userId = item.userId
    selectData.value = item
  }
  const handleCconfirmClick = () => {
    if (!initData.value.userId) {
      uni.$u.toast(uni.$t('请选择所要分配的用户'))
      return
    }
    let api = null
    let data = {}
    if (initData.value.type == 0) {
      api = allotDevice
      data = {
        projectId: initData.value.projectId,
        userId: initData.value.userId
      }
    } else {
      api = allotSIM
      data = {
        simId: initData.value.simId,
        userId: initData.value.userId
      }
    }
    api(data).then(response => {
      if (response.code !== 200) {
        uni.$u.toast(uni.$t('分配失败'))
        return
      }
      uni.$u.toast(uni.$t('分配成功'))
      uni.navigateBack()
    }).catch(() => {
      uni.$u.toast(uni.$t('分配失败'))
    })
  }

  const userList = ref([])
  const listUserFn = async () => {
    let deptId = uni.cache.getItem('userInfo').deptId
    const res = await listUser({
      pageNum: 1,
      pageSize: 9999,
      deptId,
      userName: searchValue.value
    })
    userList.value = res.rows.filter(item => item.deptId !== deptId)
  }
  /**
   * 搜索
   */
  const searchValue = ref()
  const placeholder = ref(uni.$t('请输入关键字'))
  const handleSearchClick = () => {
    listUserFn()
  }
</script>

<style lang="scss" scoped>
  .scheme-list {
    height: 100vh;
    overflow-y: auto;
    padding: 20rpx 30rpx;

    &-wrap {
      background-color: #fff;
      border-radius: 6px;
      padding: 0 30rpx;

      .actived {
        font-weight: bold;
        color: $u-primary;
      }

      &-item {
        padding: 30rpx 0;
        border-bottom: 1px solid $uni-bg-color-grey;
        justify-content: space-between;

        &-left {
          width: 80%;
        }
      }

      &-item:last-child {
        border-bottom: none;
      }
    }

    &-wrap1 {
      background-color: #fff;
      border-radius: 6px;
      padding: 0 30rpx 30rpx 30rpx;
      margin-bottom: 20rpx;

      .actived {
        font-weight: bold;
        color: $u-primary;
      }

      &-item {
        padding: 20rpx 0;
        border-bottom: 1px solid $uni-bg-color-grey;
        justify-content: space-between;

        &-left {
          width: 80%;
        }
      }

      &-item1 {
        padding: 10rpx 0 0 0;
        justify-content: space-between;
      }
    }
  }
</style>