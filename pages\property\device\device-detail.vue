<template>
  <view class="device-info">
    <view class="base-info">
      <view class="info-item flex justify-content align-items">
        <view class="bold flex align-items">
          <image src="../../../static/detail.png" class="item-img"></image>
          <span>{{ $t('设备信息') }}</span>
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('设备名称') }}</view>
        <view>{{ info.deviceName }}</view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('设备序列号') }}</view>
        <view :class="['flex', 'align-items', 'u-flex-end']" style="text-align: center;">
          <view class="u-m-r-5">{{ info.deviceSerialNumber }}</view>
          <u-copy :content="info.deviceSerialNumber" :notice="$t('复制成功')" class="flex align-items" style="width: auto;">
            <image src="../../../static/copy.png" class="copy-img-width"></image>
          </u-copy>
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('设备型号') }}</view>
        <view>{{ info.deviceModel }}
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('设备类型') }}</view>
        <view>{{ getDeviceType(info.deviceType, true) }}
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('电池容量') }}</view>
        <view>{{ info.deviceBatteryCapacity ?? '--' }}
          <span class="item-unit">kWh</span>
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('光伏装机容量') }}</view>
        <view>{{ info.photovoltaicInstalledCapacity ?? '--' }}
          <span class="item-unit">kWp</span>
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('额定功率') }}</view>
        <view>{{ info.deviceRatedPower ?? '--' }}
          <span class="item-unit">kW</span>
        </view>
      </view>
      <view class="info-item flex justify-content align-items" v-if="isGroup">
        <view>{{ $t('组合序列号') }}</view>
        <view :class="['flex', 'u-flex-column']">
          <view v-for="item in info.combinationDeviceSerialNumber" :key="item" :class="['flex', 'align-items', 'u-flex-end']" style="text-align: center;width: auto;">
            <view class="u-m-r-5">{{ item }}</view>
            <u-copy :content="item" :notice="$t('复制成功')" class="flex align-items" style="width: auto;">
              <image src="../../../static/copy.png" class="copy-img-width"></image>
            </u-copy>
          </view>
        </view>
      </view>
      <view class="info-item flex justify-content align-items" v-if="isGroup">
        <view>{{ $t('组合类型') }}</view>
        <view>
          <view v-for="item in info.combinationDeviceType" :key="item" style="width: auto;">{{ getDeviceType(item, true) }}</view>
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('创建人员') }}</view>
        <view>{{ info.nickName }}
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('添加时间') }}</view>
        <view>{{ info.createTime }}
        </view>
      </view>
    </view>
    <u-button type="primary" style="margin: 20rpx 30rpx;width: calc(100% - 60rpx);border-radius: 100rpx;" @click="handleDetailClick" v-if="isShow">{{  $t('查看详情') }}</u-button>
  </view>
</template>

<script setup>
  import {
    ref,
    computed
  } from 'vue'
  import {
    onLoad,
    onShow
  } from '@dcloudio/uni-app'
  import {
    getDeviceInfo
  } from '@/api/device.js'
  import { getDeviceType, isGroupFn } from '@/hook/useDeviceType.js'

  const id = ref()
  const isShow = ref(true)
  onLoad((options) => {
    isShow.value = JSON.parse(options.isShow)
    id.value = options.id
  })
  onShow(() => {
    getInfoFn()
  })

  const info = ref({})
  const getInfoFn = async () => {
    const res = await getDeviceInfo({
      deviceId: id.value
    })
    res.data.combinationDeviceType = res.data.combinationDeviceType && res.data.combinationDeviceType.split(',')
    res.data.combinationDeviceSerialNumber = res.data.combinationDeviceType && res.data.combinationDeviceSerialNumber.split(',')
    info.value = res.data
  }
  
  const isGroup = computed(() => {
    let type = info.value.deviceType
    return isGroupFn(type)
  })
  
  const handleDetailClick = () => {
    let item = info.value
    uni.navigateTo({
      url: `/pages/monitor/deviceDetails?id=${item.deviceSerialNumber}&type=${item.deviceType}&time=${item.timeZone ? encodeURIComponent(item.timeZone): encodeURIComponent('+08:00')}&name=${item.deviceName}&groupId=${encodeURIComponent(item.combinationDeviceSerialNumber)}&groupType=${encodeURIComponent(item.combinationDeviceType)}`
    })
  }

  const isAndroid = ref(uni.$u.sys().platform == 'android')
</script>

<style lang="scss" scoped>
  .device-info {
    height: 100vh;
    overflow: auto;
    .base-info,
    .group-info {
      background-color: $uni-bg-color;
      border-radius: $uni-border-radius-lg;
      padding: 0 30rpx;
      margin: 20rpx 30rpx;
    }

    .info-item {
      padding: 30rpx 0;
      border-bottom: 1px solid $detail-border-color;
      width: 100%;

      .item-img {
        width: 30rpx;
        height: 30rpx;
        margin-right: 10rpx;
      }

      .item-unit {
        font-size: 12px;
        // color: $uni-text-color-grey;
        margin-left: 4rpx;
      }

      view:nth-child(2n) {
        width: 50%;
        text-align: right;
      }
    }

    .info-item:last-child {
      border-bottom: none;
    }
  }
</style>