<template>
  <view class="landscape">
    <u-status-bar v-if="isAndroid" />
    <view v-if="monitorStore.orientation.isLine" style="position: relative;width: 100%">
      <!-- 功率分析日期筛选 -->
      <view class="flex align-items u-flex-center mb-20">
        <u-icon name="arrow-left" color="#333" @click="handleCutLineClick('prev')"></u-icon>
        <view class="date-text" @click="openLineDate">
          {{ lineValue1 }}
        </view>
        <u-icon name="arrow-right" color="#333" @click="handleCutLineClick('next')"></u-icon>
      </view>
      <view style="position: absolute;right: 10rpx;top: -4rpx">
        <image src="../../../static/landscape.png" style="width: 17rpx;height: 17rpx;" @click="handleLandscapeMode"></image> 
      </view>
      
        <Line v-if="!isGroup" ref="lineChart" :reshow="!loading"></Line>
        <LineGroup v-else ref="lineGroupChart" :reshow="!loading"></LineGroup>
    </view>
    <view style="width: 100%" v-else>
      <view class="flex justify-content mb-20" style="position: relative;">
        <u-subsection :list="dayList" mode="subsection" :current="current" fontSize="14px" style="width: 50%;"
          @change="sectionChange" :activeColor="otherColor.primaryColor"
          :inactiveColor="otherColor.mainColor"></u-subsection>
        <view class="flex align-items">
          <u-icon name="arrow-left" color="#333" @click="handleCutClick('prev')"></u-icon>
          <view class="date-text" @click="handleYearMonthAndYear">
            {{ value1 }}
          </view>
          <u-icon name="arrow-right" color="#333" @click="handleCutClick('next')"></u-icon>
        </view>
        <view style="position: absolute;right: 10rpx;bottom: -35rpx;z-index: 100">
          <image src="../../../static/landscape.png" style="width: 17rpx;height: 17rpx;" @click="handleLandscapeMode"></image> 
        </view>
      </view>
      <Bar ref="barChart" :reshow="!loading"></Bar>
    </view>

    <u-datetime-picker :show="yearMonthShow" v-model="yearMonthValue" mode="year-month" format="YYYY-MM-DD"
      itemHeight="48" @confirm="handleYearMonthConfirm" @cancel="handleYearMonthCancel"></u-datetime-picker>
    <u-picker :show="yearShow" :columns="yearColumns" itemHeight="48" @confirm="handleYearConfirm"
      :closeOnClickOverlay="true" @close="handleYearCancel" @cancel="handleYearCancel" :cancelText="$t('取消')"
      :confirmText="$t('确认')"></u-picker>
  </view>

  <u-popup mode="center" :show="isShowCalender" :customStyle="{
    width: '80%',height: '80%'
  }">
    <view style="width: 100%;height: calc(100% - 40px);overflow: scroll" class="flex u-flex-items-center u-flex-column">
      <uv-calendars ref="calendar" mode="single" @confirm="confirm" :date="lineValue1" :color="otherColor.primaryColor"
            :confirmColor="otherColor.primaryColor" :insert="true" @change="change"></uv-calendars>
    </view>
    <view style="height: 40px;border-top: 1px solid #EDEDED;width: 100%;" class="flex u-flex-around">
      <u-button @click="handlePopupClick">取消</u-button>
      <u-button type="primary" @click="handlePopupConfirm">确认</u-button>
    </view>
  		</u-popup>
</template>

<script setup>
  import {
    onUnload,
    onLoad,
    onReady,
    onShow
  } from '@dcloudio/uni-app'
  import {
    onMounted,
    onUnmounted,
    computed,
    ref,
    getCurrentInstance
  } from 'vue'
  import otherColor from '../../../common/other.module.scss'
  import {
    useMonitorStore
  } from '@/store/monitor.js'
  import {
    dayjsFn
  } from '@/common/utils.js'
  import dayjs from 'dayjs'
  import { isGroupFn, isPhotovoltaicFn, isEnergyFn } from '@/hook/useDeviceType.js'

  import Line from './line.vue'
  import LineGroup from './line-group.vue'
  import Bar from './bar.vue'

  const {
    proxy
  } = getCurrentInstance()

  const monitorStore = useMonitorStore()

  const deviceType = computed(() => monitorStore.routeQuery.type)

  // 光伏装机容量
  const isShowPhotovoltaicInstalledCapacity = computed(() => {
    let {
      type
    } = monitorStore.routeQuery
    return isPhotovoltaicFn(type)
  })
  // 储能充放电量
  const isShowDayOutputOfPlant = (() => {
    let {
      type
    } = monitorStore.routeQuery
    return isEnergyFn(type)
  })
  // 是否为组合类型
  const isGroup = computed(() => {
    let type = monitorStore.routeQuery.type
    return isGroupFn(type)
  })

  const dayList = ref([{
      name: uni.$t('月')
    },
    {
      name: uni.$t('年')
    }
  ])
  const current = ref(0)
  const sectionChange = (index) => {
    if (index == 0) {
      value1.value = uni.$u.timeFormat(new Date(), 'yyyy-mm')
      yearMonthValue.value = uni.$u.timeFormat(new Date(), 'yyyy-mm')
    } else if (index == 1) {
      value1.value = uni.$u.timeFormat(new Date(), 'yyyy')
    }
    current.value = index
    changeDate()
  }


  const show = ref(false);
  const value1 = ref();
  value1.value = uni.$u.timeFormat(new Date(), 'yyyy-mm')
  const handleDateClick = () => {
    show.value = true
  }

  const handleYearMonthAndYear = () => {
    if (current.value == 0) {
      yearMonthShow.value = true
    } else {
      yearShow.value = true
    }
  }
  /**
   * 月
   */
  const yearMonthShow = ref(false)
  const yearMonthValue = ref()
  yearMonthValue.value = uni.$u.timeFormat(new Date(), 'yyyy-mm')
  const handleYearMonthConfirm = (e) => {
    value1.value = uni.$u.timeFormat(e.value, 'yyyy-mm')
    changeDate()
    yearMonthShow.value = false
  }
  const handleYearMonthCancel = () => {
    yearMonthShow.value = false
  }

  /**
   * 年
   */
  const yearShow = ref(false)
  const yearColumns = ref([
    ['2014', '2015', '2016', '2017', '2018', '2019', '2020', '2021', '2022', '2023', '2024', '2025', '2026', '2027',
      '2028', '2029', '2030', '2031', '2032', '2033', '2034'
    ]
  ])
  const handleYearConfirm = (e) => {
    value1.value = e.value[0]
    changeDate()
    yearShow.value = false
  }
  const handleYearCancel = () => {
    yearShow.value = false
  }

  const changeDate = () => {
    let queryInfo = {
      type: undefined,
      endTime: '',
      startTime: '',
      deviceType: Number(monitorStore.routeQuery.type)
    }
    if (current.value == 0) {
      queryInfo.type = 2
      queryInfo.startTime = dayjsFn(value1.value).startOf('month').format('YYYY-MM-DD')
      queryInfo.endTime = dayjsFn(value1.value).endOf('month').format('YYYY-MM-DD')
    } else if (current.value == 1) {
      queryInfo.type = 3
      queryInfo.startTime = dayjsFn(value1.value).startOf('year').format('YYYY-MM-DD')
      queryInfo.endTime = dayjsFn(value1.value).endOf('year').format('YYYY-MM-DD')
    }
    monitorStore.queryInfo = queryInfo
    monitorStore.electricStatisticsFn(monitorStore.routeQuery.id)
  }
  // changeDate()

  /**
   * 日期切换
   */
  const handleCutClick = (type) => {
    let api = type == 'next' ? 'add' : 'subtract'
    if (current.value == 0) {
      let addDate = dayjsFn(value1.value)[api](1, 'month')
      value1.value = uni.$u.timeFormat(addDate, 'yyyy-mm')
    } else if (current.value == 1) {
      let addDate = dayjsFn(value1.value)[api](1, 'year')
      value1.value = uni.$u.timeFormat(addDate, 'yyyy')
    }
    changeDate()
  }

  /**
   * 功率分析日期筛选
   */
  // console.log(monitorStore.control)
  const lineValue1 = ref()
  // lineValue1.value = monitorStore.control.sdt ? uni.$u.timeFormat(monitorStore.control.sdt, 'yyyy-mm-dd'): ''
  const isShowCalender = ref(false)
  const openLineDate = () => {
    // proxy.$refs.calendar.open()
    isShowCalender.value = true
  }
  const lineValue2 = ref()
  const change = (e) => {
    lineValue2.value = e
  }
  const handlePopupClick = () => {
    isShowCalender.value = false
  }
  const handlePopupConfirm = () => {
    confirm(lineValue2.value)
  }
  const confirm = (e) => {
    let utcOffset = Number(decodeURIComponent(monitorStore.routeQuery.time).split(':')[0])
    // let nowDate = dayjs.utc(isGroup.value ? monitorStore.groupControl[0].sdt : monitorStore.control.sdt).utcOffset(
    //   utcOffset).format('YYYY-MM-DD')
    let nowDate = dayjs.utc(isGroup.value ? monitorStore.groupControl[0].sdt : monitorStore.control.sdt).format(
      'YYYY-MM-DD')
    if (dayjsFn(e.fulldate).isAfter(nowDate)) return uni.showToast({
      icon: 'none',
      title: uni.$t('不可以选择今天以后的时间')
    })
    lineValue1.value = e.fulldate
    changeLineDate(e.fulldate)
  }
  const handleCutLineClick = (type) => {
    if (!(isGroup.value ? monitorStore.groupControl[0].sdt : monitorStore.control.sdt)) return uni.$u.toast('暂无数据')
    let utcOffset = Number(decodeURIComponent(monitorStore.routeQuery.time).split(':')[0])
    // let nowDate = dayjs.utc(isGroup.value ? monitorStore.groupControl[0].sdt : monitorStore.control.sdt).utcOffset(
    //   utcOffset).format('YYYY-MM-DD')
    let nowDate = dayjs.utc(isGroup.value ? monitorStore.groupControl[0].sdt : monitorStore.control.sdt).format(
      'YYYY-MM-DD')
    let api = type == 'next' ? 'add' : 'subtract'
    let addDate = dayjsFn(lineValue1.value)[api](1, 'day')
    if (dayjsFn(addDate).isAfter(nowDate)) return uni.showToast({
      icon: 'none',
      title: uni.$t('不可以选择今天以后的时间')
    })
    lineValue1.value = uni.$u.timeFormat(addDate, 'yyyy-mm-dd')
    changeLineDate(lineValue1.value)
  }
  const loading = ref(false)
  const changeLineDate = (date) => {
    loading.value = true
    let utcOffset = Number(decodeURIComponent(monitorStore.routeQuery.time).split(':')[0])
    let lineDate = ''
    if (date) {
      // let nowDate = dayjs.utc(isGroup.value ? monitorStore.groupControl[0].sdt : monitorStore.control.sdt).utcOffset(
      //   utcOffset).format('YYYY-MM-DD')
      let nowDate = dayjs.utc(isGroup.value ? monitorStore.groupControl[0].sdt : monitorStore.control.sdt).format(
        'YYYY-MM-DD')
      if (date == nowDate) lineDate = isGroup.value ? monitorStore.groupControl[0].sdt : monitorStore.control.sdt
      if (dayjsFn(date).isBefore(nowDate)) lineDate = date + ' 23:59:59'
    } else {
      lineDate = isGroup.value ? monitorStore.groupControl[0].sdt : monitorStore.control.sdt
    }
    if (isGroup.value) {
      monitorStore.lineGroupQueryInfo = {
        ...monitorStore.lineGroupQueryInfo,
        date: lineDate
      }
      monitorStore.powerAnalysisStatisticsGroupFn({
        deviceSerialNumber: monitorStore.routeQuery.id,
        deviceType: monitorStore.routeQuery.type,
        timeZone: monitorStore.routeQuery.time,
        groupId: monitorStore.routeQuery.groupId,
        groupType: monitorStore.routeQuery.groupType,
      }).then(() => {
        isShowCalender.value = false
        loading.value = false
      })

    } else {
      monitorStore.lineQueryInfo = {
        ...monitorStore.lineQueryInfo,
        date: lineDate
      }
      monitorStore.powerAnalysisStatisticsFn({
        deviceSerialNumber: monitorStore.routeQuery.id,
        deviceType: monitorStore.routeQuery.type,
        timeZone: monitorStore.routeQuery.time,
      }).then(() => {
        isShowCalender.value = false
        loading.value = false
      })
    }
  }
  const initLineDate = () => {
    if (isGroup.value) {
      lineValue1.value = uni.$u.timeFormat(monitorStore.groupControl[0].sdt, 'yyyy-mm-dd')
    } else {
      lineValue1.value = uni.$u.timeFormat(monitorStore.control.sdt, 'yyyy-mm-dd')
    }
    changeLineDate()
  }
  // initLineDate()
  
  const handleLandscapeMode = () => {
    // #ifdef APP-PLUS
    plus.screen.lockOrientation('portrait');
    plus.screen.lockOrientation('portrait-primary');
    // #endif
    uni.navigateBack()
  }
  
  const isAndroid = ref(uni.$u.sys().platform == 'android')

  onMounted(() => {
    // #ifdef APP-PLUS
    plus.screen.lockOrientation('landscape');
    plus.screen.lockOrientation('landscape-primary');
    // #endif
    if (!monitorStore.orientation.isLine) {
      proxy.$refs.barChart.opts.extra.column.width = 10
      proxy.$refs.barChart.opts.xAxis.labelCount = 6
      changeDate()
    } else {
      if (isGroup.value) {
        proxy.$refs.lineGroupChart.opts.xAxis.labelCount = 10
      } else {
        proxy.$refs.lineChart.opts.xAxis.labelCount = 10
      }
      initLineDate()
    }
  })
  onUnmounted(() => {
    // #ifdef APP-PLUS
    plus.screen.lockOrientation('portrait');
    plus.screen.lockOrientation('portrait-primary');
    // #endif
  })
</script>

<style scoped lang="scss">
  .landscape {
    padding: 20rpx 30rpx 0rpx 40rpx;
    width: 100%;
    height: 100%;
    background-color: #fff;
  }

  .date-text {
    margin: 0 20px;
    // color: $uni-text-color-grey;
  }

  :deep(.uni-date-x--border) {
    border: none !important;
  }

  :deep(.uni-date-x .icon-calendar) {
    display: none;
  }

  :deep(.uni-date__x-input) {
    text-align: center;
    padding: 0;
    margin: 0 10rpx;
  }

  :deep(.u-navbar__content__left) {
    display: none;
  }
  
  :deep(.uv-calendar-body) {
    .uv-calendar-item__weeks-box-item {
      height: 48px;
      width: 80rpx;
    }
    .uv-calendar-item__weeks-lunar-text {
      font-size: 14px;
    }
  }
</style>