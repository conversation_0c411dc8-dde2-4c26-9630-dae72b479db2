<template>
  <view class="property">
    <z-paging ref="paging" v-model="tableData" show-refresher-update-time refresher-update-time-key="property"
      @query="queryList" hide-empty-view>
      <template #top>
        <u-navbar :title="$t('资产管理')" :bgColor="otherColor.bgColor" :placeholder="true" class="tabbar">
          <template #right>
            <image src="../../static/add.webp" mode="" class="top-add" @click="handleAddClick"></image>
          </template>
        </u-navbar>
        <u-tabs :list="tabList" :scrollable="isScrollable" lineHeight="4px" :current="current" @click="handleTabClick"
          lineColor="rgba(0,0,0,.6)" :itemStyle="{
                lineHeight: '44px',
                fontSize: '14px'
              }" :activeStyle="{
                fontSize: '14px'
              }" class="tabList"></u-tabs>
        <view class="list-top">
          <view class=" flex justify-content align-items">
            <view class="flex u-flex-items-center mr-20 u-p-l-15" @click="handleSearchList">
              <view style="font-size: 12px;margin-right: 6rpx;">{{ selectText }}</view>
              <u-icon name="arrow-down-fill" size="10px"></u-icon>
            </view>
            <u-input :placeholder="$t('请输入关键字')" v-model="queryInfo.searchValue" :customStyle="{...newSearchCustomStyle, backgroundColor: otherColor.searchInputBgColor }" class="input-search" placeholderStyle="fontSize: 12px" confirmType="search" fontSize="12px"
              @confirm="handleSearch1Click">
              <template #prefix>
                <u-icon name="search" size="36"></u-icon>
              </template>
            </u-input>
            <view class="flex u-flex-items-center u-m-r-10 u-p-l-15" @click="handleSearch1Click">
              <view style="font-size: 12px;">{{ $t('搜索') }}</view>
            </view>
          </view>
        </view>
      </template>
      <view class="items" style="height: 60vh;padding-top: 100rpx;" v-if="!tableData.length">
        <u-empty icon="../../static/empty.webp" width="300" height="300" textSize="14px" :text="$t('暂无数据')">
        </u-empty>
      </view>
      <view class="new-items">
        <template v-if="current == 0">
          <view class="new-items-wrap1" v-for="item in tableData" :key="item.projectId">
            <view class="new-items-wrap1-item flex u-flex-between
">
              <view class="new-items-wrap1-item-left flex u-flex-items-center">
                <image src="../../static/project-item.webp" mode="" style="width: 34rpx;height: 34rpx"></image>
                <view class="u-line-1 ml-5">{{ item.projectName }}</view>
              </view>
              <view class="flex u-flex-items-center" v-if="isShowPerm(['system:project:allotDevice'])" @click="handleAssignClick(item)">
                <image src="../../static/assign.webp" mode="" style="width: 34rpx;height: 34rpx"></image>
                <view class="ft12 u-m-l-6">{{ $t('分配') }}</view>
              </view>
            </view>
            <view class="flex u-flex-between u-p-b-20" @click="handleItemClick(item)">
              <view>
                <view class="new-items-wrap1-item1 flex">
                  <view class="u-line-1">{{ $t('设备总数') }}：{{ item.countDevice }} {{ $t('台') }}</view>
                </view>
                <view class="new-items-wrap1-item1 flex">
                  <view class="u-line-1">{{ $t('所属国家') }}：{{ item.country }}</view>
                </view>
                <view class="new-items-wrap1-item1 flex">
                  <view class="u-line-1">{{ $t('时区') }}：UTC{{ item.timeZone }}</view>
                </view>
                <view class="new-items-wrap1-item1 flex">
                  <view class="u-line-1">{{ $t('创建人员') }}：{{ item.nickName ? item.nickName: '--' }}</view>
                </view>
                <view class="new-items-wrap1-item1 flex">
                  <view class="u-line-1">{{ $t('项目地址') }}：{{ item.projectAddress }}</view>
                </view>
              </view>
              <u-icon name="arrow-right" color="#333"></u-icon>
            </view>
            <view class="flex new-items-wrap1-item-bo u-p-t-20 u-flex-around u-flex-items-center">
              <view class="flex u-flex-items-center" @click="handleEditClick(item)">
                <image src="../../static/edit.webp" mode="" style="width: 30rpx;height: 30rpx;margin-right: 20rpx">
                </image>
                {{ $t('修改') }}
              </view>
              <u-line direction="col" length="20rpx"></u-line>
              <view class="flex u-flex-items-center" @click="handleDeleteClick(item)">
                <image src="../../static/remove.webp" mode="" style="width: 30rpx;height: 30rpx;margin-right: 20rpx">
                </image>
                {{ $t('删除') }}
              </view>
            </view>
          </view>
        </template>
        <template v-if="current == 1">
          <view class="new-items-wrap1" v-for="item in tableData" :key="item.deviceId">
            <view class="new-items-wrap1-item flex">
              <view class="new-items-wrap1-item-left flex u-flex-items-center">
                <image src="../../static/planList.webp" mode="" style="width: 30rpx;height: 30rpx" />
                <view class="u-line-1 u-m-l-10">{{ item.deviceName }}</view>
              </view>
            </view>
            <view class="flex u-flex-between u-p-b-20" @click="handleItemClick(item)">
              <view>
                <view class="new-items-wrap1-item1 flex">
                  <view class="u-line-1">{{ $t('设备类型') }}：{{ getDeviceType(item.deviceType, true) }}</view>
                </view>
                <view class="new-items-wrap1-item1 flex">
                  <view class="u-line-1">{{ $t('额定功率') }}：{{ item.deviceRatedPower }} kW</view>
                </view>
                <view class="new-items-wrap1-item1 flex">
                  <view class="u-line-1">{{ $t('光伏装机容量') }}：{{ item.photovoltaicInstalledCapacity || '--' }} kWp</view>
                </view>
                <view class="new-items-wrap1-item1 flex">
                  <view class="u-line-1">{{ $t('创建人员') }}：{{ item.nickName ? item.nickName: '--' }}</view>
                </view>
              </view>
              <!-- <u-icon name="arrow-right" color="#333"></u-icon> -->
              <view class="flex u-flex-center u-flex-items-center u-m-t-20">
                <image src="../../static/device-system.png" mode="" style="width: 110rpx;height: 120rpx"
                  v-if="isConverterFn(item.deviceType)" />
                <image src="../../static/converter.png" mode="" style="width: 60rpx;height: 120rpx" v-else />
              </view>
            </view>
            <view class="flex new-items-wrap1-item-bo u-p-t-20 u-flex-around u-flex-items-center">
              <view class="flex u-flex-items-center" @click="handleEditClick(item)">
                <image src="../../static/edit.webp" mode="" style="width: 30rpx;height: 30rpx;margin-right: 20rpx">
                </image>
                {{ $t('修改') }}
              </view>
              <u-line direction="col" length="20rpx"></u-line>
              <view class="flex u-flex-items-center" @click="handleDeleteClick(item)">
                <image src="../../static/remove.webp" mode="" style="width: 30rpx;height: 30rpx;margin-right: 20rpx">
                </image>
                {{ $t('删除') }}
              </view>
            </view>
          </view>
        </template>
        <template v-if="current == 2">
          <view class="new-items-wrap1" v-for="item in tableData" :key="item.deviceId">
            <view class="new-items-wrap1-item flex u-flex-between">
              <view class="new-items-wrap1-item-left flex u-flex-items-center">
                <image src="../../static/sim.png" mode="" style="width: 30rpx;height: 30rpx" />
                <view class="u-line-1 u-m-l-10">{{ item.sim }}</view>
              </view>
              <u-tag :text="getStatusCom(item.status)?.label" size="mini" :type="getStatusCom(item.status)?.type" plain
                plainFill></u-tag>
            </view>
            <view class="flex u-flex-between u-p-b-20" @click="handleItemClick(item)">
              <view>
                <view class="new-items-wrap1-item1 flex">
                  <view class="u-line-1">{{ $t('流量套餐') }}：{{ item.packageName }}</view>
                </view>
                <view class="new-items-wrap1-item1 flex">
                  <view class="u-line-1">{{ $t('月使用流量') }}：{{ item.usedFlow }} MB</view>
                </view>
                <view class="new-items-wrap1-item1 flex">
                  <view class="u-line-1">{{ $t('月剩余流量') }}：{{ item.residualFlow }} MB</view>
                </view>
                <view class="new-items-wrap1-item1 flex">
                  <view class="u-line-1">{{ $t('创建人员') }}：{{ item.createBy ? item.createBy: '--' }}</view>
                </view>
                <view class="new-items-wrap1-item1 flex">
                  <view class="u-line-1">{{ 'ICCID' }}：{{ item.iccid }}</view>
                </view>
              </view>
              <u-icon name="arrow-right" color="#333"></u-icon>
            </view>
            <view class="flex new-items-wrap1-item-bo u-p-t-20 u-flex-around u-flex-items-center">
              <view class="flex u-flex-items-center" v-if="isShowPerm(['system:sim:allotSim'])" @click="handleAssignClick(item)">
                <image src="../../static/assign.webp" mode="" style="width: 30rpx;height: 30rpx;margin-right: 20rpx">
                </image>
                {{ $t('分配') }}
              </view>
              <u-line direction="col" length="20rpx"></u-line>
              <view class="flex u-flex-items-center" v-if="isShowPerm(['system:sim:recharge'])" @click="handleRechargeClick(item)">
                <image src="../../static/pay.webp" mode="" style="width: 30rpx;height: 30rpx;margin-right: 20rpx">
                </image>
                {{ $t('充值') }}
              </view>
              <u-line direction="col" length="20rpx"></u-line>
              <view class="flex u-flex-items-center" @click="handleDeleteClick(item)">
                <image src="../../static/remove.webp" mode="" style="width: 30rpx;height: 30rpx;margin-right: 20rpx">
                </image>
                {{ $t('删除') }}
              </view>
            </view>
          </view>
        </template>
      </view>

    </z-paging>

    <u-picker :show="isShowSelect" :defaultIndex="defaultSelectIndex" keyName="text" :columns="[fieldOptions]"
      itemHeight="88" @confirm="handleSelectConfirm" :closeOnClickOverlay="true" @close="handleSelectCancel"
      @cancel="handleSelectCancel" :cancelText="$t('取消')" :confirmText="$t('确认')"></u-picker>
  </view>
</template>

<script setup>
  import {
    ref,
    getCurrentInstance,
    computed,
    toRefs
  } from 'vue'
  import {
    onShow,
    onLoad,
    onReady,
    onPageScroll
  } from '@dcloudio/uni-app'
  import {
    projectList,
    deleteProject
  } from '@/api/project.js'
  import {
    deviceList,
    deleteDevice
  } from '@/api/device.js'
  import {
    simList,
    rechargeSim,
    deleteSim
  } from '@/api/sim.js'
  import otherColor from '../../common/other.module.scss'
  import {
    getDeviceType,
    isGroupFn,
    isConverterFn
  } from '@/hook/useDeviceType.js'
  import {
    isShowPerm,
    isShowRoute
  } from '@/common/utils.js'
  import { newSearchCustomStyle } from '@/constant'

  const tabList = computed(() => {
    if (isShowRoute('SIM', uni.cache.getItem('routes'))) {
      return [{
        name: uni.$i18n().t('项目管理')
      }, {
        name: uni.$i18n().t('设备入库')
      }, {
        name: 'SIM'
      }]
    }
    return [{
      name: uni.$i18n().t('项目管理')
    }, {
      name: uni.$i18n().t('设备入库')
    }]
  })

  const {
    proxy
  } = getCurrentInstance()
  const current = ref(0)
  const handleTabClick = (item) => {
    current.value = item.index
    if (current.value == 0) {
      queryInfo.value.searchKey = 'projectName'
    } else if (current.value == 1) {
      queryInfo.value.searchKey = 'deviceSerialNumber'
    } else if (current.value == 2) {
      queryInfo.value.searchKey = 'ac'
    }
    proxy.$refs.paging.reload()
  }
  const options = ref([{
      value: undefined,
      label: uni.$i18n().t('全部')
    },
    {
      value: 2,
      label: uni.$i18n().t('已激活')
    },
    {
      value: 3,
      label: uni.$i18n().t('停机')
    },
    {
      value: 7,
      label: uni.$i18n().t('已注销')
    },
    {
      value: 1,
      label: uni.$i18n().t('可激活')
    },
    {
      value: 6,
      label: uni.$i18n().t('可测试')
    },
    {
      value: 11,
      label: uni.$i18n().t('库存')
    },
    {
      value: 15,
      label: uni.$i18n().t('预注销')
    },
    {
      value: 88,
      label: uni.$i18n().t('维护中')
    },
  ])
  const getStatusCom = computed(() => {
    return (status) => {
      if (!status) return
      let label = options.value.find(item => item.value == status).label
      let type = ''
      if (status == 2) {
        type = 'success'
      } else if (status == 3) {
        type = 'warning'
      } else if (status == 7) {
        type = 'error'
      } else {
        type = ''
      }
      return {
        label,
        type
      }
    }
  })
  const tableData = ref([])
  const paging = ref({
    pageNum: 1,
    pageSize: 10,
    total: 0
  })
  const queryInfo = ref({
    deviceSerialNumber: undefined,
    projectName: undefined,
    sim: undefined,
    searchKey: 'projectName',
    searchValue: undefined
  })
  const projectListFn1 = async ({
    pageNum,
    pageSize
  }) => {
    const res = await projectList({
      pageNum,
      pageSize,
      [queryInfo.value.searchKey]: queryInfo.value.searchValue
    })
    paging.value.total = res.total
    return res.rows
  }
  const deviceListFn1 = async ({
    pageNum,
    pageSize
  }) => {
    const res = await deviceList({
      pageNum,
      pageSize,
      [queryInfo.value.searchKey]: queryInfo.value.searchValue
    })
    paging.value.total = res.total
    return res.rows
  }
  const simListFn1 = async ({
    pageNum,
    pageSize
  }) => {
    const res = await simList({
      pageNum,
      pageSize,
      [queryInfo.value.searchKey]: queryInfo.value.searchValue
    })
    paging.value.total = res.total
    return res.rows
  }


  const queryList = (pageNo, pageSize) => {
    // 组件加载时会自动触发此方法，因此默认页面加载时会自动触发，无需手动调用
    // 这里的pageNo和pageSize会自动计算好，直接传给服务器即可
    // 模拟请求服务器获取分页数据，请替换成自己的网络请求
    let api = null
    if (current.value == 0) {
      api = projectListFn1
    } else if (current.value == 1) {
      api = deviceListFn1
    } else if (current.value == 2) {
      api = simListFn1
    }
    api({
      pageNum: pageNo,
      pageSize
    }).then(res => {
      // 将请求的结果数组传递给z-paging
      proxy.$refs.paging.completeByTotal(res, paging.value.total)
    }).catch(res => {
      // 如果请求失败写this.$refs.paging.complete(false);
      // 注意，每次都需要在catch中写这句话很麻烦，z-paging提供了方案可以全局统一处理
      // 在底层的网络请求抛出异常时，写uni.$emit('z-paging-error-emit');即可
      proxy.$refs.paging.complete(false);
    })
  }


  const handleSearchClick = () => {
    proxy.$refs.paging.reload()
  }

  const handleItemClick = (item) => {
    if (current.value == 0) {
      uni.navigateTo({
        url: `/pages/property/project/project-detail?id=${item.projectId}`
      })
    } else if (current.value == 1) {
      uni.navigateTo({
        url: `/pages/property/device/device-detail?id=${item.deviceId}&isShow=false`
      })
    } else if (current.value == 2) {
      uni.navigateTo({
        url: `/pages/property/sim/sim-detail?id=${item.id}`
      })
    }
  }

  /**
   * 充值
   */
  const handleRechargeClick = async (item) => {
    const res = await rechargeSim({
      iccid: item.iccid
    })
    // #ifdef APP-PLUS
    plus.runtime.openURL(res.data)
    // #endif
    // #ifdef H5
    window.open(res.data, '_blank')
    // #endif
  }


  /**
   * 是否滚动
   */
  const isScrollable = ref(true)
  onShow(() => {
    const language = uni.cache.getItem('language') ? uni.cache.getItem('language') : 'en'
    if (language == 'zh-Hans') {
      isScrollable.value = false
    } else {
      isScrollable.value = true
    }
  })

  /**
   * 搜索
   */
  const isShowSelect = ref(false)
  const defaultSelectIndex = ref([0])
  const fieldOptions = computed(() => {
    if (current.value == 0) {
      return [{
          text: uni.$t('项目名称'),
          value: 'projectName'
        },
        {
          text: uni.$t('国家'),
          value: 'country'
        },
        {
          text: uni.$t('创建人员'),
          value: 'nickName'
        },
      ]
    } else if (current.value == 1) {
      return [{
          text: 'SN',
          value: 'deviceSerialNumber'
        },
        {
          text: uni.$t('设备名称'),
          value: 'deviceName'
        },
        {
          text: uni.$t('创建人员'),
          value: 'nickName'
        },
      ]
    } else if (current.value == 2) {
      return [{
          text: 'SN',
          value: 'ac'
        },
        {
          text: 'ICCID',
          value: 'iccid'
        },
        {
          text: uni.$t('卡号'),
          value: 'sim'
        },
        {
          text: uni.$t('创建人员'),
          value: 'createBy'
        },
      ]
    }
  })
  const selectText = computed(() => {
    return fieldOptions.value.find(item => item.value == queryInfo.value.searchKey)?.text
  })
  const handleSelectConfirm = ({
    value
  }) => {
    queryInfo.value.searchKey = value[0].value
    isShowSelect.value = false
  }
  const handleSelectCancel = () => {
    defaultSelectIndex.value = [fieldOptions.value.findIndex(item => item.value == queryInfo.value.searchKey)]
    isShowSelect.value = false
  }
  const handleSearchList = () => {
    isShowSelect.value = true
  }
  const handleSearch1Click = () => {
    proxy.$refs.paging.reload()
  }

  /**
   * 添加
   */
  const handleAddClick = () => {
    if (current.value == 0) {
      uni.navigateTo({
        url: '/pages/property/project/addProject'
      })
    } else if (current.value == 1) {
      uni.navigateTo({
        url: '/pages/property/device/addDevice'
      })
    } else if (current.value == 2) {
      uni.navigateTo({
        url: '/pages/property/sim/addSim'
      })
    }
  }
  /**
   * 修改
   */
  const handleEditClick = (item) => {
    if (current.value == 0) {
      uni.navigateTo({
        url: `/pages/property/project/addProject?id=${item.projectId}`
      })
    } else if (current.value == 1) {
      uni.navigateTo({
        url: `/pages/property/device/addDevice?id=${item.deviceId}`
      })
    } else if (current.value == 2) {}
  }
  /**
   * 删除
   */
  const handleDeleteClick = (item) => {
    uni.showModal({
      title: uni.$t('系统提示'),
      content: uni.$t('是否确认删除该数据项？'),
      confirmColor: otherColor.primaryColor,
      success: (res) => {
        if (res.confirm) {
          let api = null
          let data = {}
          if (current.value == 0) {
            api = deleteProject
            data = {
              projectIds: item.projectId
            }
          } else if (current.value == 1) {
            api = deleteDevice
            data = {
              deviceIds: item.deviceId
            }
          } else if (current.value == 2) {
            api = deleteSim
            data = {
              id: item.id
            }
          }
          api(data).then(() => {
            setTimeout(() => {
              uni.$u.toast(uni.$t('删除成功'))
            }, 20)
            proxy.$refs.paging.reload()
          }).catch(() => {
            uni.$u.toast(uni.$t('删除失败'))
          })
        } else if (res.cancel) {
          console.log('用户点击取消');
        }
      }
    });
  }
  
  /**
   * 分配
   */
  const handleAssignClick = (item) => {
    if (current.value == 0) {
      uni.navigateTo({
        url: `/pages/property/assignUser?projectId=${item.projectId}&type=${current.value}`
      })
    } else {
      uni.navigateTo({
        url: `/pages/property/assignUser?simId=${item.id}&type=${current.value}`
      })
    }
  }
</script>

<style scoped lang="scss">
  .property {
    width: 100%;
    height: 100vh;
    overflow: hidden;

    /* background-color: $uni-bg-color-grey; */
    .top-add {
      width: 50rpx;
      height: 50rpx;
    }
  }

  .items {
    background-color: $uni-bg-color;
    padding: 20rpx 30rpx;
    margin: 0rpx 30rpx;
    border-radius: $uni-border-radius-lg;
  }

  .item:last-child {
    margin-bottom: 0;
  }

  .item {
    // background-color: $uni-bg-color;
    background-color: $uni-bg-color-grey;
    padding: 16rpx;
    border-radius: $uni-border-radius-lg;
    font-size: 12px;
    margin-bottom: 20rpx;
    line-height: 38rpx;

    .item-le {
      flex: 1;
      position: relative;

      .le-img-project {
        width: 140rpx;
        height: 120rpx;
      }

      .le-img-device {
        width: 150rpx;
        height: 160rpx;
        margin-top: 10rpx;
      }
    }

    .item-ri {
      flex: 4;
      font-size: 12px;

      .ri-title {
        font-size: 12px;
        font-weight: bold;
      }

      .ri-num {
        font-weight: bold;
        margin-right: 4rpx;
      }
    }
  }

  .sim-items {
    margin: 0rpx 30rpx;

    .item:last-child {
      margin-bottom: 0;
    }

    .item {
      background-color: $uni-bg-color;
      padding: 20rpx 30rpx;
      border-radius: $uni-border-radius-lg;
      font-size: 12px;
      margin-bottom: 20rpx;
      line-height: 40rpx;

      .item-top {
        padding-bottom: 20rpx;
        margin-bottom: 10rpx;
        border-bottom: 1px solid $uni-bg-color-grey;
      }

      .item-img {
        width: 30rpx;
        height: 30rpx;
        margin-right: 5rpx;
      }

      .item-ti {
        font-size: 14px;
        font-weight: bold;
        line-height: 19px;
      }

      .item-num {
        font-weight: bold;
        margin-right: 4rpx;
      }

      .item-ce {
        // width: 30%;
        // display: inline-block;
      }

      .item-bottom {
        padding-top: 20rpx;
        margin-top: 10rpx;
        border-top: 1px solid $uni-bg-color-grey;
      }
    }
  }

  .list-top {
    background-color: $uni-bg-color;
    padding: 10rpx 30rpx 10rpx 10rpx;
    z-index: 998;
    margin: 20rpx 30rpx 20rpx 30rpx;
    border-radius: $uni-border-radius-lg;

    .top-img {
      width: 32rpx;
      height: 32rpx;
      margin-right: 14rpx;
    }

    .input-search {
      border-radius: 100px;
      border-color: transparent !important;
      flex: 1;
    }
  }

  :deep(.u-navbar__content__left) {
    display: none;
  }

  :deep(.u-tabs__wrapper__nav__item__text) {
    color: $main-color !important;
  }

  :deep(.uni-scroll-view) {
    .u-tabs__wrapper__nav {
      padding-left: 30rpx;
    }

    .u-tabs__wrapper__nav__item {
      padding: 0 30rpx
    }
  }

  :deep(.u-input) {
    flex: none
  }

  .new-items {
    margin: 0rpx 30rpx;

    &-wrap1 {
      background-color: #fff;
      border-radius: 6px;
      padding: 0 30rpx 30rpx 30rpx;
      margin-bottom: 20rpx;

      .actived {
        font-weight: bold;
        color: $u-primary;
      }

      &-item {
        padding: 20rpx 0;
        border-bottom: 1px solid $uni-bg-color-grey;
        justify-content: space-between;

        &-left {
          width: 80%;
        }

        &-bo {
          border-top: 1px solid $uni-bg-color-grey;
          width: 100%;
        }
      }

      &-item1 {
        padding: 10rpx 0 0 0;
        justify-content: space-between;
        font-size: 12px;
      }
    }
  }

  .item-new {
    // background-color: $uni-bg-color;
    // background-color: $uni-bg-color-grey;
    // padding: 16rpx;
    // border-radius: $uni-border-radius-lg;
    font-size: 12px;
    margin-bottom: 20rpx;
    line-height: 38rpx;

    .item-le {
      flex: 1;
      position: relative;

      .le-img-project {
        width: 140rpx;
        height: 120rpx;
      }

      .le-img-device {
        width: 150rpx;
        height: 160rpx;
        margin-top: 10rpx;
      }
    }

    .item-ri {
      flex: 4;
      font-size: 12px;

      .ri-title {
        font-size: 12px;
        font-weight: bold;
      }

      .ri-num {
        font-weight: bold;
        margin-right: 4rpx;
      }
    }
  }
</style>