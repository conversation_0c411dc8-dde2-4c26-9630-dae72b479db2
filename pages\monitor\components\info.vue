<template>
  <view class="data-time">
    <view>{{ $t('上报时间') }}</view>
    <view>{{ control?.sdt }}({{ baseInfo.timeZone }})</view>
  </view>
  <view class="device-info">
    <view class="info-item flex justify-content align-items">
      <view class="color-grey">{{ $t('设备型号') }}</view>
      <view>{{ baseInfo.deviceModel }}</view>
    </view>
    <view class="info-item flex justify-content align-items">
      <view class="color-grey">{{ $t('设备类型') }}</view>
      <view>{{ getDeviceType(monitorStore.routeQuery.type, true) }}</view> 
    </view>
    <view class="info-item flex justify-content align-items">
      <view class="color-grey">{{ $t('额定容量') }}</view>
      <view>{{ baseInfo.deviceBatteryCapacity }}
        <span class="item-unit">kWh</span>
      </view>
    </view>
    <view class="info-item flex justify-content align-items">
      <view class="color-grey">{{ $t('额定功率') }}</view>
      <view>{{ baseInfo.deviceRatedPower }}
        <span class="item-unit">kW</span>
      </view>
    </view>
    <view class="info-item flex justify-content align-items" v-if="isShowPhotovoltaicInstalledCapacity">
      <view class="color-grey">{{ $t('光伏装机容量') }}</view>
      <view>{{ baseInfo.photovoltaicInstalledCapacity }}
        <span class="item-unit">kWp</span>
      </view>
    </view>
    <view class="info-item flex justify-content align-items">
      <view class="color-grey">{{ $t('序列号') }}</view>
      <view :class="['flex', 'align-items', 'u-flex-end']" style="text-align: center;">
        <view class="u-m-r-5">{{ baseInfo.deviceSerialNumber }}</view>
        <u-copy :content="baseInfo.deviceSerialNumber" :notice="$t('复制成功')" class="flex align-items"
          style="width: auto;">
          <image src="../../../static/copy.png" class="copy-img-width"></image>
        </u-copy>
      </view>
    </view>
    <view class="info-item flex justify-content align-items">
      <view class="color-grey">{{ $t('系统工作模式') }}</view>
      <view v-if="control['jk_1001']">{{ parse.getOnAndOff(control['jk_1001']) }}</view>
      <view v-else>--</view>
    </view>
    <view class="info-item flex justify-content align-items">
      <view class="color-grey">{{ $t('系统故障状态') }}</view>
      <view v-if="control['jk_1002']">{{ control['jk_1002'] == '1' ? $t('故障') : $t('正常') }}</view>
      <view v-else>--</view>
    </view>
    <view class="info-item flex justify-content align-items">
      <view class="color-grey">{{ $t('系统告警状态') }}</view>
      <view v-if="control['jk_1005']">{{ control['jk_1005'] == '1' ? $t('告警') : $t('正常') }}</view>
      <view v-else>--</view>
    </view>
    <view class="info-item flex justify-content align-items">
      <view class="color-grey">{{ $t('并离网状态') }}</view>
      <view v-if="control['jk_1001']">{{ parse.get1001Bit14(control['jk_1001']) }}</view>
      <view v-else>--</view>
    </view>
    <view class="info-item flex justify-content align-items">
      <view class="color-grey">{{ $t('AC运行状态') }}</view>
      <view v-if="control['jk_1092']">{{ control['jk_1092'] == "1" ? $t('运行') : $t('关闭') }}
      </view>
      <view v-else>--</view>
    </view>
    <view class="info-item flex justify-content align-items">
      <view class="color-grey">{{ $t('DC运行状态') }}</view>
      <view v-if="control['jk_1093']">{{ control['jk_1093'] == "1" ? $t('运行') : $t('关闭') }}
      </view>
      <view v-else>--</view>
    </view>
    <view class="info-item flex justify-content align-items">
      <view class="color-grey">{{ $t('系统状态') }}</view>
      <view v-if="control['jk_1001']">{{ parse.getWorkState(control['jk_1001']) }}</view>
      <view v-else>--</view>
    </view>
    <view class="info-item flex justify-content align-items">
      <view class="color-grey">{{ $t('运行模式') }}</view>
      <view>{{ get11056Fn(control['jk_1105'], control['jk_1001'])['1105'] }}</view>
    </view>
    <view class="info-item flex justify-content align-items">
      <view class="color-grey">{{ $t('策略状态') }}</view>
      <view>{{ get11056Fn(control['jk_1105'], control['jk_1001'])['1001bit15'] }}</view>
    </view>
    <!-- <view class="info-item flex justify-content align-items">
      <view class="color-grey">{{ $t('电池状态') }}</view>
      <view v-if="control['jk_1004']">{{ control['jk_1004'] == '1' ? $t('故障') :
			          $t('正常') }}</view>
      <view v-else>--</view>
    </view> -->
    <view class="info-item flex justify-content align-items">
      <view class="color-grey">{{ $t('电池高压状态') }}</view>
      <view v-if="control['jk_1094']">{{ control['jk_1094'] == '1' ? $t('上高压') :
			          $t('未上高压') }}</view>
      <view v-else>--</view>
    </view>
    <view class="info-item flex justify-content align-items">
      <view class="color-grey">{{ $t('通讯状态') }}</view>
      <view v-if="control['onLineState'] == '在线'">{{ $t('在线') }}</view>
      <view v-else-if="control['onLineState'] == '离线'">{{ $t('离线') }}</view>
      <view v-else>--</view>
    </view>
    <view class="info-item flex justify-content align-items">
      <view class="color-grey">{{ $t('空调状态') }}</view>
      <view v-if="control['jk_1095']">{{ get1095Fn(control['jk_1095']) }}</view>
      <view v-else>--</view>
    </view>
    <view class="info-item flex justify-content align-items">
      <view class="color-grey">{{ $t('水浸状态') }}</view>
      <view v-if="control['jk_1003']">{{ parse.get1003(control['jk_1003'], 2) }}</view>
      <view v-else>--</view>
    </view>
    <view class="info-item flex justify-content align-items">
      <view class="color-grey">{{ $t('烟感状态') }}</view>
      <view v-if="control['jk_1003']">{{ parse.get1003(control['jk_1003'], 3) }}</view>
      <view v-else>--</view>
    </view>
    <view class="info-item flex justify-content align-items">
      <view class="color-grey">{{ $t('防雷') }}</view>
      <view v-if="control['jk_1003']">{{ parse.get1003(control['jk_1003'], 4) }}</view>
      <view v-else>--</view>
    </view>
    <view class="info-item flex justify-content align-items">
      <view class="color-grey">{{ $t('消防动作') }}</view>
      <view v-if="control['jk_1003']">{{ parse.get1003(control['jk_1003'], 5) }}</view>
      <view v-else>--</view>
    </view>
    <!-- <view class="info-item flex justify-content align-items">
			<view class="color-grey">电网功率</view>
			<view>23<view class="item-unit">kW</view></view>
		</view>
		<view class="info-item flex justify-content align-items">
			<view class="color-grey">负载功率</view>
			<view>23<view class="item-unit">kW</view></view>
		</view>
		<view class="info-item flex justify-content align-items">
			<view class="color-grey">光伏功率</view>
			<view>23<view class="item-unit">kW</view></view>
		</view> -->
    <view class="info-item flex justify-content align-items"v-if="control['jk_1107']">
      <view class="color-grey">{{ $t('采集屏版本') }}</view>
      <view v-if="control['jk_1107']">{{ control['jk_1107'] }}</view>
      <view v-else>--</view>
    </view>
    <view class="info-item flex justify-content align-items">
      <view class="color-grey">{{ $t('软件版本') }}</view>
      <view v-if="control['jk_1000']">{{ control['jk_1000'] }}</view>
      <view v-else>--</view>
    </view>
    <view class="info-item flex justify-content align-items">
      <view class="color-grey">{{ $t('设备地址') }}</view>
      <view>{{ baseInfo.projectAddress }}</view>
    </view>
    <view class="info-item flex justify-content align-items">
      <view class="color-grey">{{ $t('时区地址') }}</view>
      <view>{{ baseInfo[getPropFn()] }}</view>
    </view>
  </view>
</template>

<script setup>
  import {
    computed,
    ref,
    getCurrentInstance
  } from 'vue'
  import {
    useMonitorStore
  } from '@/store/monitor.js'
  import * as parse from '@/common/parseBinaryToText.js'
  import {
    isGroupFn,
    isPhotovoltaicFn,
    isPowerFn,
    isGirdFn,
    getDeviceType
  } from '@/hook/useDeviceType.js'

  const {
    proxy
  } = getCurrentInstance()
  const monitorStore = useMonitorStore()

  const baseInfo = computed(() => monitorStore.baseInfo)
  const control = computed(() => monitorStore.control)
  const get11056Fn = computed(() => {
    return (pt1, pt2) => {
      switch (pt1) {
        case '0':
          return {
            '1001bit15': parse.get1001Bit15(pt2) == uni.$i18n().t('停止') ? uni.$i18n().t('未使用') : parse
              .get1001Bit15(pt2),
              '1105': uni.$i18n().t('手动模式'),
          }
        case '1':
          return {
            '1001bit15': parse.get1001Bit15(pt2),
              '1105': uni.$i18n().t('削峰填谷'),
          }
        case '2':
          return {
            '1001bit15': parse.get1001Bit15(pt2),
              '1105': uni.$i18n().t('后备模式'),
          }
        case '3':
          return {
            '1001bit15': parse.get1001Bit15(pt2),
              '1105': uni.$i18n().t('动态扩容'),
          }
        case '4':
          return {
            '1001bit15': parse.get1001Bit15(pt2),
              '1105': uni.$i18n().t('光伏消纳'),
          }
        default:
          return {
            '1001bit15': '--',
            '1105': '--',
          }
      }
    }
  })
  const get1095Fn = computed(() => {
    return (num) => {
      switch (num) {
        case '0':
          return uni.$i18n().t('关闭')
        case '1':
          return uni.$i18n().t('风机')
        case '2':
          return uni.$i18n().t('制冷')
        case '3':
          return uni.$i18n().t('加热')
        case '4':
          return uni.$i18n().t('除湿')
      }
    }
  })
  const isShowPhotovoltaicInstalledCapacity = computed(() => {
    let type = monitorStore.routeQuery.type
    return isPhotovoltaicFn(type)
  })
  const isShowLoad = computed(() => {
    let type = monitorStore.routeQuery.type
    return isPowerFn(type)
  })
  const isShowGird = computed(() => {
    let type = monitorStore.routeQuery.type
    return isGirdFn(type)
  })
  const getPropFn = () => {
    const lang = uni.cache.getItem('language')
    switch (lang) {
      case 'zh':
        return 'timeZoneAddress'
      case 'en':
        return 'timeZoneAddressUs'
      case 'it':
        return 'timeZoneAddressIt'
      default:
        return 'timeZoneAddress'
    }
  }
</script>

<style scoped lang="scss">
  .data-time {
    display: flex;
    justify-content: space-between;
    background-color: #fff;
    padding: 20rpx 30rpx;
    margin-bottom: 20rpx;
    margin: 20rpx 30rpx;
    font-size: 10px;
    border-radius: 6px;
  }

  .device-info {
    /* background-color: $uni-bg-color; */
    background-color: #fff;
    border-radius: $uni-border-radius-lg;
    padding: 0 30rpx;
    margin: 20rpx 30rpx;

    .info-item {
      padding: 30rpx 0;
      border-bottom: 1px solid $uni-bg-color-grey;

      .item-unit {
        font-size: 12px;
        color: $uni-text-color-grey;
        margin-left: 4rpx;
      }

      view:nth-child(2n) {
        width: 50%;
        text-align: right;
      }
    }

    .info-item:last-child {
      border-bottom: none;
    }
  }
</style>