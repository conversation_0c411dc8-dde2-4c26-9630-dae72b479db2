<template>
  <qiun-data-charts type="line" :opts="opts" :chartData="chartData" :ontouch="true" :optsWatch="false" :onmovetip="true"
    :animation="false" :inScrollView="true" :tooltipCustom="{x:50,y:0}" tooltipFormat="tooltipLineDemo1"
    :reshow="reshow" />
</template>

<script setup>
  import {
    computed,
    ref
  } from 'vue'
  import {
    useMonitorStore
  } from '@/store/monitor.js'
  import {
    isShow3502Bit10
  } from '@/common/parseBinaryToText.js'
  import {
    isPhotovoltaicFn,
    isPowerFn,
    isGirdFn,
    isCellFn,
    isBusFn2
  } from '@/hook/useDeviceType.js'
  import {
    isEmpty
  } from 'lodash'
  import {
    emTypeOptions
  } from '@/constant/index.js'

  // const props = defineProps({
  //   reshow: Boolean
  // })
  const reshow = ref(false)

  const monitorStore = useMonitorStore()

  const opts = ref({
    color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4", "#ea7ccc"],
    padding: [15, 20, 0, 15],
    enableScroll: false,
    dataLabel: false,
    dataPointShape: false,
    legend: {
      show: false
    },
    xAxis: {
      type: 'grid',
      gridType: 'dash',
      // scrollShow: true,//新增是否显示滚动条，默认false
      labelCount: 5,
    },
    yAxis: {
      gridType: "dash",
      dashLength: 2,
      showTitle: true,
      data: [{
        tofix: 2,
        title: 'kW',
        titleOffsetY: -8
      }]
    },
    extra: {
      line: {
        type: "curve",
        width: 2,
        activeType: "hollow"
      },
      tooltip: {
        showArrow: false,
        borderWidth: 0,
        borderRadius: 8,
        borderColor: "#fff",
        bgColor: "#FFFFFF",
        fontColor: "#000000",
        splitLine: false,
        legendShape: 'circle',
      }
    },
    update: true,
  })
  // 是否显示发电机
  const isShowDiesel = computed(() => {
    let sts = monitorStore.pcs_sts
    if (sts.length) return isShow3502Bit10(sts[0]['sts_3502'])
    return false
  })
  // 储能系统
  const isEmTypePCC = computed(() => {
    let data = monitorStore.pcs_ele
    return (monitorStore.routeQuery.type == 7 || monitorStore.routeQuery.type == 6) && data.some(item => {
      return emTypeOptions.findIndex(range => item.dc >= range.min && item.dc <= range.max) !== -1
    })
  })
  const chartData = computed(() => {
    let {
      type,
      id
    } = monitorStore.routeQuery
    let {
      times,
      powers,
      cells,
      loads,
      photovoltaics,
      bus,
      chargingPile,
      soc,
      ac,
      em_10012s: em_10012ss
    } = monitorStore.powerLineData
    let options = {
      categories: JSON.parse(JSON.stringify(!isEmpty(times) ? times : [])),
      series: []
    };
    opts.value.xAxis.itemCount = !isEmpty(times) ? times.length : 0
    // 电网功率
    if (isGirdFn(type)) {
      options.series.push({
        name: isShowDiesel.value ? uni.$t('发电机功率') : uni.$t('电网功率'),
        data: JSON.parse(JSON.stringify(!isEmpty(powers) ? powers : []))
      })
    }
    // 电池功率
    if (isCellFn(type)) {
      options.series.push({
        name: uni.$t('电池功率'),
        data: JSON.parse(JSON.stringify(!isEmpty(cells) ? cells : []))
      })
    }
    // 负载功率
    if (isPowerFn(type) || isEmTypePCC.value) {
      let data = null
      if (isEmTypePCC.value) {
        if (!isEmpty(cells)) data = cells.map((item, index) => {
          let em_10012s = em_10012ss[index]
          return (item - em_10012s).toFixed(2)
        })
      } else {
        data = loads
      }
      options.series.push({
        name: uni.$t('负载功率'),
        data: JSON.parse(JSON.stringify(!isEmpty(data) ? data : []))
      })
    }
    // 光伏功率
    if (isPhotovoltaicFn(type)) {
      options.series.push({
        name: uni.$t('光伏功率'),
        data: JSON.parse(JSON.stringify(!isEmpty(photovoltaics) ? photovoltaics : []))
      })
    }
    // 直流母线功率
    if (isBusFn2(type)) {
      options.series.push({
        name: uni.$t('直流母线功率'),
        data: JSON.parse(JSON.stringify(!isEmpty(bus) ? bus : []))
      })
    }
    // 充电桩、soc
    let chargingPileDc = new Set()
    let socDet = new Set()
    if (chargingPile && chargingPile.length)
      chargingPile.forEach(item => {
        if (item.length) item.forEach(item1 => chargingPileDc.add(item1.dc))
      })
    if (soc && soc.length)
      soc.forEach(item => {
        if (item) Object.keys(item).forEach(item1 => socDet.add(item1))
      })
    if (Array.from(chargingPileDc).length) {
      Array.from(chargingPileDc).forEach((item) => {
        let data = chargingPile.map(item1 => {
          if (item1.length) {
            if (item1.findIndex(item2 => item2.dc == item) !== -1) {
              return item1.find(item2 => item2.dc == item).chargingPile_19003
            } else {
              return null
            }
          }
          return null
        })
        options.series.push({
          name: `${parseInt(item) - 191000 + 1}#${uni.$t('充电桩')}${uni.$t('功率')}`,
          data: JSON.parse(JSON.stringify(!isEmpty(chargingPile) ? data : []))
        })
      })
    }
    if (Array.from(socDet).length) {
      Array.from(socDet).forEach(item => {
        let data = soc.map(item1 => {
          if (item1) {
            if (item1[item] !== null) {
              return item1[item]?.toFixed(2)
            } else {
              return null
            }
          }
          return null
        })
        options.series.push({
          name: item.substring(0, 2) == '16' ? `${parseInt(item) - 161000 + 1}#_SOC` : 'SOC',
          data: JSON.parse(JSON.stringify(!isEmpty(soc) ? data : []))
        })
      })
    }
    // 直流源
    if (type == 13) {
      options.series.push({
        name: uni.$t('直流源功率'),
        data: JSON.parse(JSON.stringify(!isEmpty(cells) ? cells : []))
      })
    }
    if (ac == id) {
      reshow.value = true
      return JSON.parse(JSON.stringify(options))
    }
  })

  defineExpose({
    opts
  })
</script>

<style scoped lang="scss">

</style>