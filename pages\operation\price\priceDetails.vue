<template>
  <view class="device-info">
    <view class="base-info">
      <view class="info-item flex justify-content align-items">
        <view class="bold flex align-items">
          <image src="../../../static/detail.png" class="item-img"></image>
          <span>{{ $t('削峰填谷信息') }}</span>
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('方案名称') }}</view>
        <view class="u-line-1">{{ info.name }}
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('费率') }}</view>
      </view>
      <template v-for="(item, index) in info.rateList" :key="item.num">
        <view class="info-item flex justify-content align-items" style="border-bottom-style: dashed;padding: 20rpx 0;">
          <view>{{ `${$t('费率')}${index + 1}` }}</view>
          <view class="u-line-1">{{ item.value }}
          </view>
        </view>
      </template>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('买卖电是否同价') }}</view>
        <view class="u-line-1">{{ info.buySell == 0 ? $t('是'): $t('否') }}
        </view>
      </view>
      <view v-if="info.buySell == 0">
        <template v-for="(item, index) in info.pointList" :key="item.id">
          <view class="info-item flex justify-content align-items">
            <view>{{ `${$t('时段')}${index + 1}` }}</view>
          </view>
          <view class="info-item flex justify-content align-items"
            style="border-bottom-style: dashed;padding: 20rpx 0;">
            <view>{{ $t('开始时间') }}</view>
            <view class="u-line-1">{{ item.startTime }}
            </view>
          </view>
          <view class="info-item flex justify-content align-items"
            style="border-bottom-style: dashed;padding: 20rpx 0;">
            <view>{{ $t('结束时间') }}</view>
            <view class="u-line-1">{{ item.endTime }}
            </view>
          </view>
          <view class="info-item flex justify-content align-items"
            style="border-bottom-style: dashed;padding: 20rpx 0;">
            <view>{{ $t('费率') }}</view>
            <view class="u-line-1">{{ `${$t('费率')}${info.rateList.findIndex(item1 => item1.num == item.type) + 1}` }}
            </view>
          </view>
          <view class="info-item flex justify-content align-items info-item-dash">
            <view>{{ $t('电价') }}</view>
            <view>{{ item.price }}
            </view>
          </view>
        </template>
      </view>
      <view v-else>
        <view class="info-item flex justify-content align-items">
          <view>{{ $t('买电') }}</view>
        </view>
        <template v-for="(item, index) in info.pointList" :key="item.id">
          <view class="info-item flex justify-content align-items">
            <view>{{ `${$t('时段')}${index + 1}` }}</view>
          </view>
          <view class="info-item flex justify-content align-items"
            style="border-bottom-style: dashed;padding: 20rpx 0;">
            <view>{{ $t('开始时间') }}</view>
            <view class="u-line-1">{{ item.startTime }}
            </view>
          </view>
          <view class="info-item flex justify-content align-items"
            style="border-bottom-style: dashed;padding: 20rpx 0;">
            <view>{{ $t('结束时间') }}</view>
            <view class="u-line-1">{{ item.endTime }}
            </view>
          </view>
          <view class="info-item flex justify-content align-items"
            style="border-bottom-style: dashed;padding: 20rpx 0;">
            <view>{{ $t('费率') }}</view>
            <view class="u-line-1">{{ `${$t('费率')}${info.rateList.findIndex(item1 => item1.num == item.type) + 1}` }}
            </view>
          </view>
          <view class="info-item flex justify-content align-items info-item-dash">
            <view>{{ $t('电价') }}</view>
              <view>{{ item.price }}
            </view>
          </view>
        </template>
        <view class="info-item flex justify-content align-items">
          <view>{{ $t('卖电') }}</view>
        </view>
        <template v-for="(item, index) in info.sellPointList" :key="item.id">
          <view class="info-item flex justify-content align-items">
            <view>{{ `${$t('时段')}${index + 1}` }}</view>
          </view>
          <view class="info-item flex justify-content align-items"
            style="border-bottom-style: dashed;padding: 20rpx 0;">
            <view>{{ $t('开始时间') }}</view>
            <view class="u-line-1">{{ item.startTime }}
            </view>
          </view>
          <view class="info-item flex justify-content align-items"
            style="border-bottom-style: dashed;padding: 20rpx 0;">
            <view>{{ $t('结束时间') }}</view>
            <view class="u-line-1">{{ item.endTime }}
            </view>
          </view>
          <view class="info-item flex justify-content align-items"
            style="border-bottom-style: dashed;padding: 20rpx 0;">
            <view>{{ $t('费率') }}</view>
            <view class="u-line-1">{{ `${$t('费率')}${info.rateList.findIndex(item1 => item1.num == item.type) + 1}` }}
            </view>
          </view>
          <view class="info-item flex justify-content align-items info-item-dash">
            <view>{{ $t('电价') }}</view>
              <view>{{ item.price }}
            </view>
          </view>
        </template>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('创建人员') }}</view>
        <view>{{ info.createBy }}
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('创建时间') }}</view>
        <view>{{ info.createTime }}
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
  import {
    ref,
    computed
  } from 'vue'
  import {
    onLoad,
    onShow
  } from '@dcloudio/uni-app'
  import {
    lookPrice
  } from '@/api/price.js'

  const id = ref()
  onLoad((options) => {
    id.value = options.id
  })
  onShow(() => {
    getInfoFn()
  })

  const info = ref({})
  const getInfoFn = async () => {
    const res = await lookPrice({
      id: id.value
    })
    info.value = res.data.length ? res.data[0] : {}
  }
</script>

<style lang="scss" scoped>
  .device-info {
    height: 100vh;
    overflow: auto;

    .base-info,
    .group-info {
      background-color: $uni-bg-color;
      border-radius: $uni-border-radius-lg;
      padding: 0 30rpx;
      margin: 20rpx 30rpx;
    }

    .info-item {
      padding: 30rpx 0;
      border-bottom: 1px solid $detail-border-color;

      .item-img {
        width: 30rpx;
        height: 30rpx;
        margin-right: 10rpx;
      }

      .item-unit {
        font-size: 12px;
        // color: $uni-text-color-grey;
        margin-left: 4rpx;
      }

      view:nth-child(2n) {
        width: 50%;
        text-align: right;
      }
    }

    .info-item-dash {
      border-bottom-style: dashed;
      border-bottom: 1px dashed $detail-border-color;
    }

    .info-item:last-child {
      border-bottom: none;
    }
  }
</style>