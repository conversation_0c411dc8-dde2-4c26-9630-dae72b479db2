<template>
  <view class="add-box">
    <u-navbar leftText="" :title="title" bgColor="#fff" leftIconSize="20px" :autoBack="true" :placeholder="true">
    </u-navbar>
    <u-form labelPosition="left" :model="form" :rules="rules" ref="formRef" labelWidth="auto">
      <view class="add-item u-p-b-10">
        <u-form-item :label="$t('设备序列号')" prop="ac" required :borderBottom="true">
          <u-input v-model="form.ac" :placeholder="$t('请输入序列号')" border="none"></u-input>
        </u-form-item>
        <u-form-item :label="'ICCID'" prop="iccid" required>
          <u-input v-model="form.iccid" :placeholder="$t('请输入')" border="none" :maxlength="20" :showWordLimit="true"></u-input>
        </u-form-item>
      </view>
    </u-form>
    <u-button type="primary" style="border-radius: 100rpx;" class="u-m-t-40"
      @click="handleConfirmClick">{{ $t('确认') }}</u-button>
  </view>
</template>

<script setup>
  import {
    ref,
    computed,
    nextTick,
    getCurrentInstance
  } from 'vue'
  import {
    onLoad,
    onShow
  } from '@dcloudio/uni-app'
  import {
    addSim
  } from '@/api/sim'

  const {
    proxy
  } = getCurrentInstance()
  const title = ref(uni.$t('添加SIM卡'))

  const formRef = ref(null)
  const form = ref({
    ac: '',
    iccid: ''
  })
  const rules = ref({
    'ac': {
      type: 'string',
      required: true,
      message: uni.$t('请输入序列号'),
      trigger: ['blur', 'change'],
    },
    'iccid': [{
        type: 'string',
        required: true,
        message: uni.$t('请输入ICCID'),
        trigger: ['blur', 'change'],
      },
      {
        validator: (rule, value, callback) => {
          return value.length == 20
        },
        message: uni.$t('长度为20')
      }
    ]
  })

  const handleConfirmClick = () => {
    if (formRef.value) {
      formRef.value.validate().then(valid => {
        if (valid) {
          addSim(form.value).then(res => {
            uni.$u.toast(uni.$t('添加成功'))
            uni.navigateBack()
          }).catch(() => {
            uni.$u.toast(uni.$t('添加失败'))
          })
        }
      })
    }
  }
</script>

<style lang="scss" scoped>
  .add-box {
    width: 100%;
    height: 100vh;
    padding: 20rpx 30rpx;
    overflow: auto;
  }

  .add-item {
    background-color: #fff;
    padding: 0 30rpx;
    border-radius: 6px;
  }

  .btn {
    height: 50rpx;
    background: rgba(0, 0, 0, 0);
  }

  :deep(.u-input__content__field-wrapper__field) {
    text-align: right !important;
  }

  :deep(.u-button__text) {
    font-size: 12px !important;
  }

  :deep(.u-datetime-picker) {
    .u-input {
      padding: 0 !important;
      border: none;
    }
  }

  :deep(.rate) {
    .u-form-item__body__right__content__slot {
      flex-direction: row-reverse !important;
    }
  }

  :deep(.u-form-item__body__right__content__slot) {
    flex-direction: row-reverse;
  }
</style>